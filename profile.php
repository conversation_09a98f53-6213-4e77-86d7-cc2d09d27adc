<?php
session_start();
require_once 'config/database.php';
require_once 'includes/functions.php';
require_once 'includes/language.php';

// Определение языка
$lang = detectLanguage();
loadLanguage($lang);

// Проверка авторизации
if (!isset($_SESSION['user_id'])) {
    redirect('login.php');
}

$user = getUserById($_SESSION['user_id']);
if (!$user) {
    session_destroy();
    redirect('login.php');
}

$errors = [];
$success = '';

// Обработка обновления профиля
if ($_POST && isset($_POST['update_profile'])) {
    $first_name = trim($_POST['first_name'] ?? '');
    $last_name = trim($_POST['last_name'] ?? '');
    $phone = trim($_POST['phone'] ?? '');
    
    // Валидация
    if (empty($first_name)) {
        $errors[] = t('first_name') . ' ' . t('required');
    }
    
    if (empty($last_name)) {
        $errors[] = t('last_name') . ' ' . t('required');
    }
    
    if (empty($errors)) {
        $db = getDB();
        
        try {
            $stmt = $db->prepare("
                UPDATE users 
                SET first_name = ?, last_name = ?, phone = ?, updated_at = NOW() 
                WHERE id = ?
            ");
            
            if ($stmt->execute([$first_name, $last_name, $phone, $_SESSION['user_id']])) {
                $success = 'Profile updated successfully!';
                $user = getUserById($_SESSION['user_id']); // Обновляем данные пользователя
                logAction($_SESSION['user_id'], 'profile_updated', 'User profile updated');
            } else {
                $errors[] = 'Failed to update profile. Please try again.';
            }
        } catch (Exception $e) {
            $errors[] = 'Database error occurred. Please try again.';
            logAction($_SESSION['user_id'], 'profile_update_failed', $e->getMessage());
        }
    }
}

// Обработка смены пароля
if ($_POST && isset($_POST['change_password'])) {
    $current_password = $_POST['current_password'] ?? '';
    $new_password = $_POST['new_password'] ?? '';
    $confirm_password = $_POST['confirm_password'] ?? '';
    
    // Валидация
    if (empty($current_password)) {
        $errors[] = 'Current password is required';
    }
    
    if (empty($new_password)) {
        $errors[] = 'New password is required';
    }
    
    if (strlen($new_password) < 8) {
        $errors[] = t('password_too_short');
    }
    
    if ($new_password !== $confirm_password) {
        $errors[] = t('passwords_dont_match');
    }
    
    // Проверка текущего пароля
    if (!empty($current_password) && !verifyPassword($current_password, $user['password'])) {
        $errors[] = 'Current password is incorrect';
    }
    
    if (empty($errors)) {
        $db = getDB();
        
        try {
            $stmt = $db->prepare("
                UPDATE users 
                SET password = ?, updated_at = NOW() 
                WHERE id = ?
            ");
            
            if ($stmt->execute([hashPassword($new_password), $_SESSION['user_id']])) {
                $success = 'Password changed successfully!';
                logAction($_SESSION['user_id'], 'password_changed', 'User password changed');
            } else {
                $errors[] = 'Failed to change password. Please try again.';
            }
        } catch (Exception $e) {
            $errors[] = 'Database error occurred. Please try again.';
            logAction($_SESSION['user_id'], 'password_change_failed', $e->getMessage());
        }
    }
}

// Получение статистики пользователя
$db = getDB();

// Общая статистика
$stmt = $db->prepare("
    SELECT 
        COUNT(*) as total_investments,
        COALESCE(SUM(amount), 0) as total_invested
    FROM investments 
    WHERE user_id = ?
");
$stmt->execute([$_SESSION['user_id']]);
$investment_stats = $stmt->fetch();

// Статистика транзакций
$stmt = $db->prepare("
    SELECT 
        COUNT(*) as total_transactions,
        COALESCE(SUM(CASE WHEN type = 'profit' THEN amount ELSE 0 END), 0) as total_profits
    FROM transactions 
    WHERE user_id = ?
");
$stmt->execute([$_SESSION['user_id']]);
$transaction_stats = $stmt->fetch();

// Реферальная статистика
$stmt = $db->prepare("
    SELECT 
        COUNT(*) as total_referrals,
        COALESCE(SUM(commission_amount), 0) as total_referral_earnings
    FROM referral_commissions 
    WHERE referrer_id = ?
");
$stmt->execute([$_SESSION['user_id']]);
$referral_stats = $stmt->fetch();

$pageTitle = t('profile') . ' - Astragenix';
$pageDescription = t('profile_description');
$currentPage = 'profile';
$bodyClass = 'profile-page';
$additionalCSS = ['assets/css/profile.css'];
include 'includes/header.php';
?>

<!-- Profile Section -->
<section class="profile-section">
    <div class="container">
        <!-- Page Header -->
        <div class="page-header">
            <div class="page-header-content">
                <h1 class="page-title"><?php echo t('profile'); ?></h1>
                <p class="page-subtitle"><?php echo t('profile_description'); ?></p>
                <div class="breadcrumb">
                    <a href="dashboard.php"><?php echo t('dashboard'); ?></a>
                    <span class="breadcrumb-separator">/</span>
                    <span class="breadcrumb-current"><?php echo t('profile'); ?></span>
                </div>
            </div>
        </div>

        <!-- Messages -->
        <?php if (!empty($errors)): ?>
            <div class="error-messages">
                <ul>
                    <?php foreach ($errors as $error): ?>
                        <li><?php echo h($error); ?></li>
                    <?php endforeach; ?>
                </ul>
            </div>
        <?php endif; ?>

        <?php if (!empty($success)): ?>
            <div class="success-message">
                <?php echo h($success); ?>
            </div>
        <?php endif; ?>

        <div class="profile-content">
            <!-- Profile Overview -->
            <div class="profile-overview">
                <div class="profile-card">
                    <div class="profile-avatar">
                        <img src="<?php echo $user['avatar'] ?? 'assets/images/default-avatar.png'; ?>" 
                             alt="<?php echo h($user['first_name']); ?>">
                        <div class="avatar-badge">
                            <i class="fas fa-crown"></i>
                        </div>
                    </div>
                    <div class="profile-info">
                        <h2><?php echo h($user['first_name'] . ' ' . $user['last_name']); ?></h2>
                        <p class="profile-email"><?php echo h($user['email']); ?></p>
                        <div class="profile-meta">
                            <span class="meta-item">
                                <i class="fas fa-calendar"></i>
                                <?php echo t('joined'); ?>: <?php echo date('M Y', strtotime($user['created_at'])); ?>
                            </span>
                            <span class="meta-item">
                                <i class="fas fa-shield-alt"></i>
                                <?php echo t('verified'); ?>
                            </span>
                        </div>
                    </div>
                </div>

                <!-- Stats Grid -->
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <div class="stat-info">
                            <h3><?php echo $investment_stats['total_investments']; ?></h3>
                            <p><?php echo t('total_investments'); ?></p>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-dollar-sign"></i>
                        </div>
                        <div class="stat-info">
                            <h3><?php echo formatCurrency($investment_stats['total_invested']); ?></h3>
                            <p><?php echo t('total_invested'); ?></p>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-coins"></i>
                        </div>
                        <div class="stat-info">
                            <h3><?php echo formatCurrency($transaction_stats['total_profits']); ?></h3>
                            <p><?php echo t('total_profits'); ?></p>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="stat-info">
                            <h3><?php echo $referral_stats['total_referrals']; ?></h3>
                            <p><?php echo t('referrals'); ?></p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Profile Forms -->
            <div class="profile-forms">
                <!-- Personal Information -->
                <div class="form-section">
                    <div class="form-card">
                        <div class="form-header">
                            <h3><?php echo t('personal_information'); ?></h3>
                            <p><?php echo t('update_personal_info'); ?></p>
                        </div>
                        
                        <form method="POST" class="profile-form">
                            <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                            <input type="hidden" name="update_profile" value="1">
                            
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="first_name"><?php echo t('first_name'); ?></label>
                                    <input type="text" 
                                           id="first_name" 
                                           name="first_name" 
                                           class="form-input" 
                                           value="<?php echo h($user['first_name']); ?>"
                                           required>
                                </div>
                                
                                <div class="form-group">
                                    <label for="last_name"><?php echo t('last_name'); ?></label>
                                    <input type="text" 
                                           id="last_name" 
                                           name="last_name" 
                                           class="form-input" 
                                           value="<?php echo h($user['last_name']); ?>"
                                           required>
                                </div>
                            </div>
                            
                            <div class="form-group">
                                <label for="email"><?php echo t('email_address'); ?></label>
                                <input type="email" 
                                       id="email" 
                                       class="form-input" 
                                       value="<?php echo h($user['email']); ?>"
                                       disabled>
                                <div class="input-help">
                                    <?php echo t('email_cannot_be_changed'); ?>
                                </div>
                            </div>
                            
                            <div class="form-group">
                                <label for="phone"><?php echo t('phone_number'); ?></label>
                                <input type="tel" 
                                       id="phone" 
                                       name="phone" 
                                       class="form-input" 
                                       value="<?php echo h($user['phone'] ?? ''); ?>">
                            </div>
                            
                            <div class="form-actions">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save"></i>
                                    <?php echo t('save_changes'); ?>
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Change Password -->
                <div class="form-section">
                    <div class="form-card">
                        <div class="form-header">
                            <h3><?php echo t('change_password'); ?></h3>
                            <p><?php echo t('update_password_security'); ?></p>
                        </div>
                        
                        <form method="POST" class="password-form">
                            <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                            <input type="hidden" name="change_password" value="1">
                            
                            <div class="form-group">
                                <label for="current_password"><?php echo t('current_password'); ?></label>
                                <div class="password-input-container">
                                    <input type="password" 
                                           id="current_password" 
                                           name="current_password" 
                                           class="form-input" 
                                           required>
                                    <button type="button" class="password-toggle">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </div>
                            </div>
                            
                            <div class="form-group">
                                <label for="new_password"><?php echo t('new_password'); ?></label>
                                <div class="password-input-container">
                                    <input type="password" 
                                           id="new_password" 
                                           name="new_password" 
                                           class="form-input" 
                                           minlength="8"
                                           required>
                                    <button type="button" class="password-toggle">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </div>
                                <div class="input-help">
                                    <?php echo t('password_requirements'); ?>
                                </div>
                            </div>
                            
                            <div class="form-group">
                                <label for="confirm_password"><?php echo t('confirm_password'); ?></label>
                                <div class="password-input-container">
                                    <input type="password" 
                                           id="confirm_password" 
                                           name="confirm_password" 
                                           class="form-input" 
                                           required>
                                    <button type="button" class="password-toggle">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </div>
                            </div>
                            
                            <div class="form-actions">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-lock"></i>
                                    <?php echo t('change_password'); ?>
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<?php include 'includes/footer.php'; ?>
