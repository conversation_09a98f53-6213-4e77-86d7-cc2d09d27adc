<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type, X-Requested-With');

session_start();
require_once '../../config/database.php';
require_once '../../includes/functions.php';

// Проверка авторизации и прав администратора
if (!isset($_SESSION['user_id']) || !isAdmin($_SESSION['user_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Unauthorized']);
    exit;
}

// Проверка метода запроса
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

// Получение данных запроса
$input = json_decode(file_get_contents('php://input'), true);

if (!$input || !isset($input['transaction_id']) || !isset($input['action'])) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Invalid request data']);
    exit;
}

$transactionId = (int)$input['transaction_id'];
$action = $input['action']; // 'approve' or 'reject'

if (!in_array($action, ['approve', 'reject'])) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Invalid action']);
    exit;
}

try {
    $db = getDB();
    
    // Получаем информацию о транзакции
    $stmt = $db->prepare("
        SELECT t.*, u.first_name, u.last_name, u.email 
        FROM transactions t 
        JOIN users u ON t.user_id = u.id 
        WHERE t.id = ? AND t.status = 'pending'
    ");
    $stmt->execute([$transactionId]);
    $transaction = $stmt->fetch();
    
    if (!$transaction) {
        echo json_encode(['success' => false, 'message' => 'Transaction not found or already processed']);
        exit;
    }
    
    // Начинаем транзакцию БД
    $db->beginTransaction();
    
    if ($action === 'approve') {
        // Одобряем транзакцию
        $newStatus = 'completed';
        $message = 'Transaction approved successfully';
        
        // Если это депозит, создаем инвестицию
        if ($transaction['type'] === 'deposit') {
            // Здесь можно добавить логику создания инвестиции
            // на основе суммы депозита
        }
        
        // Если это вывод, помечаем как обработанный
        if ($transaction['type'] === 'withdrawal') {
            // Здесь можно добавить интеграцию с платежной системой
        }
        
    } else {
        // Отклоняем транзакцию
        $newStatus = 'rejected';
        $message = 'Transaction rejected';
        
        // Если это был вывод, возвращаем средства на баланс
        if ($transaction['type'] === 'withdrawal') {
            // Создаем возвратную транзакцию
            $stmt = $db->prepare("
                INSERT INTO transactions (user_id, type, amount, description, status, created_at)
                VALUES (?, 'bonus', ?, 'Refund for rejected withdrawal', 'completed', NOW())
            ");
            $stmt->execute([$transaction['user_id'], $transaction['amount']]);
        }
    }
    
    // Обновляем статус транзакции
    $stmt = $db->prepare("
        UPDATE transactions 
        SET status = ?, processed_by = ?, processed_at = NOW(), 
            admin_notes = CONCAT(COALESCE(admin_notes, ''), ?, '\n')
        WHERE id = ?
    ");
    $stmt->execute([
        $newStatus, 
        $_SESSION['user_id'], 
        "Transaction {$action}d by admin on " . date('Y-m-d H:i:s'),
        $transactionId
    ]);
    
    // Логируем действие администратора
    logAction($_SESSION['user_id'], 'admin_transaction_' . $action, 
              "Transaction ID: {$transactionId}, User: {$transaction['email']}, Amount: {$transaction['amount']}");
    
    // Подтверждаем транзакцию БД
    $db->commit();
    
    // Отправляем уведомление пользователю (опционально)
    // sendTransactionNotification($transaction, $action);
    
    echo json_encode([
        'success' => true, 
        'message' => $message,
        'transaction_id' => $transactionId,
        'new_status' => $newStatus
    ]);
    
} catch (Exception $e) {
    // Откатываем транзакцию БД в случае ошибки
    if ($db->inTransaction()) {
        $db->rollback();
    }
    
    error_log("Error handling transaction: " . $e->getMessage());
    
    http_response_code(500);
    echo json_encode([
        'success' => false, 
        'message' => 'Internal server error'
    ]);
}

// Функция для отправки уведомлений (заглушка)
function sendTransactionNotification($transaction, $action) {
    // Здесь можно добавить отправку email или push-уведомлений
    $subject = "Transaction " . ucfirst($action) . "d";
    $message = "Your {$transaction['type']} transaction for {$transaction['amount']} USDT has been {$action}d.";
    
    // sendEmail($transaction['email'], $subject, $message);
}
?>
