# Astragenix - Advanced Crypto Investment Platform

Astragenix is a modern, feature-rich cryptocurrency investment platform built with PHP, MySQL, and modern web technologies. It provides a comprehensive solution for managing crypto investments, user accounts, referral programs, and administrative operations.

## 🚀 Features

### User Features
- **User Registration & Authentication** - Secure signup/login with email verification
- **Investment Plans** - Multiple investment plans with different rates and durations
- **Real-time Dashboard** - Comprehensive dashboard with investment tracking
- **Wallet Management** - Deposit and withdrawal functionality
- **Referral Program** - Multi-level referral system with commissions
- **Transaction History** - Complete transaction tracking and filtering
- **Profile Management** - User profile and security settings
- **Multi-language Support** - English and Russian language support

### Admin Features
- **Admin Dashboard** - Comprehensive admin panel with statistics
- **User Management** - Complete user account management
- **Investment Management** - Monitor and manage all investments
- **Transaction Management** - Approve/reject deposits and withdrawals
- **Referral Tracking** - Monitor referral activities and commissions
- **System Settings** - Configure platform settings and parameters
- **Bulk Operations** - Perform bulk actions on users and transactions

### Technical Features
- **Modern UI/UX** - Responsive design with modern CSS and animations
- **Security** - CSRF protection, password hashing, input validation
- **Database** - Well-structured MySQL database with proper relationships
- **AJAX** - Dynamic content loading without page refreshes
- **Mobile Responsive** - Optimized for all device sizes
- **Performance** - Optimized queries and efficient code structure

## 📋 Requirements

- **PHP** 7.4 or higher
- **MySQL** 5.7 or higher
- **Web Server** Apache or Nginx
- **Extensions**: PDO, PDO_MySQL, OpenSSL, JSON, MBString

## 🛠️ Installation

### 1. Database Setup
```sql
-- Create database
CREATE DATABASE astragenix CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Import the main database structure
mysql -u your_username -p astragenix < database/astragenix.sql

-- Import additional tables
mysql -u your_username -p astragenix < database/additional_tables.sql
```

### 2. Configuration
```php
// Edit config/database.php with your database credentials
$host = 'localhost';
$dbname = 'astragenix';
$username = 'your_db_username';
$password = 'your_db_password';
```

### 3. Admin Account
Create the first admin account by registering normally, then update the database:
```sql
UPDATE users SET role = 'admin' WHERE email = '<EMAIL>';
```

## 📁 Project Structure

```
astragenix/
├── admin/                  # Admin panel files
│   ├── index.php          # Admin dashboard
│   ├── users.php          # User management
│   └── ajax/              # Admin AJAX handlers
├── assets/                # Static assets
│   ├── css/               # Stylesheets
│   ├── js/                # JavaScript files
│   └── images/            # Images and icons
├── config/                # Configuration files
├── database/              # Database files
├── includes/              # PHP includes
├── languages/             # Language files
├── index.php              # Homepage
├── dashboard.php          # User dashboard
├── invest.php             # Investment page
├── withdraw.php           # Withdrawal page
├── profile.php            # User profile
├── history.php            # Transaction history
├── referrals.php          # Referral program
├── login.php              # Login page
└── register.php           # Registration page
```

## 🔒 Security Features

- **CSRF Protection** - All forms include CSRF tokens
- **Password Hashing** - Secure password storage with PHP's password_hash()
- **Input Validation** - Comprehensive input sanitization
- **SQL Injection Prevention** - Prepared statements throughout
- **XSS Protection** - Output escaping and content security
- **Rate Limiting** - Protection against brute force attacks

## 📱 Mobile Responsiveness

The platform is fully responsive and optimized for:
- Desktop computers
- Tablets
- Mobile phones
- Various screen sizes and orientations

## 🚀 Performance Optimization

- **Optimized Queries** - Efficient database queries with proper indexing
- **Lazy Loading** - Images and content loaded as needed
- **Minified Assets** - Compressed CSS and JavaScript
- **Caching** - Browser caching for static assets
- **GZIP Compression** - Server-side compression

## 🐛 Troubleshooting

### Common Issues

1. **Database Connection Error**
   - Check database credentials in `config/database.php`
   - Ensure MySQL service is running
   - Verify database exists and user has proper permissions

2. **Permission Denied Errors**
   - Check file permissions (755 for directories, 644 for files)
   - Ensure web server has read access to all files

3. **CSS/JS Not Loading**
   - Check file paths in HTML
   - Verify web server configuration
   - Clear browser cache

## 📊 Key Pages

### User Pages
- **Homepage** (`index.php`) - Landing page with platform overview
- **Dashboard** (`dashboard.php`) - User dashboard with statistics
- **Investment** (`invest.php`) - Investment plans and calculator
- **Withdrawal** (`withdraw.php`) - Withdrawal request form
- **Profile** (`profile.php`) - User profile management
- **History** (`history.php`) - Transaction history with filters
- **Referrals** (`referrals.php`) - Referral program and statistics

### Admin Pages
- **Admin Dashboard** (`admin/index.php`) - Admin overview and statistics
- **User Management** (`admin/users.php`) - Manage user accounts
- **Transaction Management** (`admin/transactions.php`) - Handle transactions
- **Investment Management** (`admin/investments.php`) - Monitor investments
- **Settings** (`admin/settings.php`) - Platform configuration

## 🎨 Customization

### Styling
- Edit `assets/css/modern.css` for global styles
- Page-specific styles are in separate CSS files
- CSS variables are used for easy theming

### Languages
- Add new language files in `languages/` directory
- Follow the existing format in `en.php` and `ru.php`
- Update `includes/language.php` to support new languages

### Investment Plans
- Modify plans in the admin panel
- Customize plan features and descriptions
- Add new plan types as needed

## 🔄 Recent Updates

This version includes:
- Complete user dashboard with investment tracking
- Investment page with plan selection and calculator
- Withdrawal system with balance management
- User profile management with security features
- Transaction history with advanced filtering
- Referral program with multi-level commissions
- Comprehensive admin panel with user management
- Mobile-responsive design throughout
- Enhanced security features
- Multi-language support (English/Russian)

---

**Astragenix** - Building the future of crypto investment platforms.
