<?php
if (!isset($pageTitle)) $pageTitle = 'Astragenix - Advanced Crypto Investment Platform';
if (!isset($pageDescription)) $pageDescription = 'Join the future of cryptocurrency investment with Astragenix. Professional trading algorithms, guaranteed daily returns, and secure investment opportunities.';
if (!isset($currentPage)) $currentPage = '';
?>
<!DOCTYPE html>
<html lang="<?php echo $lang ?? 'en'; ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo h($pageTitle); ?></title>
    <meta name="description" content="<?php echo h($pageDescription); ?>">
    <meta name="keywords" content="cryptocurrency, investment, trading, blockchain, USDT, profit, daily returns">
    <meta name="author" content="Astragenix">
    
    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:url" content="<?php echo SITE_URL; ?>">
    <meta property="og:title" content="<?php echo h($pageTitle); ?>">
    <meta property="og:description" content="<?php echo h($pageDescription); ?>">
    <meta property="og:image" content="<?php echo SITE_URL; ?>/assets/images/og-image.jpg">

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image">
    <meta property="twitter:url" content="<?php echo SITE_URL; ?>">
    <meta property="twitter:title" content="<?php echo h($pageTitle); ?>">
    <meta property="twitter:description" content="<?php echo h($pageDescription); ?>">
    <meta property="twitter:image" content="<?php echo SITE_URL; ?>/assets/images/og-image.jpg">

    <!-- Favicon -->
    <link rel="icon" type="image/png" sizes="32x32" href="assets/images/favicon.png">
    <link rel="icon" type="image/png" sizes="16x16" href="assets/images/favicon-16x16.png">
    <link rel="apple-touch-icon" sizes="180x180" href="assets/images/apple-touch-icon.png">

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    
    <!-- Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Styles -->
    <link rel="stylesheet" href="assets/css/modern.css">
    <link rel="stylesheet" href="assets/css/components.css">
    <link rel="stylesheet" href="assets/css/animations.css">
    <?php if (isset($additionalCSS)): ?>
        <?php foreach ($additionalCSS as $css): ?>
            <link rel="stylesheet" href="<?php echo $css; ?>">
        <?php endforeach; ?>
    <?php endif; ?>
    
    <!-- Preload critical resources -->
    <link rel="preload" href="assets/css/modern.css" as="style">
    <link rel="preload" href="assets/js/modern.js" as="script">
</head>
<body class="<?php echo $bodyClass ?? ''; ?>">
    <!-- Loading Screen -->
    <div id="loading-screen" class="loading-screen">
        <div class="loading-content">
            <div class="loading-logo">
                <img src="assets/images/logo.png" alt="Astragenix" class="logo-img">
                <div class="loading-text">Astragenix</div>
            </div>
            <div class="loading-spinner">
                <div class="spinner"></div>
            </div>
        </div>
    </div>

    <!-- Navigation -->
    <nav class="navbar" id="navbar">
        <div class="nav-container">
            <div class="nav-brand">
                <a href="index.php" class="brand-link">
                    <img src="assets/images/logo.png" alt="Astragenix" class="brand-logo">
                    <span class="brand-text">Astragenix</span>
                </a>
            </div>
            
            <div class="nav-menu" id="nav-menu">
                <a href="index.php" class="nav-link <?php echo $currentPage === 'home' ? 'active' : ''; ?>">
                    <i class="fas fa-home"></i>
                    <span><?php echo t('home'); ?></span>
                </a>
                <a href="about.php" class="nav-link <?php echo $currentPage === 'about' ? 'active' : ''; ?>">
                    <i class="fas fa-info-circle"></i>
                    <span><?php echo t('about'); ?></span>
                </a>
                <a href="investment-plans.php" class="nav-link <?php echo $currentPage === 'plans' ? 'active' : ''; ?>">
                    <i class="fas fa-chart-line"></i>
                    <span><?php echo t('investment_plans'); ?></span>
                </a>
                <a href="faq.php" class="nav-link <?php echo $currentPage === 'faq' ? 'active' : ''; ?>">
                    <i class="fas fa-question-circle"></i>
                    <span><?php echo t('faq'); ?></span>
                </a>
                <a href="contact.php" class="nav-link <?php echo $currentPage === 'contact' ? 'active' : ''; ?>">
                    <i class="fas fa-envelope"></i>
                    <span><?php echo t('contact'); ?></span>
                </a>
                
                <?php if (isset($_SESSION['user_id'])): ?>
                    <div class="nav-divider"></div>
                    <a href="dashboard.php" class="nav-link dashboard-link">
                        <i class="fas fa-tachometer-alt"></i>
                        <span><?php echo t('dashboard'); ?></span>
                    </a>
                    <div class="nav-user">
                        <div class="user-avatar">
                            <img src="<?php echo $user['avatar'] ?? 'assets/images/default-avatar.png'; ?>" 
                                 alt="<?php echo h($user['first_name'] ?? 'User'); ?>">
                        </div>
                        <div class="user-menu">
                            <a href="dashboard.php" class="user-menu-item">
                                <i class="fas fa-tachometer-alt"></i>
                                <?php echo t('dashboard'); ?>
                            </a>
                            <a href="invest.php" class="user-menu-item">
                                <i class="fas fa-chart-line"></i>
                                <?php echo t('make_investment'); ?>
                            </a>
                            <a href="withdraw.php" class="user-menu-item">
                                <i class="fas fa-money-bill-wave"></i>
                                <?php echo t('withdraw_funds'); ?>
                            </a>
                            <a href="history.php" class="user-menu-item">
                                <i class="fas fa-history"></i>
                                <?php echo t('transaction_history'); ?>
                            </a>
                            <a href="referrals.php" class="user-menu-item">
                                <i class="fas fa-users"></i>
                                <?php echo t('referral_program'); ?>
                            </a>
                            <a href="profile.php" class="user-menu-item">
                                <i class="fas fa-user"></i>
                                <?php echo t('profile'); ?>
                            </a>
                            <div class="menu-divider"></div>
                            <a href="logout.php" class="user-menu-item logout">
                                <i class="fas fa-sign-out-alt"></i>
                                <?php echo t('logout'); ?>
                            </a>
                        </div>
                    </div>
                <?php else: ?>
                    <div class="nav-divider"></div>
                    <a href="login.php" class="nav-link login-link">
                        <i class="fas fa-sign-in-alt"></i>
                        <span><?php echo t('login'); ?></span>
                    </a>
                    <a href="register.php" class="btn btn-primary nav-cta">
                        <i class="fas fa-rocket"></i>
                        <span><?php echo t('get_started'); ?></span>
                    </a>
                <?php endif; ?>
            </div>
            
            <!-- Language Switcher -->
            <div class="language-switcher">
                <button class="lang-toggle" id="lang-toggle">
                    <i class="fas fa-globe"></i>
                    <span><?php echo strtoupper($lang ?? 'EN'); ?></span>
                    <i class="fas fa-chevron-down"></i>
                </button>
                <div class="lang-dropdown" id="lang-dropdown">
                    <a href="?lang=en" class="lang-option <?php echo ($lang ?? 'en') === 'en' ? 'active' : ''; ?>">
                        <img src="assets/images/flags/en.svg" alt="English" class="flag-icon">
                        <span>English</span>
                    </a>
                    <a href="?lang=ru" class="lang-option <?php echo ($lang ?? 'en') === 'ru' ? 'active' : ''; ?>">
                        <img src="assets/images/flags/ru.svg" alt="Русский" class="flag-icon">
                        <span>Русский</span>
                    </a>
                </div>
            </div>
            
            <!-- Mobile Menu Toggle -->
            <button class="nav-toggle" id="nav-toggle">
                <span class="hamburger-line"></span>
                <span class="hamburger-line"></span>
                <span class="hamburger-line"></span>
            </button>
        </div>
    </nav>

    <!-- Mobile Menu Overlay -->
    <div class="mobile-overlay" id="mobile-overlay"></div>

    <!-- Main Content -->
    <main class="main-content" id="main-content">
