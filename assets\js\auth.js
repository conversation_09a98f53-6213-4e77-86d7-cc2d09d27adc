// Astragenix Authentication JavaScript

document.addEventListener('DOMContentLoaded', function() {
    initPasswordToggle();
    initFormEnhancements();
    initSocialLogin();
    initPasswordStrength();
    
    console.log('Auth module initialized');
});

// Password visibility toggle
function initPasswordToggle() {
    const toggleButtons = document.querySelectorAll('.password-toggle');
    
    toggleButtons.forEach(button => {
        button.addEventListener('click', function() {
            const input = this.parentNode.querySelector('input[type="password"], input[type="text"]');
            const icon = this.querySelector('i');
            
            if (input.type === 'password') {
                input.type = 'text';
                icon.classList.remove('fa-eye');
                icon.classList.add('fa-eye-slash');
            } else {
                input.type = 'password';
                icon.classList.remove('fa-eye-slash');
                icon.classList.add('fa-eye');
            }
        });
    });
}

// Global function for password toggle (called from HTML)
function togglePassword(inputId) {
    const input = document.getElementById(inputId);
    const button = input.parentNode.querySelector('.password-toggle');
    const icon = button.querySelector('i');
    
    if (input.type === 'password') {
        input.type = 'text';
        icon.classList.remove('fa-eye');
        icon.classList.add('fa-eye-slash');
    } else {
        input.type = 'password';
        icon.classList.remove('fa-eye-slash');
        icon.classList.add('fa-eye');
    }
}

// Form enhancements
function initFormEnhancements() {
    const forms = document.querySelectorAll('.auth-form');
    
    forms.forEach(form => {
        // Add loading state to submit button
        form.addEventListener('submit', function() {
            const submitButton = this.querySelector('button[type="submit"]');
            if (submitButton) {
                submitButton.classList.add('loading');
                submitButton.disabled = true;
                
                // Re-enable button after 5 seconds as fallback
                setTimeout(() => {
                    submitButton.classList.remove('loading');
                    submitButton.disabled = false;
                }, 5000);
            }
        });
        
        // Enhanced input animations
        const inputs = form.querySelectorAll('input, select, textarea');
        inputs.forEach(input => {
            // Add focus animations
            input.addEventListener('focus', function() {
                this.parentNode.classList.add('focused');
            });
            
            input.addEventListener('blur', function() {
                this.parentNode.classList.remove('focused');
                
                // Add filled class if input has value
                if (this.value.trim()) {
                    this.parentNode.classList.add('filled');
                } else {
                    this.parentNode.classList.remove('filled');
                }
            });
            
            // Check initial state
            if (input.value.trim()) {
                input.parentNode.classList.add('filled');
            }
        });
    });
    
    // Auto-focus first input
    const firstInput = document.querySelector('.auth-form input:not([type="hidden"])');
    if (firstInput && !firstInput.hasAttribute('autofocus')) {
        setTimeout(() => {
            firstInput.focus();
        }, 500);
    }
}

// Social login handlers
function initSocialLogin() {
    const socialButtons = document.querySelectorAll('.btn-social');
    
    socialButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            
            const provider = this.textContent.toLowerCase().includes('google') ? 'google' : 'unknown';
            
            // Add loading state
            this.classList.add('loading');
            
            // Simulate social login process
            setTimeout(() => {
                this.classList.remove('loading');
                showNotification('Social login coming soon!', 'info');
            }, 2000);
        });
    });
}

// Password strength indicator
function initPasswordStrength() {
    const passwordInputs = document.querySelectorAll('input[type="password"][name="password"]');
    
    passwordInputs.forEach(input => {
        // Create strength indicator
        const strengthIndicator = document.createElement('div');
        strengthIndicator.className = 'password-strength';
        strengthIndicator.innerHTML = `
            <div class="strength-bar">
                <div class="strength-fill"></div>
            </div>
            <div class="strength-text"></div>
        `;
        
        input.parentNode.appendChild(strengthIndicator);
        
        // Add styles
        const style = document.createElement('style');
        style.textContent = `
            .password-strength {
                margin-top: 0.5rem;
            }
            
            .strength-bar {
                height: 4px;
                background: rgba(255, 255, 255, 0.1);
                border-radius: 2px;
                overflow: hidden;
                margin-bottom: 0.25rem;
            }
            
            .strength-fill {
                height: 100%;
                width: 0%;
                transition: width 0.3s ease, background-color 0.3s ease;
                border-radius: 2px;
            }
            
            .strength-text {
                font-size: 0.75rem;
                color: var(--text-muted);
            }
            
            .strength-weak .strength-fill {
                width: 25%;
                background: #ef4444;
            }
            
            .strength-fair .strength-fill {
                width: 50%;
                background: #f59e0b;
            }
            
            .strength-good .strength-fill {
                width: 75%;
                background: #3b82f6;
            }
            
            .strength-strong .strength-fill {
                width: 100%;
                background: #10b981;
            }
        `;
        document.head.appendChild(style);
        
        // Update strength on input
        input.addEventListener('input', function() {
            updatePasswordStrength(this, strengthIndicator);
        });
    });
}

function updatePasswordStrength(input, indicator) {
    const password = input.value;
    const strength = calculatePasswordStrength(password);
    
    const strengthBar = indicator.querySelector('.strength-bar');
    const strengthText = indicator.querySelector('.strength-text');
    
    // Remove all strength classes
    strengthBar.classList.remove('strength-weak', 'strength-fair', 'strength-good', 'strength-strong');
    
    if (password.length === 0) {
        strengthText.textContent = '';
        return;
    }
    
    switch (strength.level) {
        case 1:
            strengthBar.classList.add('strength-weak');
            strengthText.textContent = 'Weak password';
            break;
        case 2:
            strengthBar.classList.add('strength-fair');
            strengthText.textContent = 'Fair password';
            break;
        case 3:
            strengthBar.classList.add('strength-good');
            strengthText.textContent = 'Good password';
            break;
        case 4:
            strengthBar.classList.add('strength-strong');
            strengthText.textContent = 'Strong password';
            break;
    }
    
    // Show requirements
    if (strength.level < 3) {
        const missing = [];
        if (!strength.hasLength) missing.push('8+ characters');
        if (!strength.hasUpper) missing.push('uppercase letter');
        if (!strength.hasLower) missing.push('lowercase letter');
        if (!strength.hasNumber) missing.push('number');
        if (!strength.hasSpecial) missing.push('special character');
        
        if (missing.length > 0) {
            strengthText.textContent += ` (needs: ${missing.join(', ')})`;
        }
    }
}

function calculatePasswordStrength(password) {
    const strength = {
        level: 0,
        hasLength: password.length >= 8,
        hasUpper: /[A-Z]/.test(password),
        hasLower: /[a-z]/.test(password),
        hasNumber: /[0-9]/.test(password),
        hasSpecial: /[^A-Za-z0-9]/.test(password)
    };
    
    // Calculate level
    let score = 0;
    if (strength.hasLength) score++;
    if (strength.hasUpper) score++;
    if (strength.hasLower) score++;
    if (strength.hasNumber) score++;
    if (strength.hasSpecial) score++;
    
    // Bonus for length
    if (password.length >= 12) score++;
    if (password.length >= 16) score++;
    
    // Penalty for common patterns
    if (/(.)\1{2,}/.test(password)) score--; // Repeated characters
    if (/123|abc|qwe/i.test(password)) score--; // Sequential characters
    
    strength.level = Math.max(1, Math.min(4, score));
    
    return strength;
}

// Email validation with suggestions
function initEmailValidation() {
    const emailInputs = document.querySelectorAll('input[type="email"]');
    
    emailInputs.forEach(input => {
        input.addEventListener('blur', function() {
            const email = this.value.trim();
            if (email && !isValidEmail(email)) {
                const suggestion = suggestEmailCorrection(email);
                if (suggestion && suggestion !== email) {
                    showEmailSuggestion(this, suggestion);
                }
            }
        });
    });
}

function suggestEmailCorrection(email) {
    const commonDomains = [
        'gmail.com', 'yahoo.com', 'hotmail.com', 'outlook.com',
        'aol.com', 'icloud.com', 'mail.com', 'protonmail.com'
    ];
    
    const parts = email.split('@');
    if (parts.length !== 2) return null;
    
    const [username, domain] = parts;
    
    // Check for common typos
    const domainCorrections = {
        'gmial.com': 'gmail.com',
        'gmai.com': 'gmail.com',
        'yahooo.com': 'yahoo.com',
        'hotmial.com': 'hotmail.com',
        'outlok.com': 'outlook.com'
    };
    
    if (domainCorrections[domain]) {
        return `${username}@${domainCorrections[domain]}`;
    }
    
    // Suggest closest match
    let closestDomain = null;
    let minDistance = Infinity;
    
    commonDomains.forEach(commonDomain => {
        const distance = levenshteinDistance(domain, commonDomain);
        if (distance < minDistance && distance <= 2) {
            minDistance = distance;
            closestDomain = commonDomain;
        }
    });
    
    return closestDomain ? `${username}@${closestDomain}` : null;
}

function levenshteinDistance(str1, str2) {
    const matrix = [];
    
    for (let i = 0; i <= str2.length; i++) {
        matrix[i] = [i];
    }
    
    for (let j = 0; j <= str1.length; j++) {
        matrix[0][j] = j;
    }
    
    for (let i = 1; i <= str2.length; i++) {
        for (let j = 1; j <= str1.length; j++) {
            if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
                matrix[i][j] = matrix[i - 1][j - 1];
            } else {
                matrix[i][j] = Math.min(
                    matrix[i - 1][j - 1] + 1,
                    matrix[i][j - 1] + 1,
                    matrix[i - 1][j] + 1
                );
            }
        }
    }
    
    return matrix[str2.length][str1.length];
}

function showEmailSuggestion(input, suggestion) {
    // Remove existing suggestion
    const existingSuggestion = input.parentNode.querySelector('.email-suggestion');
    if (existingSuggestion) {
        existingSuggestion.remove();
    }
    
    const suggestionElement = document.createElement('div');
    suggestionElement.className = 'email-suggestion';
    suggestionElement.innerHTML = `
        <span>Did you mean <strong>${suggestion}</strong>?</span>
        <button type="button" class="suggestion-accept">Yes</button>
        <button type="button" class="suggestion-dismiss">No</button>
    `;
    
    // Add styles
    suggestionElement.style.cssText = `
        margin-top: 0.5rem;
        padding: 0.5rem;
        background: rgba(59, 130, 246, 0.1);
        border: 1px solid rgba(59, 130, 246, 0.3);
        border-radius: 0.375rem;
        font-size: 0.875rem;
        color: #93c5fd;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    `;
    
    input.parentNode.appendChild(suggestionElement);
    
    // Handle suggestion actions
    suggestionElement.querySelector('.suggestion-accept').addEventListener('click', function() {
        input.value = suggestion;
        input.focus();
        suggestionElement.remove();
    });
    
    suggestionElement.querySelector('.suggestion-dismiss').addEventListener('click', function() {
        suggestionElement.remove();
    });
    
    // Auto-remove after 10 seconds
    setTimeout(() => {
        if (suggestionElement.parentNode) {
            suggestionElement.remove();
        }
    }, 10000);
}

// Initialize email validation
initEmailValidation();

// Utility function for email validation
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

// Show notification function (if not already defined)
if (typeof showNotification === 'undefined') {
    function showNotification(message, type = 'info') {
        console.log(`${type.toUpperCase()}: ${message}`);
    }
}
