<?php
session_start();
require_once 'config/database.php';
require_once 'includes/functions.php';
require_once 'includes/language.php';

// Определение языка
$lang = detectLanguage();
loadLanguage($lang);

// Проверка авторизации
$isLoggedIn = isset($_SESSION['user_id']);
$user = null;
if ($isLoggedIn) {
    $user = getUserById($_SESSION['user_id']);
}
?>
<!DOCTYPE html>
<html lang="<?php echo $lang; ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Astragenix - <?php echo t('advanced_crypto_investment'); ?></title>
    <link rel="stylesheet" href="assets/css/main.css">
    <link rel="stylesheet" href="assets/css/animations.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- Навигация -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-logo">
                <img src="assets/images/logo.png" alt="Astragenix" class="logo-img">
                <span class="logo-text">Astragenix</span>
            </div>
            
            <div class="nav-menu" id="nav-menu">
                <a href="#home" class="nav-link"><?php echo t('home'); ?></a>
                <a href="#about" class="nav-link"><?php echo t('about'); ?></a>
                <a href="#plans" class="nav-link"><?php echo t('investment_plans'); ?></a>
                <a href="#features" class="nav-link"><?php echo t('features'); ?></a>
                <a href="#contact" class="nav-link"><?php echo t('contact'); ?></a>
                
                <?php if ($isLoggedIn): ?>
                    <a href="dashboard.php" class="nav-link dashboard-link">
                        <i class="fas fa-tachometer-alt"></i> <?php echo t('dashboard'); ?>
                    </a>
                    <a href="logout.php" class="nav-link logout-link"><?php echo t('logout'); ?></a>
                <?php else: ?>
                    <a href="login.php" class="nav-link login-link"><?php echo t('login'); ?></a>
                    <a href="register.php" class="btn btn-primary"><?php echo t('register'); ?></a>
                <?php endif; ?>
            </div>
            
            <div class="nav-toggle" id="nav-toggle">
                <span class="bar"></span>
                <span class="bar"></span>
                <span class="bar"></span>
            </div>
        </div>
    </nav>

    <!-- Главная секция -->
    <section id="home" class="hero">
        <div class="hero-background">
            <div class="stars"></div>
            <div class="floating-elements">
                <div class="floating-element"></div>
                <div class="floating-element"></div>
                <div class="floating-element"></div>
            </div>
        </div>
        
        <div class="hero-content">
            <div class="container">
                <div class="hero-text">
                    <h1 class="hero-title animate-fade-in">
                        <?php echo t('welcome_to_future'); ?>
                        <span class="gradient-text">Astragenix</span>
                    </h1>
                    <p class="hero-subtitle animate-fade-in-delay">
                        <?php echo t('hero_description'); ?>
                    </p>
                    
                    <div class="hero-stats animate-slide-up">
                        <div class="stat-item">
                            <div class="stat-number" data-target="50000">0</div>
                            <div class="stat-label"><?php echo t('active_investors'); ?></div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number" data-target="25000000">0</div>
                            <div class="stat-label"><?php echo t('total_invested'); ?> USDT</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number" data-target="99.8">0</div>
                            <div class="stat-label"><?php echo t('success_rate'); ?>%</div>
                        </div>
                    </div>
                    
                    <div class="hero-buttons animate-slide-up-delay">
                        <?php if (!$isLoggedIn): ?>
                            <a href="register.php" class="btn btn-primary btn-large">
                                <i class="fas fa-rocket"></i> <?php echo t('start_investing'); ?>
                            </a>
                            <a href="#about" class="btn btn-outline btn-large">
                                <i class="fas fa-play"></i> <?php echo t('learn_more'); ?>
                            </a>
                        <?php else: ?>
                            <a href="dashboard.php" class="btn btn-primary btn-large">
                                <i class="fas fa-tachometer-alt"></i> <?php echo t('go_to_dashboard'); ?>
                            </a>
                        <?php endif; ?>
                    </div>
                </div>
                
                <div class="hero-visual">
                    <div class="crypto-chart">
                        <canvas id="heroChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- О компании -->
    <section id="about" class="about-section">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title"><?php echo t('about_astragenix'); ?></h2>
                <p class="section-subtitle"><?php echo t('about_description'); ?></p>
            </div>
            
            <div class="about-content">
                <div class="about-text">
                    <div class="about-story">
                        <h3><?php echo t('our_story'); ?></h3>
                        <p><?php echo t('company_story'); ?></p>
                    </div>
                    
                    <div class="about-mission">
                        <h3><?php echo t('our_mission'); ?></h3>
                        <p><?php echo t('company_mission'); ?></p>
                    </div>
                    
                    <div class="about-features">
                        <div class="feature-item">
                            <i class="fas fa-shield-alt"></i>
                            <h4><?php echo t('security_first'); ?></h4>
                            <p><?php echo t('security_description'); ?></p>
                        </div>
                        <div class="feature-item">
                            <i class="fas fa-chart-line"></i>
                            <h4><?php echo t('proven_results'); ?></h4>
                            <p><?php echo t('results_description'); ?></p>
                        </div>
                        <div class="feature-item">
                            <i class="fas fa-users"></i>
                            <h4><?php echo t('community_driven'); ?></h4>
                            <p><?php echo t('community_description'); ?></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Инвестиционные планы -->
    <section id="plans" class="plans-section">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title"><?php echo t('investment_plans'); ?></h2>
                <p class="section-subtitle"><?php echo t('plans_description'); ?></p>
            </div>
            
            <div class="plans-grid">
                <div class="plan-card starter">
                    <div class="plan-header">
                        <h3 class="plan-name"><?php echo t('starter_plan'); ?></h3>
                        <div class="plan-icon">
                            <i class="fas fa-seedling"></i>
                        </div>
                    </div>
                    <div class="plan-details">
                        <div class="plan-amount">
                            <span class="currency">USDT</span>
                            <span class="amount">100 - 999</span>
                        </div>
                        <div class="plan-profit">
                            <span class="percentage">1.2%</span>
                            <span class="period"><?php echo t('daily'); ?></span>
                        </div>
                        <ul class="plan-features">
                            <li><i class="fas fa-check"></i> <?php echo t('daily_profits'); ?></li>
                            <li><i class="fas fa-check"></i> <?php echo t('instant_withdrawal'); ?></li>
                            <li><i class="fas fa-check"></i> <?php echo t('24_7_support'); ?></li>
                        </ul>
                    </div>
                    <div class="plan-footer">
                        <a href="<?php echo $isLoggedIn ? 'invest.php?plan=starter' : 'register.php'; ?>" class="btn btn-outline">
                            <?php echo t('choose_plan'); ?>
                        </a>
                    </div>
                </div>
                
                <div class="plan-card professional popular">
                    <div class="plan-badge"><?php echo t('most_popular'); ?></div>
                    <div class="plan-header">
                        <h3 class="plan-name"><?php echo t('professional_plan'); ?></h3>
                        <div class="plan-icon">
                            <i class="fas fa-chart-line"></i>
                        </div>
                    </div>
                    <div class="plan-details">
                        <div class="plan-amount">
                            <span class="currency">USDT</span>
                            <span class="amount">1,000 - 9,999</span>
                        </div>
                        <div class="plan-profit">
                            <span class="percentage">1.8%</span>
                            <span class="period"><?php echo t('daily'); ?></span>
                        </div>
                        <ul class="plan-features">
                            <li><i class="fas fa-check"></i> <?php echo t('daily_profits'); ?></li>
                            <li><i class="fas fa-check"></i> <?php echo t('instant_withdrawal'); ?></li>
                            <li><i class="fas fa-check"></i> <?php echo t('priority_support'); ?></li>
                            <li><i class="fas fa-check"></i> <?php echo t('bonus_rewards'); ?></li>
                        </ul>
                    </div>
                    <div class="plan-footer">
                        <a href="<?php echo $isLoggedIn ? 'invest.php?plan=professional' : 'register.php'; ?>" class="btn btn-primary">
                            <?php echo t('choose_plan'); ?>
                        </a>
                    </div>
                </div>
                
                <div class="plan-card enterprise">
                    <div class="plan-header">
                        <h3 class="plan-name"><?php echo t('enterprise_plan'); ?></h3>
                        <div class="plan-icon">
                            <i class="fas fa-crown"></i>
                        </div>
                    </div>
                    <div class="plan-details">
                        <div class="plan-amount">
                            <span class="currency">USDT</span>
                            <span class="amount">10,000+</span>
                        </div>
                        <div class="plan-profit">
                            <span class="percentage">2.5%</span>
                            <span class="period"><?php echo t('daily'); ?></span>
                        </div>
                        <ul class="plan-features">
                            <li><i class="fas fa-check"></i> <?php echo t('daily_profits'); ?></li>
                            <li><i class="fas fa-check"></i> <?php echo t('instant_withdrawal'); ?></li>
                            <li><i class="fas fa-check"></i> <?php echo t('vip_support'); ?></li>
                            <li><i class="fas fa-check"></i> <?php echo t('exclusive_bonuses'); ?></li>
                            <li><i class="fas fa-check"></i> <?php echo t('personal_manager'); ?></li>
                        </ul>
                    </div>
                    <div class="plan-footer">
                        <a href="<?php echo $isLoggedIn ? 'invest.php?plan=enterprise' : 'register.php'; ?>" class="btn btn-outline">
                            <?php echo t('choose_plan'); ?>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <script src="assets/js/main.js"></script>
    <script src="assets/js/animations.js"></script>
    <script src="assets/js/chart.js"></script>
</body>
</html>
