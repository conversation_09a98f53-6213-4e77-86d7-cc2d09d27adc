<?php
session_start();
require_once 'config/database.php';
require_once 'includes/functions.php';
require_once 'includes/language.php';

// Определение языка
$lang = detectLanguage();
loadLanguage($lang);

// Проверка авторизации
$isLoggedIn = isset($_SESSION['user_id']);
$user = null;
if ($isLoggedIn) {
    $user = getUserById($_SESSION['user_id']);
}

// Настройки страницы
$pageTitle = 'Astragenix - ' . t('advanced_crypto_investment');
$pageDescription = t('hero_description');
$currentPage = 'home';
$bodyClass = 'home-page';

// Подключение заголовка
include 'includes/header.php';
?>
    <!-- Hero Section -->
    <section class="hero-section" id="hero">
        <div class="hero-background">
            <div class="particles" id="particles"></div>
            <div class="hero-gradient"></div>
        </div>

        <div class="container">
            <div class="hero-content">
                <div class="hero-text">
                    <h1 class="hero-title scroll-reveal">
                        <?php echo t('welcome_to_future'); ?>
                        <span class="gradient-text">Astragenix</span>
                    </h1>
                    <p class="hero-subtitle scroll-reveal">
                        <?php echo t('hero_description'); ?>
                    </p>

                    <div class="hero-stats scroll-reveal">
                        <div class="stat-item">
                            <div class="stat-number" data-target="50000">0</div>
                            <div class="stat-label"><?php echo t('active_investors'); ?></div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number" data-target="25000000">0</div>
                            <div class="stat-label"><?php echo t('total_invested'); ?> USDT</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number" data-target="99.8">0</div>
                            <div class="stat-label"><?php echo t('success_rate'); ?>%</div>
                        </div>
                    </div>

                    <div class="hero-buttons scroll-reveal">
                        <?php if (!$isLoggedIn): ?>
                            <a href="register.php" class="btn btn-primary btn-lg">
                                <i class="fas fa-rocket"></i>
                                <span><?php echo t('start_investing'); ?></span>
                            </a>
                            <a href="about.php" class="btn btn-outline btn-lg">
                                <i class="fas fa-info-circle"></i>
                                <span><?php echo t('learn_more'); ?></span>
                            </a>
                        <?php else: ?>
                            <a href="dashboard.php" class="btn btn-primary btn-lg">
                                <i class="fas fa-tachometer-alt"></i>
                                <span><?php echo t('go_to_dashboard'); ?></span>
                            </a>
                            <a href="investment-plans.php" class="btn btn-outline btn-lg">
                                <i class="fas fa-chart-line"></i>
                                <span><?php echo t('view_plans'); ?></span>
                            </a>
                        <?php endif; ?>
                    </div>
                </div>

                <div class="hero-visual scroll-reveal-right">
                    <div class="hero-dashboard">
                        <div class="dashboard-header">
                            <div class="dashboard-title">
                                <i class="fas fa-chart-line"></i>
                                <span><?php echo t('live_trading'); ?></span>
                            </div>
                            <div class="dashboard-status">
                                <span class="status-dot"></span>
                                <span><?php echo t('online'); ?></span>
                            </div>
                        </div>
                        <div class="dashboard-chart">
                            <canvas id="heroChart" width="400" height="200"></canvas>
                        </div>
                        <div class="dashboard-stats">
                            <div class="dashboard-stat">
                                <div class="stat-value">+12.5%</div>
                                <div class="stat-name"><?php echo t('today_profit'); ?></div>
                            </div>
                            <div class="dashboard-stat">
                                <div class="stat-value">$2,847</div>
                                <div class="stat-name"><?php echo t('total_earned'); ?></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section class="features-section" id="features">
        <div class="container">
            <div class="section-header text-center">
                <h2 class="section-title scroll-reveal"><?php echo t('why_choose_astragenix'); ?></h2>
                <p class="section-subtitle scroll-reveal"><?php echo t('features_description'); ?></p>
            </div>

            <div class="features-grid">
                <div class="feature-card scroll-reveal stagger-item">
                    <div class="feature-icon">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <h3 class="feature-title"><?php echo t('security_first'); ?></h3>
                    <p class="feature-description"><?php echo t('security_description'); ?></p>
                </div>

                <div class="feature-card scroll-reveal stagger-item">
                    <div class="feature-icon">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <h3 class="feature-title"><?php echo t('proven_results'); ?></h3>
                    <p class="feature-description"><?php echo t('results_description'); ?></p>
                </div>

                <div class="feature-card scroll-reveal stagger-item">
                    <div class="feature-icon">
                        <i class="fas fa-robot"></i>
                    </div>
                    <h3 class="feature-title"><?php echo t('ai_trading'); ?></h3>
                    <p class="feature-description"><?php echo t('ai_description'); ?></p>
                </div>

                <div class="feature-card scroll-reveal stagger-item">
                    <div class="feature-icon">
                        <i class="fas fa-clock"></i>
                    </div>
                    <h3 class="feature-title"><?php echo t('instant_withdrawals'); ?></h3>
                    <p class="feature-description"><?php echo t('withdrawal_description'); ?></p>
                </div>

                <div class="feature-card scroll-reveal stagger-item">
                    <div class="feature-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <h3 class="feature-title"><?php echo t('community_driven'); ?></h3>
                    <p class="feature-description"><?php echo t('community_description'); ?></p>
                </div>

                <div class="feature-card scroll-reveal stagger-item">
                    <div class="feature-icon">
                        <i class="fas fa-headset"></i>
                    </div>
                    <h3 class="feature-title"><?php echo t('24_7_support'); ?></h3>
                    <p class="feature-description"><?php echo t('support_description'); ?></p>
                </div>
            </div>
        </div>
    </section>

    <!-- About Section -->
    <section class="about-section" id="about">
        <div class="container">
            <div class="about-content">
                <div class="about-text scroll-reveal-left">
                    <h2 class="section-title"><?php echo t('about_astragenix'); ?></h2>
                    <p class="about-description"><?php echo t('about_description_detailed'); ?></p>

                    <div class="about-highlights">
                        <div class="highlight-item">
                            <div class="highlight-icon">
                                <i class="fas fa-calendar-alt"></i>
                            </div>
                            <div class="highlight-content">
                                <h4><?php echo t('founded_in'); ?> 2025</h4>
                                <p><?php echo t('founded_description'); ?></p>
                            </div>
                        </div>

                        <div class="highlight-item">
                            <div class="highlight-icon">
                                <i class="fas fa-globe"></i>
                            </div>
                            <div class="highlight-content">
                                <h4><?php echo t('global_presence'); ?></h4>
                                <p><?php echo t('global_description'); ?></p>
                            </div>
                        </div>

                        <div class="highlight-item">
                            <div class="highlight-icon">
                                <i class="fas fa-award"></i>
                            </div>
                            <div class="highlight-content">
                                <h4><?php echo t('industry_leader'); ?></h4>
                                <p><?php echo t('leader_description'); ?></p>
                            </div>
                        </div>
                    </div>

                    <div class="about-cta">
                        <a href="about.php" class="btn btn-primary">
                            <i class="fas fa-arrow-right"></i>
                            <span><?php echo t('learn_more'); ?></span>
                        </a>
                    </div>
                </div>

                <div class="about-visual scroll-reveal-right">
                    <div class="about-image">
                        <img src="assets/images/about-visual.jpg" alt="<?php echo t('about_astragenix'); ?>" class="img-responsive">
                        <div class="image-overlay">
                            <div class="play-button" id="play-video">
                                <i class="fas fa-play"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- CTA Section -->
    <section class="cta-section">
        <div class="container">
            <div class="cta-content text-center">
                <h2 class="cta-title scroll-reveal"><?php echo t('ready_to_start'); ?></h2>
                <p class="cta-subtitle scroll-reveal"><?php echo t('cta_description'); ?></p>

                <div class="cta-buttons scroll-reveal">
                    <?php if (!$isLoggedIn): ?>
                        <a href="register.php" class="btn btn-primary btn-xl">
                            <i class="fas fa-rocket"></i>
                            <span><?php echo t('get_started_now'); ?></span>
                        </a>
                        <a href="investment-plans.php" class="btn btn-outline btn-xl">
                            <i class="fas fa-chart-line"></i>
                            <span><?php echo t('view_plans'); ?></span>
                        </a>
                    <?php else: ?>
                        <a href="dashboard.php" class="btn btn-primary btn-xl">
                            <i class="fas fa-tachometer-alt"></i>
                            <span><?php echo t('go_to_dashboard'); ?></span>
                        </a>
                    <?php endif; ?>
                </div>

                <div class="cta-features scroll-reveal">
                    <div class="cta-feature">
                        <i class="fas fa-check-circle"></i>
                        <span><?php echo t('no_hidden_fees'); ?></span>
                    </div>
                    <div class="cta-feature">
                        <i class="fas fa-check-circle"></i>
                        <span><?php echo t('instant_setup'); ?></span>
                    </div>
                    <div class="cta-feature">
                        <i class="fas fa-check-circle"></i>
                        <span><?php echo t('secure_platform'); ?></span>
                    </div>
                </div>
            </div>
        </div>
    </section>

<?php
// Подключение футера
include 'includes/footer.php';
?>
