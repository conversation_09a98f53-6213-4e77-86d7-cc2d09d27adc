# 🚀 Быстрая установка Astragenix

## Шаг 1: Подготовка сервера

### Требования:
- PHP 7.4+ с расширениями: mysqli, gd, curl, json
- MySQL 5.7+ или MariaDB 10.2+
- Apache/Nginx с mod_rewrite
- 50MB свободного места

### Проверка PHP:
```bash
php -v
php -m | grep -E "(mysqli|gd|curl|json)"
```

## Шаг 2: Настройка базы данных

### 1. Создание БД:
```sql
CREATE DATABASE astragenix CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER 'astragenix_user'@'localhost' IDENTIFIED BY 'your_strong_password';
GRANT ALL PRIVILEGES ON astragenix.* TO 'astragenix_user'@'localhost';
FLUSH PRIVILEGES;
```

### 2. Импорт структуры:
```bash
mysql -u astragenix_user -p astragenix < database.sql
```

## Шаг 3: Настройка конфигурации

### Отредактируйте `config/database.php`:
```php
define('DB_HOST', 'localhost');
define('DB_NAME', 'astragenix');
define('DB_USER', 'astragenix_user');
define('DB_PASS', 'your_strong_password');

// Измените секретные ключи!
define('ENCRYPTION_KEY', 'your-unique-32-character-key-here');
define('JWT_SECRET', 'your-unique-jwt-secret-key-here');
```

## Шаг 4: Создание изображений

### Запустите скрипт создания изображений:
```bash
php create-default-images.php
```

Или создайте изображения вручную:
- `assets/images/logo.png` (64x64)
- `assets/images/default-avatar.png` (100x100)
- `assets/images/default-news.jpg` (300x200)

## Шаг 5: Настройка веб-сервера

### Apache (.htaccess):
```apache
RewriteEngine On
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^api/(.*)$ api/$1 [L]

# Security
Header always set X-Content-Type-Options nosniff
Header always set X-Frame-Options DENY
Header always set X-XSS-Protection "1; mode=block"

# Cache static files
<FilesMatch "\.(css|js|png|jpg|jpeg|gif|svg|ico)$">
    ExpiresActive On
    ExpiresDefault "access plus 1 month"
</FilesMatch>
```

### Nginx:
```nginx
server {
    listen 80;
    server_name your-domain.com;
    root /path/to/astragenix;
    index index.php;

    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php7.4-fpm.sock;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        include fastcgi_params;
    }

    location ~* \.(css|js|png|jpg|jpeg|gif|svg|ico)$ {
        expires 1M;
        add_header Cache-Control "public, immutable";
    }
}
```

## Шаг 6: Создание администратора

### Выполните SQL запрос:
```sql
INSERT INTO users (
    email, password, first_name, last_name, 
    referral_code, role, status, created_at
) VALUES (
    '<EMAIL>',
    '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi',
    'Admin',
    'User',
    'ADMIN001',
    'admin',
    'active',
    NOW()
);
```

**Данные для входа:**
- Email: `<EMAIL>`
- Пароль: `password`

⚠️ **ОБЯЗАТЕЛЬНО смените пароль после первого входа!**

## Шаг 7: Настройка прав доступа

```bash
# Права на папки
chmod 755 uploads/
chmod 755 assets/
chmod 644 config/database.php

# Владелец файлов (для Apache)
chown -R www-data:www-data /path/to/astragenix

# Или для Nginx
chown -R nginx:nginx /path/to/astragenix
```

## Шаг 8: Проверка установки

### 1. Откройте сайт в браузере:
```
http://your-domain.com
```

### 2. Проверьте основные страницы:
- ✅ Главная страница
- ✅ Регистрация: `/register.php`
- ✅ Вход: `/login.php`
- ✅ Админ-панель: `/admin/dashboard.php`

### 3. Тестовая регистрация:
1. Зарегистрируйте тестового пользователя
2. Проверьте получение стартового бонуса (50 USDT)
3. Войдите в пользовательскую панель

## 🔧 Дополнительные настройки

### SSL сертификат (рекомендуется):
```bash
# Для Let's Encrypt
certbot --nginx -d your-domain.com
```

### Настройка cron для автоматизации:
```bash
# Добавьте в crontab
0 0 * * * php /path/to/astragenix/cron/daily-tasks.php
0 */6 * * * php /path/to/astragenix/cron/cleanup.php
```

### Настройка email (опционально):
Отредактируйте функцию `sendEmail()` в `includes/functions.php` для интеграции с вашим SMTP сервером.

## 🐛 Устранение проблем

### Проблема: Белая страница
**Решение:**
```bash
# Проверьте логи ошибок
tail -f /var/log/apache2/error.log
# или
tail -f /var/log/nginx/error.log

# Включите отображение ошибок (только для разработки!)
echo "ini_set('display_errors', 1);" >> config/database.php
```

### Проблема: Ошибка подключения к БД
**Решение:**
1. Проверьте данные в `config/database.php`
2. Убедитесь, что MySQL запущен: `systemctl status mysql`
3. Проверьте права пользователя БД

### Проблема: 404 ошибки
**Решение:**
1. Проверьте настройку mod_rewrite для Apache
2. Убедитесь, что файл `.htaccess` существует
3. Для Nginx проверьте конфигурацию `try_files`

### Проблема: Не загружаются стили/скрипты
**Решение:**
1. Проверьте права доступа к папке `assets/`
2. Убедитесь, что файлы CSS/JS существуют
3. Проверьте консоль браузера на ошибки

## 📱 Тестирование мобильной версии

1. Откройте сайт на мобильном устройстве
2. Проверьте адаптивность всех страниц
3. Протестируйте навигацию и формы
4. Убедитесь в корректной работе touch-событий

## 🔒 Безопасность в продакшене

### Обязательные настройки:
1. **Смените все пароли по умолчанию**
2. **Используйте HTTPS**
3. **Настройте файрвол**
4. **Регулярно обновляйте систему**
5. **Делайте резервные копии БД**

### Рекомендуемые настройки PHP:
```ini
; php.ini
expose_php = Off
display_errors = Off
log_errors = On
session.cookie_secure = 1
session.cookie_httponly = 1
session.use_strict_mode = 1
```

## 📊 Мониторинг

### Логи для отслеживания:
- `/var/log/apache2/access.log` - доступ к сайту
- `/var/log/apache2/error.log` - ошибки сервера
- Таблица `activity_logs` - действия пользователей
- Таблица `transactions` - финансовые операции

### Важные метрики:
- Количество регистраций в день
- Объем новых инвестиций
- Количество запросов на вывод
- Активность пользователей

## 🎉 Готово!

Ваша платформа Astragenix готова к работе!

### Следующие шаги:
1. Настройте дизайн под ваш бренд
2. Добавьте реальные платежные системы
3. Настройте email уведомления
4. Создайте контент (новости, FAQ)
5. Протестируйте все функции

### Поддержка:
- Документация: `README.md`
- Структура БД: `database.sql`
- Примеры API: папка `api/`

**Удачи с вашим проектом! 🚀**
