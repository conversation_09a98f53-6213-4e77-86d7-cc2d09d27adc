/* Investment Page Styles */

.invest-page {
    background: linear-gradient(135deg, var(--bg-primary), var(--bg-secondary));
    min-height: 100vh;
}

.invest-section {
    padding: var(--spacing-2xl) 0;
}

.balance-info {
    margin-bottom: var(--spacing-3xl);
}

.balance-card {
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-xl);
    padding: var(--spacing-xl);
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
    box-shadow: var(--shadow-lg);
    max-width: 400px;
    margin: 0 auto;
}

.balance-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-inverse);
    font-size: var(--font-size-xl);
}

.balance-details h3 {
    font-size: var(--font-size-base);
    color: var(--text-secondary);
    margin-bottom: var(--spacing-xs);
    font-weight: 500;
}

.balance-amount {
    font-size: var(--font-size-2xl);
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: var(--spacing-xs);
}

.balance-available {
    font-size: var(--font-size-sm);
    color: var(--text-muted);
}

.investment-plans {
    margin-bottom: var(--spacing-3xl);
}

.section-title {
    text-align: center;
    font-size: var(--font-size-3xl);
    font-weight: 700;
    margin-bottom: var(--spacing-2xl);
    color: var(--text-primary);
}

.plans-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: var(--spacing-2xl);
    margin-top: var(--spacing-2xl);
}

.plan-card {
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-2xl);
    padding: var(--spacing-2xl);
    position: relative;
    transition: var(--transition-base);
    cursor: pointer;
    overflow: hidden;
}

.plan-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
    transform: scaleX(0);
    transition: var(--transition-base);
}

.plan-card:hover::before,
.plan-card.selected::before {
    transform: scaleX(1);
}

.plan-card:hover,
.plan-card.selected {
    transform: translateY(-8px);
    border-color: var(--primary-color);
    box-shadow: var(--shadow-glow);
}

.plan-card.popular {
    border-color: var(--primary-color);
    box-shadow: var(--shadow-glow);
}

.plan-badge {
    position: absolute;
    top: var(--spacing-lg);
    right: var(--spacing-lg);
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    color: var(--text-inverse);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-full);
    font-size: var(--font-size-xs);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.plan-header {
    text-align: center;
    margin-bottom: var(--spacing-xl);
}

.plan-name {
    font-size: var(--font-size-xl);
    font-weight: 700;
    margin-bottom: var(--spacing-md);
    color: var(--text-primary);
}

.plan-rate {
    display: flex;
    align-items: baseline;
    justify-content: center;
    gap: var(--spacing-xs);
}

.rate-value {
    font-size: var(--font-size-4xl);
    font-weight: 800;
    color: var(--primary-color);
}

.rate-period {
    font-size: var(--font-size-base);
    color: var(--text-secondary);
}

.plan-details {
    margin-bottom: var(--spacing-xl);
}

.plan-range,
.plan-duration {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-sm) 0;
    border-bottom: 1px solid var(--border-color);
    margin-bottom: var(--spacing-sm);
}

.range-label,
.duration-label {
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
}

.range-value,
.duration-value {
    color: var(--text-primary);
    font-weight: 600;
}

.plan-features {
    margin-bottom: var(--spacing-xl);
}

.feature-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-sm);
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
}

.feature-item i {
    color: var(--success-color);
    font-size: var(--font-size-xs);
}

.plan-calculator {
    background: var(--bg-tertiary);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
}

.calculator-input {
    margin-bottom: var(--spacing-md);
}

.calculator-input label {
    display: block;
    margin-bottom: var(--spacing-xs);
    font-size: var(--font-size-sm);
    font-weight: 500;
    color: var(--text-primary);
}

.calc-amount {
    width: 100%;
    padding: var(--spacing-sm);
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    color: var(--text-primary);
    font-size: var(--font-size-base);
    transition: var(--transition-fast);
}

.calc-amount:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.calculator-results {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-md);
}

.result-item {
    text-align: center;
}

.result-label {
    display: block;
    font-size: var(--font-size-xs);
    color: var(--text-muted);
    margin-bottom: var(--spacing-xs);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.daily-profit,
.total-profit {
    font-size: var(--font-size-lg);
    font-weight: 700;
    color: var(--success-color);
}

.select-plan-btn {
    width: 100%;
    padding: var(--spacing-md);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.investment-form-container {
    max-width: 600px;
    margin: 0 auto;
}

.form-card {
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-2xl);
    padding: var(--spacing-3xl);
    box-shadow: var(--shadow-xl);
}

.form-header {
    text-align: center;
    margin-bottom: var(--spacing-2xl);
}

.form-header h3 {
    font-size: var(--font-size-2xl);
    font-weight: 700;
    margin-bottom: var(--spacing-sm);
    color: var(--text-primary);
}

.form-header p {
    color: var(--text-secondary);
}

.investment-summary {
    background: var(--bg-tertiary);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
}

.summary-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-sm) 0;
    border-bottom: 1px solid var(--border-color);
    margin-bottom: var(--spacing-sm);
}

.summary-item:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.summary-item.total {
    font-weight: 700;
    font-size: var(--font-size-lg);
    color: var(--primary-color);
    border-top: 2px solid var(--border-color);
    padding-top: var(--spacing-md);
    margin-top: var(--spacing-md);
}

.form-actions {
    display: flex;
    gap: var(--spacing-lg);
    justify-content: center;
}

.form-actions .btn {
    min-width: 140px;
    padding: var(--spacing-md) var(--spacing-xl);
    font-weight: 600;
}

.input-help {
    margin-top: var(--spacing-xs);
    font-size: var(--font-size-xs);
    color: var(--text-muted);
}

@media (max-width: 768px) {
    .plans-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
    }
    
    .plan-card {
        padding: var(--spacing-xl);
    }
    
    .balance-card {
        flex-direction: column;
        text-align: center;
        gap: var(--spacing-md);
    }
    
    .form-card {
        padding: var(--spacing-2xl);
    }
    
    .form-actions {
        flex-direction: column;
    }
    
    .calculator-results {
        grid-template-columns: 1fr;
        gap: var(--spacing-sm);
    }
}
