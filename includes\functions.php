<?php
// Основные функции приложения

// Функция для безопасного вывода данных
function h($string) {
    return htmlspecialchars($string, ENT_QUOTES, 'UTF-8');
}

// Функция для генерации CSRF токена
function generateCSRFToken() {
    if (!isset($_SESSION['csrf_token'])) {
        $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
    }
    return $_SESSION['csrf_token'];
}

// Функция для проверки CSRF токена
function verifyCSRFToken($token) {
    return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
}

// Функция для хеширования паролей
function hashPassword($password) {
    return password_hash($password, PASSWORD_DEFAULT);
}

// Функция для проверки паролей
function verifyPassword($password, $hash) {
    return password_verify($password, $hash);
}

// Функция для генерации случайной строки
function generateRandomString($length = 32) {
    return bin2hex(random_bytes($length / 2));
}

// Функция для получения пользователя по ID
function getUserById($userId) {
    $db = getDB();
    $stmt = $db->prepare("SELECT * FROM users WHERE id = ?");
    $stmt->execute([$userId]);
    return $stmt->fetch();
}

// Функция для получения пользователя по email
function getUserByEmail($email) {
    $db = getDB();
    $stmt = $db->prepare("SELECT * FROM users WHERE email = ?");
    $stmt->execute([$email]);
    return $stmt->fetch();
}

// Функция для создания нового пользователя
function createUser($data) {
    $db = getDB();
    
    $stmt = $db->prepare("
        INSERT INTO users (
            email, password, first_name, last_name, phone, 
            referral_code, referred_by, registration_ip, 
            created_at, status
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW(), 'active')
    ");
    
    return $stmt->execute([
        $data['email'],
        hashPassword($data['password']),
        $data['first_name'],
        $data['last_name'],
        $data['phone'],
        generateReferralCode(),
        $data['referred_by'] ?? null,
        $_SERVER['REMOTE_ADDR']
    ]);
}

// Функция для генерации реферального кода
function generateReferralCode() {
    return strtoupper(substr(md5(uniqid(rand(), true)), 0, 8));
}

// Функция для получения баланса пользователя
function getUserBalance($userId) {
    $db = getDB();
    $stmt = $db->prepare("
        SELECT 
            COALESCE(SUM(CASE WHEN type = 'deposit' THEN amount ELSE 0 END), 0) as deposits,
            COALESCE(SUM(CASE WHEN type = 'withdrawal' THEN amount ELSE 0 END), 0) as withdrawals,
            COALESCE(SUM(CASE WHEN type = 'profit' THEN amount ELSE 0 END), 0) as profits,
            COALESCE(SUM(CASE WHEN type = 'bonus' THEN amount ELSE 0 END), 0) as bonuses,
            COALESCE(SUM(CASE WHEN type = 'referral' THEN amount ELSE 0 END), 0) as referral_earnings
        FROM transactions 
        WHERE user_id = ? AND status = 'completed'
    ");
    $stmt->execute([$userId]);
    $result = $stmt->fetch();
    
    $balance = $result['deposits'] + $result['profits'] + $result['bonuses'] + $result['referral_earnings'] - $result['withdrawals'];
    
    return [
        'total_balance' => $balance,
        'deposits' => $result['deposits'],
        'withdrawals' => $result['withdrawals'],
        'profits' => $result['profits'],
        'bonuses' => $result['bonuses'],
        'referral_earnings' => $result['referral_earnings']
    ];
}

// Функция для получения активных инвестиций пользователя
function getUserInvestments($userId) {
    $db = getDB();
    $stmt = $db->prepare("
        SELECT * FROM investments 
        WHERE user_id = ? AND status = 'active'
        ORDER BY created_at DESC
    ");
    $stmt->execute([$userId]);
    return $stmt->fetchAll();
}

// Функция для создания новой инвестиции
function createInvestment($userId, $planId, $amount) {
    $db = getDB();
    
    // Получаем информацию о плане
    $plan = getInvestmentPlan($planId);
    if (!$plan) {
        return false;
    }
    
    $stmt = $db->prepare("
        INSERT INTO investments (
            user_id, plan_id, amount, daily_profit, 
            total_profit, status, created_at
        ) VALUES (?, ?, ?, ?, 0, 'active', NOW())
    ");
    
    $dailyProfit = $amount * ($plan['daily_percentage'] / 100);
    
    return $stmt->execute([$userId, $planId, $amount, $dailyProfit]);
}

// Функция для получения плана инвестиций
function getInvestmentPlan($planId) {
    $plans = [
        'starter' => [
            'id' => 'starter',
            'name' => 'Starter Plan',
            'min_amount' => 100,
            'max_amount' => 999,
            'daily_percentage' => 1.2,
            'duration_days' => 365
        ],
        'professional' => [
            'id' => 'professional',
            'name' => 'Professional Plan',
            'min_amount' => 1000,
            'max_amount' => 9999,
            'daily_percentage' => 1.8,
            'duration_days' => 365
        ],
        'enterprise' => [
            'id' => 'enterprise',
            'name' => 'Enterprise Plan',
            'min_amount' => 10000,
            'max_amount' => 100000,
            'daily_percentage' => 2.5,
            'duration_days' => 365
        ]
    ];
    
    return $plans[$planId] ?? null;
}

// Функция для добавления транзакции
function addTransaction($userId, $type, $amount, $description = '', $status = 'pending') {
    $db = getDB();
    
    $stmt = $db->prepare("
        INSERT INTO transactions (
            user_id, type, amount, description, status, created_at
        ) VALUES (?, ?, ?, ?, ?, NOW())
    ");
    
    return $stmt->execute([$userId, $type, $amount, $description, $status]);
}

// Функция для получения истории транзакций
function getUserTransactions($userId, $limit = 50) {
    $db = getDB();
    $stmt = $db->prepare("
        SELECT * FROM transactions 
        WHERE user_id = ? 
        ORDER BY created_at DESC 
        LIMIT ?
    ");
    $stmt->execute([$userId, $limit]);
    return $stmt->fetchAll();
}

// Функция для проверки администратора
function isAdmin($userId) {
    $user = getUserById($userId);
    return $user && $user['role'] === 'admin';
}

// Функция для логирования действий
function logAction($userId, $action, $details = '') {
    $db = getDB();
    
    $stmt = $db->prepare("
        INSERT INTO activity_logs (
            user_id, action, details, ip_address, created_at
        ) VALUES (?, ?, ?, ?, NOW())
    ");
    
    return $stmt->execute([$userId, $action, $details, $_SERVER['REMOTE_ADDR']]);
}

// Функция для отправки email уведомлений
function sendEmail($to, $subject, $message) {
    // Здесь можно интегрировать с почтовым сервисом
    // Например, PHPMailer или SendGrid
    return mail($to, $subject, $message);
}

// Функция для форматирования валюты
function formatCurrency($amount, $currency = 'USDT') {
    return number_format($amount, 2) . ' ' . $currency;
}

// Функция для получения IP адреса пользователя
function getUserIP() {
    if (!empty($_SERVER['HTTP_CLIENT_IP'])) {
        return $_SERVER['HTTP_CLIENT_IP'];
    } elseif (!empty($_SERVER['HTTP_X_FORWARDED_FOR'])) {
        return $_SERVER['HTTP_X_FORWARDED_FOR'];
    } else {
        return $_SERVER['REMOTE_ADDR'];
    }
}

// Функция для проверки силы пароля
function isStrongPassword($password) {
    return strlen($password) >= 8 && 
           preg_match('/[A-Z]/', $password) && 
           preg_match('/[a-z]/', $password) && 
           preg_match('/[0-9]/', $password);
}

// Функция для валидации email
function isValidEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
}

// Функция для редиректа
function redirect($url) {
    header("Location: $url");
    exit;
}

// Функция для установки flash сообщения
function setFlashMessage($type, $message) {
    $_SESSION['flash'][$type] = $message;
}

// Функция для получения flash сообщения
function getFlashMessage($type) {
    if (isset($_SESSION['flash'][$type])) {
        $message = $_SESSION['flash'][$type];
        unset($_SESSION['flash'][$type]);
        return $message;
    }
    return null;
}
?>
