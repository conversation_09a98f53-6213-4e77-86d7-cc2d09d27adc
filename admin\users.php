<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';
require_once '../includes/language.php';

// Определение языка
$lang = detectLanguage();
loadLanguage($lang);

// Проверка авторизации и прав администратора
if (!isset($_SESSION['user_id'])) {
    redirect('../login.php');
}

$user = getUserById($_SESSION['user_id']);
if (!$user || $user['role'] !== 'admin') {
    redirect('../dashboard.php');
}

$db = getDB();

// Параметры фильтрации и пагинации
$search = $_GET['search'] ?? '';
$status_filter = $_GET['status'] ?? 'all';
$role_filter = $_GET['role'] ?? 'all';
$page = max(1, intval($_GET['page'] ?? 1));
$per_page = 20;
$offset = ($page - 1) * $per_page;

// Построение WHERE условий
$where_conditions = ['1=1'];
$params = [];

if (!empty($search)) {
    $where_conditions[] = '(first_name LIKE ? OR last_name LIKE ? OR email LIKE ?)';
    $search_param = "%{$search}%";
    $params[] = $search_param;
    $params[] = $search_param;
    $params[] = $search_param;
}

if ($status_filter !== 'all') {
    $where_conditions[] = 'status = ?';
    $params[] = $status_filter;
}

if ($role_filter !== 'all') {
    $where_conditions[] = 'role = ?';
    $params[] = $role_filter;
}

$where_clause = implode(' AND ', $where_conditions);

// Получение общего количества пользователей
$count_stmt = $db->prepare("SELECT COUNT(*) FROM users WHERE {$where_clause}");
$count_stmt->execute($params);
$total_users = $count_stmt->fetchColumn();
$total_pages = ceil($total_users / $per_page);

// Получение пользователей
$stmt = $db->prepare("
    SELECT 
        u.*,
        COALESCE(ub.total_balance, 0) as total_balance,
        COALESCE(ub.available_balance, 0) as available_balance,
        COALESCE(inv_stats.total_invested, 0) as total_invested,
        COALESCE(inv_stats.active_investments, 0) as active_investments
    FROM users u
    LEFT JOIN user_balances ub ON u.id = ub.user_id
    LEFT JOIN (
        SELECT 
            user_id,
            SUM(amount) as total_invested,
            COUNT(*) as active_investments
        FROM investments 
        WHERE status = 'active'
        GROUP BY user_id
    ) inv_stats ON u.id = inv_stats.user_id
    WHERE {$where_clause}
    ORDER BY u.created_at DESC
    LIMIT ? OFFSET ?
");

$params[] = $per_page;
$params[] = $offset;
$stmt->execute($params);
$users = $stmt->fetchAll();

$pageTitle = 'User Management - Admin Panel';
$currentPage = 'admin-users';
$bodyClass = 'admin-page';
$additionalCSS = ['../assets/css/admin.css'];
?>

<!DOCTYPE html>
<html lang="<?php echo $lang; ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?></title>
    <link rel="stylesheet" href="../assets/css/modern.css">
    <?php if (isset($additionalCSS)): ?>
        <?php foreach ($additionalCSS as $css): ?>
            <link rel="stylesheet" href="<?php echo $css; ?>">
        <?php endforeach; ?>
    <?php endif; ?>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body class="<?php echo $bodyClass; ?>">
    <!-- Admin Header -->
    <header class="admin-header">
        <div class="admin-header-content">
            <div class="admin-logo">
                <img src="../assets/images/logo.png" alt="Astragenix">
                <span>Admin Panel</span>
            </div>
            
            <div class="admin-nav">
                <a href="index.php" class="nav-item">
                    <i class="fas fa-tachometer-alt"></i>
                    Dashboard
                </a>
                <a href="users.php" class="nav-item active">
                    <i class="fas fa-users"></i>
                    Users
                </a>
                <a href="investments.php" class="nav-item">
                    <i class="fas fa-chart-line"></i>
                    Investments
                </a>
                <a href="transactions.php" class="nav-item">
                    <i class="fas fa-exchange-alt"></i>
                    Transactions
                </a>
                <a href="settings.php" class="nav-item">
                    <i class="fas fa-cog"></i>
                    Settings
                </a>
            </div>
            
            <div class="admin-user">
                <span>Welcome, <?php echo h($user['first_name']); ?></span>
                <a href="../dashboard.php" class="btn btn-outline btn-sm">
                    <i class="fas fa-external-link-alt"></i>
                    View Site
                </a>
                <a href="../logout.php" class="btn btn-danger btn-sm">
                    <i class="fas fa-sign-out-alt"></i>
                    Logout
                </a>
            </div>
        </div>
    </header>

    <!-- Admin Content -->
    <main class="admin-main">
        <div class="admin-container">
            <!-- Page Header -->
            <div class="admin-page-header">
                <h1>User Management</h1>
                <p>Manage user accounts, view statistics, and handle user-related operations</p>
            </div>

            <!-- Filters and Search -->
            <div class="admin-card">
                <div class="card-header">
                    <h3>Filter Users</h3>
                    <div class="header-stats">
                        Total Users: <?php echo number_format($total_users); ?>
                    </div>
                </div>
                <div class="card-content">
                    <form method="GET" class="admin-filters">
                        <div class="filter-group">
                            <label for="search">Search Users</label>
                            <input type="text" 
                                   id="search" 
                                   name="search" 
                                   value="<?php echo h($search); ?>"
                                   placeholder="Search by name or email..."
                                   class="form-input">
                        </div>
                        
                        <div class="filter-group">
                            <label for="status">Status</label>
                            <select id="status" name="status" class="form-input">
                                <option value="all" <?php echo $status_filter === 'all' ? 'selected' : ''; ?>>All Statuses</option>
                                <option value="active" <?php echo $status_filter === 'active' ? 'selected' : ''; ?>>Active</option>
                                <option value="suspended" <?php echo $status_filter === 'suspended' ? 'selected' : ''; ?>>Suspended</option>
                                <option value="banned" <?php echo $status_filter === 'banned' ? 'selected' : ''; ?>>Banned</option>
                            </select>
                        </div>
                        
                        <div class="filter-group">
                            <label for="role">Role</label>
                            <select id="role" name="role" class="form-input">
                                <option value="all" <?php echo $role_filter === 'all' ? 'selected' : ''; ?>>All Roles</option>
                                <option value="user" <?php echo $role_filter === 'user' ? 'selected' : ''; ?>>User</option>
                                <option value="admin" <?php echo $role_filter === 'admin' ? 'selected' : ''; ?>>Admin</option>
                            </select>
                        </div>
                        
                        <div class="filter-actions">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-search"></i>
                                Search
                            </button>
                            <a href="users.php" class="btn btn-secondary">
                                <i class="fas fa-times"></i>
                                Clear
                            </a>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Users Table -->
            <div class="admin-card">
                <div class="card-header">
                    <h3>Users List</h3>
                    <div class="header-actions">
                        <button class="btn btn-success btn-sm" onclick="showBulkActionsModal()">
                            <i class="fas fa-tasks"></i>
                            Bulk Actions
                        </button>
                        <button class="btn btn-primary btn-sm" onclick="exportUsers()">
                            <i class="fas fa-download"></i>
                            Export
                        </button>
                    </div>
                </div>
                <div class="card-content">
                    <?php if (!empty($users)): ?>
                        <div class="table-responsive">
                            <table class="admin-table">
                                <thead>
                                    <tr>
                                        <th>
                                            <input type="checkbox" id="select-all" onchange="toggleAllUsers(this)">
                                        </th>
                                        <th>User</th>
                                        <th>Status</th>
                                        <th>Role</th>
                                        <th>Balance</th>
                                        <th>Investments</th>
                                        <th>Joined</th>
                                        <th>Last Activity</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($users as $user_item): ?>
                                        <tr>
                                            <td>
                                                <input type="checkbox" 
                                                       class="user-checkbox" 
                                                       value="<?php echo $user_item['id']; ?>">
                                            </td>
                                            <td>
                                                <div class="user-cell">
                                                    <div class="user-avatar">
                                                        <?php echo strtoupper(substr($user_item['first_name'], 0, 1)); ?>
                                                    </div>
                                                    <div>
                                                        <div class="user-name">
                                                            <?php echo h($user_item['first_name'] . ' ' . $user_item['last_name']); ?>
                                                        </div>
                                                        <div class="user-email">
                                                            <?php echo h($user_item['email']); ?>
                                                        </div>
                                                        <div class="user-id">
                                                            ID: <?php echo $user_item['id']; ?>
                                                        </div>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                <span class="status <?php echo $user_item['status']; ?>">
                                                    <?php echo ucfirst($user_item['status']); ?>
                                                </span>
                                            </td>
                                            <td>
                                                <span class="role <?php echo $user_item['role']; ?>">
                                                    <?php echo ucfirst($user_item['role']); ?>
                                                </span>
                                            </td>
                                            <td>
                                                <div class="balance-info">
                                                    <div class="total-balance">
                                                        <?php echo formatCurrency($user_item['total_balance']); ?>
                                                    </div>
                                                    <div class="available-balance">
                                                        Available: <?php echo formatCurrency($user_item['available_balance']); ?>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                <div class="investment-info">
                                                    <div class="invested-amount">
                                                        <?php echo formatCurrency($user_item['total_invested']); ?>
                                                    </div>
                                                    <div class="investment-count">
                                                        <?php echo $user_item['active_investments']; ?> active
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                <?php echo date('M j, Y', strtotime($user_item['created_at'])); ?>
                                            </td>
                                            <td>
                                                <?php if ($user_item['last_activity']): ?>
                                                    <?php echo date('M j, H:i', strtotime($user_item['last_activity'])); ?>
                                                <?php else: ?>
                                                    <span class="text-muted">Never</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <div class="action-buttons">
                                                    <button class="btn btn-outline btn-xs" 
                                                            onclick="viewUser(<?php echo $user_item['id']; ?>)"
                                                            title="View Details">
                                                        <i class="fas fa-eye"></i>
                                                    </button>
                                                    <button class="btn btn-primary btn-xs" 
                                                            onclick="editUser(<?php echo $user_item['id']; ?>)"
                                                            title="Edit User">
                                                        <i class="fas fa-edit"></i>
                                                    </button>
                                                    <?php if ($user_item['status'] === 'active'): ?>
                                                        <button class="btn btn-warning btn-xs" 
                                                                onclick="suspendUser(<?php echo $user_item['id']; ?>)"
                                                                title="Suspend User">
                                                            <i class="fas fa-pause"></i>
                                                        </button>
                                                    <?php else: ?>
                                                        <button class="btn btn-success btn-xs" 
                                                                onclick="activateUser(<?php echo $user_item['id']; ?>)"
                                                                title="Activate User">
                                                            <i class="fas fa-play"></i>
                                                        </button>
                                                    <?php endif; ?>
                                                    <?php if ($user_item['id'] !== $_SESSION['user_id']): ?>
                                                        <button class="btn btn-danger btn-xs" 
                                                                onclick="deleteUser(<?php echo $user_item['id']; ?>)"
                                                                title="Delete User">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        <?php if ($total_pages > 1): ?>
                            <div class="admin-pagination">
                                <div class="pagination-info">
                                    Showing <?php echo $offset + 1; ?>-<?php echo min($offset + $per_page, $total_users); ?> 
                                    of <?php echo $total_users; ?> users
                                </div>
                                
                                <div class="pagination-controls">
                                    <?php if ($page > 1): ?>
                                        <a href="?page=<?php echo $page - 1; ?>&search=<?php echo urlencode($search); ?>&status=<?php echo $status_filter; ?>&role=<?php echo $role_filter; ?>" 
                                           class="btn btn-outline btn-sm">
                                            <i class="fas fa-chevron-left"></i>
                                            Previous
                                        </a>
                                    <?php endif; ?>
                                    
                                    <span class="page-info">
                                        Page <?php echo $page; ?> of <?php echo $total_pages; ?>
                                    </span>
                                    
                                    <?php if ($page < $total_pages): ?>
                                        <a href="?page=<?php echo $page + 1; ?>&search=<?php echo urlencode($search); ?>&status=<?php echo $status_filter; ?>&role=<?php echo $role_filter; ?>" 
                                           class="btn btn-outline btn-sm">
                                            Next
                                            <i class="fas fa-chevron-right"></i>
                                        </a>
                                    <?php endif; ?>
                                </div>
                            </div>
                        <?php endif; ?>
                    <?php else: ?>
                        <div class="empty-state">
                            <i class="fas fa-users"></i>
                            <h3>No Users Found</h3>
                            <p>No users match your current filters.</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </main>

    <!-- Scripts -->
    <script src="../assets/js/modern.js"></script>
    <script src="../assets/js/admin.js"></script>
    <script src="../assets/js/admin-users.js"></script>
</body>
</html>
