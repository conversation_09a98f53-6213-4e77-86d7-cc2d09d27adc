<?php
// Конфигурация базы данных
define('DB_HOST', 'localhost');
define('DB_NAME', 'astrogenix');
define('DB_USER', 'admin');
define('DB_PASS', 'admin');

// Настройки приложения
define('SITE_URL', 'http://localhost/astragenix');
define('SITE_NAME', 'Astragenix');
define('ADMIN_EMAIL', '<EMAIL>');

// Настройки безопасности
define('ENCRYPTION_KEY', 'your-secret-encryption-key-here');
define('JWT_SECRET', 'your-jwt-secret-key-here');

// Настройки файлов
define('UPLOAD_PATH', 'uploads/');
define('MAX_FILE_SIZE', 5 * 1024 * 1024); // 5MB

// Настройки инвестиций
define('MIN_INVESTMENT', 100);
define('MAX_INVESTMENT', 100000);
define('STARTER_BONUS', 50); // Стартовый бонус в USDT

// Настройки рефералов
define('REFERRAL_LEVELS', 3);
define('REFERRAL_COMMISSION', [0.05, 0.03, 0.02]); // 5%, 3%, 2%

// Подключение к базе данных
try {
    $pdo = new PDO(
        "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4",
        DB_USER,
        DB_PASS,
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_EMULATE_PREPARES => false
        ]
    );
} catch (PDOException $e) {
    die("Database connection failed: " . $e->getMessage());
}

// Функция для получения подключения к БД
function getDB() {
    global $pdo;
    return $pdo;
}
?>
