/* Astragenix Main Styles */

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* Colors */
    --primary-color: #6366f1;
    --primary-dark: #4f46e5;
    --secondary-color: #8b5cf6;
    --accent-color: #06b6d4;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --error-color: #ef4444;
    --dark-bg: #0f172a;
    --dark-surface: #1e293b;
    --dark-border: #334155;
    --text-primary: #f8fafc;
    --text-secondary: #cbd5e1;
    --text-muted: #64748b;
    
    /* Gradients */
    --gradient-primary: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
    --gradient-secondary: linear-gradient(135deg, #06b6d4 0%, #3b82f6 100%);
    --gradient-success: linear-gradient(135deg, #10b981 0%, #059669 100%);
    
    /* Spacing */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;
    
    /* Border Radius */
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    
    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
    
    /* Transitions */
    --transition-fast: 0.15s ease-in-out;
    --transition-normal: 0.3s ease-in-out;
    --transition-slow: 0.5s ease-in-out;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: var(--dark-bg);
    color: var(--text-primary);
    line-height: 1.6;
    overflow-x: hidden;
}

/* Container */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-md);
}

/* Navigation */
.navbar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background: rgba(15, 23, 42, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid var(--dark-border);
    z-index: 1000;
    transition: var(--transition-normal);
}

.nav-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-md) var(--spacing-lg);
    max-width: 1200px;
    margin: 0 auto;
}

.nav-logo {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-primary);
    text-decoration: none;
}

.logo-img {
    width: 32px;
    height: 32px;
}

.logo-text {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.nav-menu {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
}

.nav-link {
    color: var(--text-secondary);
    text-decoration: none;
    font-weight: 500;
    transition: var(--transition-fast);
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--radius-md);
}

.nav-link:hover {
    color: var(--text-primary);
    background: rgba(99, 102, 241, 0.1);
}

.nav-toggle {
    display: none;
    flex-direction: column;
    cursor: pointer;
    gap: 4px;
}

.bar {
    width: 25px;
    height: 3px;
    background: var(--text-primary);
    transition: var(--transition-fast);
    border-radius: 2px;
}

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) var(--spacing-lg);
    border: none;
    border-radius: var(--radius-md);
    font-weight: 600;
    text-decoration: none;
    cursor: pointer;
    transition: var(--transition-fast);
    font-size: 0.875rem;
    line-height: 1.5;
}

.btn-primary {
    background: var(--gradient-primary);
    color: white;
    box-shadow: var(--shadow-md);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.btn-outline {
    background: transparent;
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
}

.btn-outline:hover {
    background: var(--primary-color);
    color: white;
}

.btn-large {
    padding: var(--spacing-md) var(--spacing-xl);
    font-size: 1rem;
}

/* Hero Section */
.hero {
    position: relative;
    min-height: 100vh;
    display: flex;
    align-items: center;
    overflow: hidden;
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(ellipse at center, rgba(99, 102, 241, 0.1) 0%, transparent 70%);
}

.stars {
    position: absolute;
    width: 100%;
    height: 100%;
    background-image: 
        radial-gradient(2px 2px at 20px 30px, #eee, transparent),
        radial-gradient(2px 2px at 40px 70px, rgba(255,255,255,0.8), transparent),
        radial-gradient(1px 1px at 90px 40px, #fff, transparent),
        radial-gradient(1px 1px at 130px 80px, rgba(255,255,255,0.6), transparent);
    background-repeat: repeat;
    background-size: 200px 100px;
    animation: sparkle 20s linear infinite;
}

.floating-elements {
    position: absolute;
    width: 100%;
    height: 100%;
}

.floating-element {
    position: absolute;
    width: 60px;
    height: 60px;
    background: var(--gradient-primary);
    border-radius: 50%;
    opacity: 0.1;
    animation: float 6s ease-in-out infinite;
}

.floating-element:nth-child(1) {
    top: 20%;
    left: 10%;
    animation-delay: 0s;
}

.floating-element:nth-child(2) {
    top: 60%;
    right: 15%;
    animation-delay: 2s;
}

.floating-element:nth-child(3) {
    bottom: 20%;
    left: 20%;
    animation-delay: 4s;
}

.hero-content {
    position: relative;
    z-index: 2;
    width: 100%;
    padding: var(--spacing-2xl) 0;
}

.hero-content .container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-2xl);
    align-items: center;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 800;
    line-height: 1.1;
    margin-bottom: var(--spacing-lg);
}

.gradient-text {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-subtitle {
    font-size: 1.25rem;
    color: var(--text-secondary);
    margin-bottom: var(--spacing-2xl);
    line-height: 1.6;
}

.hero-stats {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-2xl);
}

.stat-item {
    text-align: center;
    padding: var(--spacing-lg);
    background: rgba(255, 255, 255, 0.05);
    border-radius: var(--radius-lg);
    border: 1px solid var(--dark-border);
}

.stat-number {
    font-size: 2rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: var(--spacing-sm);
}

.stat-label {
    font-size: 0.875rem;
    color: var(--text-secondary);
}

.hero-buttons {
    display: flex;
    gap: var(--spacing-lg);
    flex-wrap: wrap;
}

.hero-visual {
    display: flex;
    justify-content: center;
    align-items: center;
}

.crypto-chart {
    width: 100%;
    max-width: 500px;
    height: 400px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: var(--radius-xl);
    border: 1px solid var(--dark-border);
    padding: var(--spacing-lg);
}

/* Sections */
.section-header {
    text-align: center;
    margin-bottom: var(--spacing-2xl);
}

.section-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: var(--spacing-md);
}

.section-subtitle {
    font-size: 1.125rem;
    color: var(--text-secondary);
    max-width: 600px;
    margin: 0 auto;
}

/* About Section */
.about-section {
    padding: var(--spacing-2xl) 0;
    background: rgba(255, 255, 255, 0.02);
}

.about-content {
    max-width: 800px;
    margin: 0 auto;
}

.about-story,
.about-mission {
    margin-bottom: var(--spacing-2xl);
}

.about-story h3,
.about-mission h3 {
    font-size: 1.5rem;
    margin-bottom: var(--spacing-md);
    color: var(--primary-color);
}

.about-features {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-lg);
    margin-top: var(--spacing-2xl);
}

.feature-item {
    text-align: center;
    padding: var(--spacing-xl);
    background: rgba(255, 255, 255, 0.05);
    border-radius: var(--radius-lg);
    border: 1px solid var(--dark-border);
}

.feature-item i {
    font-size: 2.5rem;
    color: var(--primary-color);
    margin-bottom: var(--spacing-md);
}

.feature-item h4 {
    font-size: 1.25rem;
    margin-bottom: var(--spacing-sm);
}

/* Investment Plans */
.plans-section {
    padding: var(--spacing-2xl) 0;
}

.plans-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-xl);
    margin-top: var(--spacing-2xl);
}

.plan-card {
    position: relative;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid var(--dark-border);
    border-radius: var(--radius-xl);
    padding: var(--spacing-xl);
    transition: var(--transition-normal);
}

.plan-card:hover {
    transform: translateY(-5px);
    border-color: var(--primary-color);
    box-shadow: var(--shadow-xl);
}

.plan-card.popular {
    border-color: var(--primary-color);
    background: rgba(99, 102, 241, 0.1);
}

.plan-badge {
    position: absolute;
    top: -10px;
    left: 50%;
    transform: translateX(-50%);
    background: var(--gradient-primary);
    color: white;
    padding: var(--spacing-xs) var(--spacing-md);
    border-radius: var(--radius-md);
    font-size: 0.75rem;
    font-weight: 600;
}

.plan-header {
    text-align: center;
    margin-bottom: var(--spacing-lg);
}

.plan-name {
    font-size: 1.5rem;
    margin-bottom: var(--spacing-md);
}

.plan-icon i {
    font-size: 3rem;
    color: var(--primary-color);
}

.plan-amount {
    text-align: center;
    margin-bottom: var(--spacing-lg);
}

.plan-amount .currency {
    font-size: 1rem;
    color: var(--text-secondary);
}

.plan-amount .amount {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-primary);
}

.plan-profit {
    text-align: center;
    margin-bottom: var(--spacing-lg);
    padding: var(--spacing-md);
    background: rgba(16, 185, 129, 0.1);
    border-radius: var(--radius-md);
}

.plan-profit .percentage {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--success-color);
}

.plan-profit .period {
    font-size: 1rem;
    color: var(--text-secondary);
}

.plan-features {
    list-style: none;
    margin-bottom: var(--spacing-xl);
}

.plan-features li {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-sm);
    color: var(--text-secondary);
}

.plan-features i {
    color: var(--success-color);
}

.plan-footer {
    text-align: center;
}

/* Responsive Design */
@media (max-width: 768px) {
    .nav-menu {
        position: fixed;
        top: 70px;
        left: -100%;
        width: 100%;
        height: calc(100vh - 70px);
        background: var(--dark-bg);
        flex-direction: column;
        justify-content: flex-start;
        align-items: center;
        padding-top: var(--spacing-2xl);
        transition: var(--transition-normal);
    }
    
    .nav-menu.active {
        left: 0;
    }
    
    .nav-toggle {
        display: flex;
    }
    
    .hero-content .container {
        grid-template-columns: 1fr;
        text-align: center;
    }
    
    .hero-title {
        font-size: 2.5rem;
    }
    
    .hero-stats {
        grid-template-columns: 1fr;
    }
    
    .plans-grid {
        grid-template-columns: 1fr;
    }
    
    .about-features {
        grid-template-columns: 1fr;
    }
}

/* Animations */
@keyframes sparkle {
    from { transform: translateX(0); }
    to { transform: translateX(-200px); }
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-20px); }
}

/* Utility Classes */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.mb-sm { margin-bottom: var(--spacing-sm); }
.mb-md { margin-bottom: var(--spacing-md); }
.mb-lg { margin-bottom: var(--spacing-lg); }
.mb-xl { margin-bottom: var(--spacing-xl); }

.mt-sm { margin-top: var(--spacing-sm); }
.mt-md { margin-top: var(--spacing-md); }
.mt-lg { margin-top: var(--spacing-lg); }
.mt-xl { margin-top: var(--spacing-xl); }
