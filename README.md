# Astragenix - Advanced Crypto Investment Platform

Современная инвестиционная платформа с веб-версией и мобильной адаптацией, разработанная на PHP, JavaScript, MySQL, CSS и HTML5.

## 🚀 Особенности

### Основные функции
- **Инвестиционные планы**: Несколько пакетов с депозитами в USDT
- **Ежедневная прибыль**: Система распределения прибыли, управляемая администратором
- **Стартовый бонус**: Бесплатный бонус для новых пользователей
- **Реферальная система**: Многоуровневое отслеживание рефералов
- **Система заданий**: Ежедневные и еженедельные задания с наградами
- **Майнинг**: Виртуальный майнинг с возможностью заработка

### Технические особенности
- **Адаптивный дизайн**: Работает на всех устройствах
- **Мультиязычность**: Поддержка английского и русского языков
- **Современный UI**: Стильный дизайн с анимациями
- **Безопасность**: Защищенная аутентификация и шифрование данных
- **Админ-панель**: Полноценное управление платформой

## 📋 Требования

- PHP 7.4 или выше
- MySQL 5.7 или выше
- Apache/Nginx веб-сервер
- Поддержка mod_rewrite (для Apache)

## 🛠 Установка

### 1. Клонирование проекта
```bash
git clone https://github.com/your-username/astragenix.git
cd astragenix
```

### 2. Настройка базы данных
1. Создайте новую базу данных MySQL:
```sql
CREATE DATABASE astragenix CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

2. Импортируйте структуру базы данных:
```bash
mysql -u your_username -p astragenix < database.sql
```

### 3. Настройка конфигурации
Отредактируйте файл `config/database.php`:
```php
define('DB_HOST', 'localhost');
define('DB_NAME', 'astragenix');
define('DB_USER', 'your_username');
define('DB_PASS', 'your_password');
```

### 4. Настройка веб-сервера

#### Apache
Убедитесь, что mod_rewrite включен и создайте файл `.htaccess` в корне проекта:
```apache
RewriteEngine On
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^(.*)$ index.php [QSA,L]

# Security headers
Header always set X-Content-Type-Options nosniff
Header always set X-Frame-Options DENY
Header always set X-XSS-Protection "1; mode=block"
```

#### Nginx
Добавьте в конфигурацию Nginx:
```nginx
location / {
    try_files $uri $uri/ /index.php?$query_string;
}

location ~ \.php$ {
    fastcgi_pass unix:/var/run/php/php7.4-fpm.sock;
    fastcgi_index index.php;
    fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
    include fastcgi_params;
}
```

### 5. Настройка прав доступа
```bash
chmod 755 uploads/
chmod 644 config/database.php
```

## 👤 Первый запуск

### Создание администратора
После установки создайте первого администратора через SQL:
```sql
INSERT INTO users (
    email, password, first_name, last_name, 
    referral_code, role, status, created_at
) VALUES (
    '<EMAIL>', 
    '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', -- password: password
    'Admin', 
    'User',
    'ADMIN001',
    'admin',
    'active',
    NOW()
);
```

### Доступ к админ-панели
- URL: `http://your-domain.com/admin/dashboard.php`
- Email: `<EMAIL>`
- Пароль: `password` (обязательно смените после входа!)

## 🎨 Структура проекта

```
astragenix/
├── admin/                  # Админ-панель
│   ├── dashboard.php
│   ├── users.php
│   └── ...
├── api/                    # API endpoints
│   ├── admin/
│   └── user/
├── assets/                 # Статические файлы
│   ├── css/
│   ├── js/
│   └── images/
├── config/                 # Конфигурация
│   └── database.php
├── includes/               # Общие PHP файлы
│   ├── functions.php
│   └── language.php
├── languages/              # Языковые файлы
│   ├── en.php
│   └── ru.php
├── uploads/                # Загруженные файлы
├── index.php              # Главная страница
├── dashboard.php          # Пользовательская панель
├── login.php              # Страница входа
├── register.php           # Страница регистрации
└── database.sql           # Структура БД
```

## 🔧 Основные настройки

### Инвестиционные планы
Планы настраиваются в файле `includes/functions.php` в функции `getInvestmentPlan()`:
```php
'starter' => [
    'min_amount' => 100,
    'max_amount' => 999,
    'daily_percentage' => 1.2,
    'duration_days' => 365
]
```

### Реферальная система
Настройки в `config/database.php`:
```php
define('REFERRAL_LEVELS', 3);
define('REFERRAL_COMMISSION', [0.05, 0.03, 0.02]); // 5%, 3%, 2%
```

### Стартовый бонус
```php
define('STARTER_BONUS', 50); // 50 USDT
```

## 📱 Мобильная адаптация

Платформа полностью адаптирована для мобильных устройств:
- Responsive дизайн
- Touch-friendly интерфейс
- Оптимизированная навигация
- Быстрая загрузка

## 🔒 Безопасность

### Реализованные меры безопасности:
- CSRF защита
- XSS защита
- SQL injection защита (prepared statements)
- Хеширование паролей (bcrypt)
- Валидация входных данных
- Защищенные сессии

### Рекомендации:
1. Используйте HTTPS в продакшене
2. Регулярно обновляйте пароли
3. Настройте файрвол
4. Делайте резервные копии БД

## 🎯 Функционал администратора

### Управление пользователями
- Просмотр всех пользователей
- Блокировка/разблокировка аккаунтов
- Просмотр статистики пользователей

### Управление транзакциями
- Одобрение/отклонение депозитов
- Обработка запросов на вывод
- Просмотр истории транзакций

### Распределение прибыли
- Ручное распределение прибыли
- Настройка процентов
- Массовые операции

### Управление контентом
- Добавление новостей
- Управление заданиями
- Настройки сайта

## 🚀 Развертывание в продакшене

### 1. Настройка окружения
```bash
# Установка зависимостей
composer install --no-dev --optimize-autoloader

# Настройка прав
chmod -R 755 storage/
chmod -R 644 config/
```

### 2. Настройка SSL
```nginx
server {
    listen 443 ssl http2;
    ssl_certificate /path/to/certificate.crt;
    ssl_certificate_key /path/to/private.key;
    
    # Остальная конфигурация...
}
```

### 3. Оптимизация производительности
- Включите кеширование
- Настройте сжатие gzip
- Оптимизируйте изображения
- Используйте CDN для статических файлов

## 📊 Мониторинг и аналитика

### Логирование
Все действия пользователей логируются в таблицу `activity_logs`:
```sql
SELECT * FROM activity_logs WHERE user_id = 1 ORDER BY created_at DESC;
```

### Статистика
Основная статистика доступна в админ-панели:
- Количество пользователей
- Общий объем инвестиций
- Выплаченная прибыль
- Активность пользователей

## 🤝 Поддержка

### Система тикетов
Встроенная система поддержки позволяет:
- Создавать тикеты пользователям
- Отвечать на запросы администраторам
- Отслеживать статус обращений

### FAQ
Добавьте часто задаваемые вопросы в админ-панели для снижения нагрузки на поддержку.

## 📝 Лицензия

Этот проект разработан для демонстрационных целей. Перед использованием в коммерческих целях убедитесь в соответствии местному законодательству.

## ⚠️ Предупреждение

Данная платформа создана в образовательных целях. Инвестиционная деятельность может быть регулируемой в вашей юрисдикции. Обязательно проконсультируйтесь с юристами перед запуском в продакшене.

---

**Разработано с ❤️ для демонстрации современных веб-технологий**
