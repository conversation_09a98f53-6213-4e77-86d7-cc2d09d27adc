<?php
session_start();
require_once '../../config/database.php';
require_once '../../includes/functions.php';

// Проверка авторизации и прав администратора
if (!isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Unauthorized']);
    exit;
}

$user = getUserById($_SESSION['user_id']);
if (!$user || $user['role'] !== 'admin') {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'Access denied']);
    exit;
}

try {
    $db = getDB();
    
    // Получение статистики пользователей
    $stmt = $db->query("
        SELECT 
            COUNT(*) as total_users,
            COUNT(CASE WHEN created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY) THEN 1 END) as new_users_month,
            COUNT(CASE WHEN last_activity >= DATE_SUB(NOW(), INTERVAL 24 HOUR) THEN 1 END) as active_users_today,
            COUNT(CASE WHEN created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY) THEN 1 END) as new_users_week
        FROM users
    ");
    $user_stats = $stmt->fetch();
    
    // Статистика инвестиций
    $stmt = $db->query("
        SELECT 
            COUNT(*) as total_investments,
            COUNT(CASE WHEN status = 'active' THEN 1 END) as active_investments,
            COALESCE(SUM(amount), 0) as total_invested,
            COALESCE(SUM(CASE WHEN created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY) THEN amount ELSE 0 END), 0) as invested_this_month,
            COALESCE(SUM(CASE WHEN created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY) THEN amount ELSE 0 END), 0) as invested_this_week
        FROM investments
    ");
    $investment_stats = $stmt->fetch();
    
    // Статистика транзакций
    $stmt = $db->query("
        SELECT 
            COUNT(*) as total_transactions,
            COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_transactions,
            COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_transactions,
            COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed_transactions,
            COALESCE(SUM(CASE WHEN type = 'deposit' AND status = 'completed' THEN amount ELSE 0 END), 0) as total_deposits,
            COALESCE(SUM(CASE WHEN type = 'withdrawal' AND status = 'completed' THEN amount ELSE 0 END), 0) as total_withdrawals,
            COALESCE(SUM(CASE WHEN type = 'profit' AND status = 'completed' THEN amount ELSE 0 END), 0) as total_profits_paid
        FROM transactions
    ");
    $transaction_stats = $stmt->fetch();
    
    // Статистика рефералов
    $stmt = $db->query("
        SELECT 
            COUNT(*) as total_commissions,
            COALESCE(SUM(commission_amount), 0) as total_commission_paid,
            COALESCE(SUM(CASE WHEN created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY) THEN commission_amount ELSE 0 END), 0) as commission_this_month
        FROM referral_commissions
    ");
    $referral_stats = $stmt->fetch();
    
    // Получение ожидающих транзакций
    $stmt = $db->query("
        SELECT t.*, u.first_name, u.last_name, u.email
        FROM transactions t
        JOIN users u ON t.user_id = u.id
        WHERE t.status = 'pending'
        ORDER BY t.created_at ASC
        LIMIT 20
    ");
    $pending_transactions = $stmt->fetchAll();
    
    // Статистика по планам инвестиций
    $stmt = $db->query("
        SELECT 
            plan_id,
            COUNT(*) as count,
            SUM(amount) as total_amount
        FROM investments 
        WHERE status = 'active'
        GROUP BY plan_id
    ");
    $plan_stats = $stmt->fetchAll();
    
    // Статистика по дням (последние 30 дней)
    $stmt = $db->query("
        SELECT 
            DATE(created_at) as date,
            COUNT(*) as new_users,
            COALESCE(SUM(CASE WHEN EXISTS(SELECT 1 FROM investments WHERE user_id = users.id) THEN 1 ELSE 0 END), 0) as investing_users
        FROM users 
        WHERE created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
        GROUP BY DATE(created_at)
        ORDER BY date DESC
    ");
    $daily_stats = $stmt->fetchAll();
    
    // Топ пользователи по инвестициям
    $stmt = $db->query("
        SELECT 
            u.id,
            u.first_name,
            u.last_name,
            u.email,
            COALESCE(SUM(i.amount), 0) as total_invested,
            COUNT(i.id) as investment_count
        FROM users u
        LEFT JOIN investments i ON u.id = i.user_id
        GROUP BY u.id
        HAVING total_invested > 0
        ORDER BY total_invested DESC
        LIMIT 10
    ");
    $top_investors = $stmt->fetchAll();
    
    // Системная информация
    $system_info = [
        'php_version' => PHP_VERSION,
        'server_time' => date('Y-m-d H:i:s'),
        'timezone' => date_default_timezone_get(),
        'memory_usage' => memory_get_usage(true),
        'memory_peak' => memory_get_peak_usage(true)
    ];
    
    $response = [
        'success' => true,
        'stats' => [
            'users' => $user_stats,
            'investments' => $investment_stats,
            'transactions' => $transaction_stats,
            'referrals' => $referral_stats
        ],
        'pending_transactions' => $pending_transactions,
        'plan_stats' => $plan_stats,
        'daily_stats' => $daily_stats,
        'top_investors' => $top_investors,
        'system_info' => $system_info,
        'timestamp' => time()
    ];
    
    echo json_encode($response);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Internal server error']);
    
    // Логируем ошибку
    error_log("Error getting dashboard data: " . $e->getMessage());
}
?>
