/* Button Components */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-sm) var(--spacing-lg);
    border: none;
    border-radius: var(--radius-md);
    font-family: inherit;
    font-size: var(--font-size-base);
    font-weight: 600;
    text-decoration: none;
    cursor: pointer;
    transition: var(--transition-fast);
    position: relative;
    overflow: hidden;
    white-space: nowrap;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: var(--text-inverse);
    box-shadow: var(--shadow-md);
}

.btn-primary:hover:not(:disabled) {
    background: linear-gradient(135deg, var(--primary-light), var(--primary-color));
    box-shadow: var(--shadow-glow);
    transform: translateY(-2px);
}

.btn-secondary {
    background: var(--bg-card);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
}

.btn-secondary:hover:not(:disabled) {
    background: var(--bg-tertiary);
    border-color: var(--border-hover);
}

.btn-outline {
    background: transparent;
    color: var(--primary-color);
    border: 1px solid var(--primary-color);
}

.btn-outline:hover:not(:disabled) {
    background: var(--primary-color);
    color: var(--text-inverse);
}

.btn-ghost {
    background: transparent;
    color: var(--text-secondary);
    border: none;
}

.btn-ghost:hover:not(:disabled) {
    background: rgba(255, 255, 255, 0.05);
    color: var(--text-primary);
}

.btn-success {
    background: var(--success-color);
    color: var(--text-inverse);
}

.btn-warning {
    background: var(--warning-color);
    color: var(--text-inverse);
}

.btn-error {
    background: var(--error-color);
    color: var(--text-inverse);
}

/* Button Sizes */
.btn-sm {
    padding: var(--spacing-xs) var(--spacing-md);
    font-size: var(--font-size-sm);
}

.btn-lg {
    padding: var(--spacing-md) var(--spacing-2xl);
    font-size: var(--font-size-lg);
}

.btn-xl {
    padding: var(--spacing-lg) var(--spacing-3xl);
    font-size: var(--font-size-xl);
}

.btn-full {
    width: 100%;
}

/* Card Components */
.card {
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-xl);
    padding: var(--spacing-xl);
    box-shadow: var(--shadow-md);
    transition: var(--transition-base);
}

.card:hover {
    border-color: var(--border-hover);
    box-shadow: var(--shadow-lg);
    transform: translateY(-4px);
}

.card-header {
    margin-bottom: var(--spacing-lg);
    padding-bottom: var(--spacing-lg);
    border-bottom: 1px solid var(--border-color);
}

.card-title {
    font-size: var(--font-size-xl);
    font-weight: 700;
    margin-bottom: var(--spacing-sm);
    color: var(--text-primary);
}

.card-subtitle {
    color: var(--text-muted);
    font-size: var(--font-size-sm);
}

.card-body {
    margin-bottom: var(--spacing-lg);
}

.card-footer {
    margin-top: var(--spacing-lg);
    padding-top: var(--spacing-lg);
    border-top: 1px solid var(--border-color);
}

.card-glass {
    background: var(--bg-glass);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.card-gradient {
    background: linear-gradient(135deg, var(--bg-card), var(--bg-tertiary));
    border: 1px solid rgba(0, 212, 255, 0.2);
}

.card-highlight {
    border: 2px solid var(--primary-color);
    box-shadow: var(--shadow-glow);
}

/* Form Components */
.form-group {
    margin-bottom: var(--spacing-lg);
}

.form-label {
    display: block;
    margin-bottom: var(--spacing-sm);
    font-weight: 600;
    color: var(--text-primary);
}

.form-input {
    width: 100%;
    padding: var(--spacing-md);
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    color: var(--text-primary);
    font-family: inherit;
    font-size: var(--font-size-base);
    transition: var(--transition-fast);
}

.form-input:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(0, 212, 255, 0.1);
}

.form-input::placeholder {
    color: var(--text-muted);
}

.form-textarea {
    resize: vertical;
    min-height: 120px;
}

.form-select {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 0.5rem center;
    background-repeat: no-repeat;
    background-size: 1.5em 1.5em;
    padding-right: 2.5rem;
}

.form-checkbox,
.form-radio {
    width: auto;
    margin-right: var(--spacing-sm);
}

.form-error {
    color: var(--error-color);
    font-size: var(--font-size-sm);
    margin-top: var(--spacing-xs);
}

.form-help {
    color: var(--text-muted);
    font-size: var(--font-size-sm);
    margin-top: var(--spacing-xs);
}

/* Badge Components */
.badge {
    display: inline-flex;
    align-items: center;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-full);
    font-size: var(--font-size-xs);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.badge-primary {
    background: rgba(0, 212, 255, 0.2);
    color: var(--primary-color);
}

.badge-success {
    background: rgba(0, 230, 118, 0.2);
    color: var(--success-color);
}

.badge-warning {
    background: rgba(255, 152, 0, 0.2);
    color: var(--warning-color);
}

.badge-error {
    background: rgba(244, 67, 54, 0.2);
    color: var(--error-color);
}

.badge-info {
    background: rgba(33, 150, 243, 0.2);
    color: var(--info-color);
}

/* Alert Components */
.alert {
    padding: var(--spacing-md);
    border-radius: var(--radius-md);
    margin-bottom: var(--spacing-lg);
    border-left: 4px solid;
}

.alert-success {
    background: rgba(0, 230, 118, 0.1);
    border-color: var(--success-color);
    color: var(--success-color);
}

.alert-warning {
    background: rgba(255, 152, 0, 0.1);
    border-color: var(--warning-color);
    color: var(--warning-color);
}

.alert-error {
    background: rgba(244, 67, 54, 0.1);
    border-color: var(--error-color);
    color: var(--error-color);
}

.alert-info {
    background: rgba(33, 150, 243, 0.1);
    border-color: var(--info-color);
    color: var(--info-color);
}

/* Modal Components */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: var(--z-modal);
    opacity: 0;
    visibility: hidden;
    transition: var(--transition-base);
}

.modal.active {
    opacity: 1;
    visibility: visible;
}

.modal-content {
    background: var(--bg-card);
    border-radius: var(--radius-xl);
    padding: var(--spacing-2xl);
    max-width: 500px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    transform: scale(0.9);
    transition: var(--transition-base);
}

.modal.active .modal-content {
    transform: scale(1);
}

.modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--spacing-lg);
}

.modal-title {
    font-size: var(--font-size-xl);
    font-weight: 700;
    color: var(--text-primary);
}

.modal-close {
    background: none;
    border: none;
    color: var(--text-muted);
    font-size: var(--font-size-lg);
    cursor: pointer;
    padding: var(--spacing-sm);
    border-radius: var(--radius-md);
    transition: var(--transition-fast);
}

.modal-close:hover {
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-primary);
}

/* Tooltip Components */
.tooltip {
    position: relative;
    display: inline-block;
}

.tooltip-content {
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: var(--bg-tertiary);
    color: var(--text-primary);
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--radius-md);
    font-size: var(--font-size-sm);
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: var(--transition-fast);
    z-index: var(--z-tooltip);
    margin-bottom: var(--spacing-xs);
}

.tooltip:hover .tooltip-content {
    opacity: 1;
    visibility: visible;
}

.tooltip-content::after {
    content: '';
    position: absolute;
    top: 100%;
    left: 50%;
    transform: translateX(-50%);
    border: 5px solid transparent;
    border-top-color: var(--bg-tertiary);
}

/* Features Section */
.features-section {
    padding: var(--spacing-3xl) 0;
    background: var(--bg-secondary);
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-2xl);
    margin-top: var(--spacing-2xl);
}

.feature-card {
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-xl);
    padding: var(--spacing-2xl);
    text-align: center;
    transition: var(--transition-base);
    position: relative;
    overflow: hidden;
}

.feature-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
    transform: scaleX(0);
    transition: var(--transition-base);
}

.feature-card:hover::before {
    transform: scaleX(1);
}

.feature-card:hover {
    transform: translateY(-8px);
    border-color: var(--primary-color);
    box-shadow: var(--shadow-glow);
}

.feature-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto var(--spacing-lg);
    font-size: var(--font-size-2xl);
    color: var(--text-inverse);
}

.feature-title {
    font-size: var(--font-size-xl);
    font-weight: 700;
    margin-bottom: var(--spacing-md);
    color: var(--text-primary);
}

.feature-description {
    color: var(--text-secondary);
    line-height: 1.6;
}

/* About Section */
.about-section {
    padding: var(--spacing-3xl) 0;
    background: var(--bg-primary);
}

.about-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-3xl);
    align-items: center;
}

.about-highlights {
    margin: var(--spacing-2xl) 0;
}

.highlight-item {
    display: flex;
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
    padding: var(--spacing-lg);
    background: var(--bg-card);
    border-radius: var(--radius-lg);
    border: 1px solid var(--border-color);
}

.highlight-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-inverse);
    font-size: var(--font-size-lg);
    flex-shrink: 0;
}

.highlight-content h4 {
    font-size: var(--font-size-lg);
    font-weight: 600;
    margin-bottom: var(--spacing-xs);
    color: var(--text-primary);
}

.highlight-content p {
    color: var(--text-secondary);
    margin: 0;
}

.about-visual {
    position: relative;
}

.about-image {
    position: relative;
    border-radius: var(--radius-2xl);
    overflow: hidden;
    box-shadow: var(--shadow-xl);
}

.img-responsive {
    width: 100%;
    height: auto;
    display: block;
}

.image-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.3);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: var(--transition-base);
}

.about-image:hover .image-overlay {
    opacity: 1;
}

.play-button {
    width: 80px;
    height: 80px;
    background: var(--primary-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-inverse);
    font-size: var(--font-size-2xl);
    cursor: pointer;
    transition: var(--transition-base);
}

.play-button:hover {
    transform: scale(1.1);
    box-shadow: var(--shadow-glow);
}

/* CTA Section */
.cta-section {
    padding: var(--spacing-3xl) 0;
    background: linear-gradient(135deg, var(--bg-secondary), var(--bg-tertiary));
    position: relative;
    overflow: hidden;
}

.cta-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(ellipse at center, rgba(0, 212, 255, 0.1) 0%, transparent 70%);
}

.cta-content {
    position: relative;
    z-index: 2;
}

.cta-title {
    font-size: var(--font-size-4xl);
    font-weight: 800;
    margin-bottom: var(--spacing-lg);
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.cta-subtitle {
    font-size: var(--font-size-xl);
    color: var(--text-secondary);
    margin-bottom: var(--spacing-2xl);
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.cta-buttons {
    display: flex;
    gap: var(--spacing-lg);
    justify-content: center;
    margin-bottom: var(--spacing-2xl);
    flex-wrap: wrap;
}

.cta-features {
    display: flex;
    justify-content: center;
    gap: var(--spacing-2xl);
    flex-wrap: wrap;
}

.cta-feature {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
}

.cta-feature i {
    color: var(--success-color);
    font-size: var(--font-size-base);
}

/* Back to Top Button */
.back-to-top {
    position: fixed;
    bottom: var(--spacing-xl);
    right: var(--spacing-xl);
    width: 50px;
    height: 50px;
    background: var(--primary-color);
    border: none;
    border-radius: 50%;
    color: var(--text-inverse);
    font-size: var(--font-size-lg);
    cursor: pointer;
    opacity: 0;
    visibility: hidden;
    transform: translateY(20px);
    transition: var(--transition-base);
    z-index: var(--z-fixed);
    box-shadow: var(--shadow-lg);
}

.back-to-top.visible {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.back-to-top:hover {
    background: var(--primary-dark);
    transform: translateY(-2px);
    box-shadow: var(--shadow-glow);
}

@media (max-width: 768px) {
    .features-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
    }

    .about-content {
        grid-template-columns: 1fr;
        gap: var(--spacing-2xl);
    }

    .cta-title {
        font-size: var(--font-size-3xl);
    }

    .cta-buttons {
        flex-direction: column;
        align-items: center;
    }

    .cta-features {
        flex-direction: column;
        align-items: center;
        gap: var(--spacing-md);
    }

    .back-to-top {
        bottom: var(--spacing-lg);
        right: var(--spacing-lg);
        width: 45px;
        height: 45px;
    }
}
