/* Astragenix Dashboard Styles */

/* Dashboard Layout */
.dashboard-page {
    display: flex;
    min-height: 100vh;
    background: var(--dark-bg);
}

/* Sidebar */
.sidebar {
    width: 280px;
    background: var(--dark-surface);
    border-right: 1px solid var(--dark-border);
    display: flex;
    flex-direction: column;
    position: fixed;
    height: 100vh;
    left: 0;
    top: 0;
    z-index: 1000;
    transition: var(--transition-normal);
}

.sidebar.collapsed {
    width: 80px;
}

.sidebar-header {
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--dark-border);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.sidebar-logo {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.sidebar-logo .logo-img {
    width: 32px;
    height: 32px;
}

.sidebar-logo .logo-text {
    font-size: 1.25rem;
    font-weight: 700;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    transition: var(--transition-normal);
}

.sidebar.collapsed .logo-text {
    opacity: 0;
    width: 0;
}

.sidebar-toggle {
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: var(--spacing-sm);
    border-radius: var(--radius-sm);
    transition: var(--transition-fast);
}

.sidebar-toggle:hover {
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-primary);
}

/* Sidebar Navigation */
.sidebar-nav {
    flex: 1;
    padding: var(--spacing-lg) 0;
    overflow-y: auto;
}

.nav-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-md) var(--spacing-lg);
    color: var(--text-secondary);
    text-decoration: none;
    transition: var(--transition-fast);
    position: relative;
}

.nav-item:hover {
    background: rgba(255, 255, 255, 0.05);
    color: var(--text-primary);
}

.nav-item.active {
    background: rgba(99, 102, 241, 0.1);
    color: var(--primary-color);
}

.nav-item.active::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 3px;
    background: var(--primary-color);
}

.nav-item i {
    width: 20px;
    text-align: center;
    font-size: 1.125rem;
}

.nav-item span {
    transition: var(--transition-normal);
}

.sidebar.collapsed .nav-item span {
    opacity: 0;
    width: 0;
}

.sidebar-footer {
    padding: var(--spacing-lg);
    border-top: 1px solid var(--dark-border);
}

.nav-item.logout {
    color: var(--error-color);
}

.nav-item.logout:hover {
    background: rgba(239, 68, 68, 0.1);
}

/* Main Content */
.main-content {
    flex: 1;
    margin-left: 280px;
    display: flex;
    flex-direction: column;
    min-height: 100vh;
    transition: var(--transition-normal);
}

.sidebar.collapsed + .main-content {
    margin-left: 80px;
}

/* Dashboard Header */
.dashboard-header {
    background: var(--dark-surface);
    border-bottom: 1px solid var(--dark-border);
    padding: var(--spacing-lg) var(--spacing-xl);
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: sticky;
    top: 0;
    z-index: 100;
}

.header-left {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
}

.mobile-menu-toggle {
    display: none;
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: var(--spacing-sm);
    border-radius: var(--radius-sm);
}

.page-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.header-right {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
}

.header-stats {
    display: flex;
    gap: var(--spacing-lg);
}

.header-stats .stat-item {
    text-align: right;
}

.header-stats .stat-label {
    display: block;
    font-size: 0.75rem;
    color: var(--text-muted);
    margin-bottom: var(--spacing-xs);
}

.header-stats .stat-value {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--text-primary);
}

.user-menu {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.user-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    overflow: hidden;
    border: 2px solid var(--primary-color);
}

.user-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.user-info {
    display: flex;
    flex-direction: column;
}

.user-name {
    font-weight: 500;
    color: var(--text-primary);
    font-size: 0.875rem;
}

.user-email {
    font-size: 0.75rem;
    color: var(--text-muted);
}

/* Dashboard Content */
.dashboard-content {
    flex: 1;
    padding: var(--spacing-xl);
    max-width: 1400px;
    margin: 0 auto;
    width: 100%;
}

/* Welcome Section */
.welcome-section {
    margin-bottom: var(--spacing-xl);
}

.welcome-card {
    background: var(--gradient-primary);
    border-radius: var(--radius-xl);
    padding: var(--spacing-xl);
    display: flex;
    align-items: center;
    justify-content: space-between;
    color: white;
}

.welcome-content h2 {
    font-size: 1.75rem;
    font-weight: 700;
    margin-bottom: var(--spacing-sm);
}

.welcome-content p {
    opacity: 0.9;
    font-size: 1rem;
}

.welcome-actions {
    display: flex;
    gap: var(--spacing-md);
}

.welcome-actions .btn {
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: white;
}

.welcome-actions .btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
}

/* Stats Grid */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
}

.stat-card {
    background: var(--dark-surface);
    border: 1px solid var(--dark-border);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    transition: var(--transition-fast);
}

.stat-card:hover {
    border-color: var(--primary-color);
    transform: translateY(-2px);
}

.stat-icon {
    width: 60px;
    height: 60px;
    background: var(--gradient-primary);
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
}

.stat-info {
    flex: 1;
}

.stat-info h3 {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
}

.stat-info p {
    color: var(--text-secondary);
    font-size: 0.875rem;
}

.stat-trend {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    font-size: 0.875rem;
    font-weight: 500;
}

.stat-trend.positive {
    color: var(--success-color);
}

.stat-trend.negative {
    color: var(--error-color);
}

/* Dashboard Grid */
.dashboard-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: var(--spacing-xl);
}

/* Dashboard Cards */
.dashboard-card {
    background: var(--dark-surface);
    border: 1px solid var(--dark-border);
    border-radius: var(--radius-lg);
    overflow: hidden;
    transition: var(--transition-fast);
}

.dashboard-card:hover {
    border-color: var(--primary-color);
    box-shadow: var(--shadow-lg);
}

.card-header {
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--dark-border);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.card-header h3 {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--text-primary);
}

.card-action {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    color: var(--primary-color);
    text-decoration: none;
    font-size: 0.875rem;
    font-weight: 500;
    transition: var(--transition-fast);
}

.card-action:hover {
    color: var(--primary-dark);
}

.card-content {
    padding: var(--spacing-lg);
}

/* Investment List */
.investment-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.investment-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-md);
    background: rgba(255, 255, 255, 0.02);
    border-radius: var(--radius-md);
    border: 1px solid var(--dark-border);
}

.investment-info h4 {
    font-size: 1rem;
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
}

.investment-info p {
    color: var(--text-secondary);
    font-size: 0.875rem;
}

.investment-profit {
    text-align: right;
}

.profit-amount {
    display: block;
    font-size: 1rem;
    font-weight: 600;
    color: var(--success-color);
    margin-bottom: var(--spacing-xs);
}

.profit-percentage {
    font-size: 0.75rem;
    color: var(--text-muted);
}

/* Transaction List */
.transaction-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.transaction-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
    border-radius: var(--radius-md);
    transition: var(--transition-fast);
}

.transaction-item:hover {
    background: rgba(255, 255, 255, 0.02);
}

.transaction-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
}

.transaction-icon.deposit {
    background: rgba(16, 185, 129, 0.2);
    color: var(--success-color);
}

.transaction-icon.withdrawal {
    background: rgba(239, 68, 68, 0.2);
    color: var(--error-color);
}

.transaction-icon.profit {
    background: rgba(59, 130, 246, 0.2);
    color: #3b82f6;
}

.transaction-icon.bonus {
    background: rgba(245, 158, 11, 0.2);
    color: var(--warning-color);
}

.transaction-icon.referral {
    background: rgba(139, 92, 246, 0.2);
    color: var(--secondary-color);
}

.transaction-info {
    flex: 1;
}

.transaction-info h4 {
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
}

.transaction-info p {
    font-size: 0.75rem;
    color: var(--text-muted);
}

.transaction-amount {
    font-size: 0.875rem;
    font-weight: 600;
}

.transaction-amount.positive {
    color: var(--success-color);
}

.transaction-amount.negative {
    color: var(--error-color);
}

/* Task List */
.task-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.task-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-md);
    background: rgba(255, 255, 255, 0.02);
    border-radius: var(--radius-md);
    border: 1px solid var(--dark-border);
}

.task-item.completed {
    opacity: 0.7;
}

.task-info h4 {
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
}

.task-info p {
    font-size: 0.75rem;
    color: var(--text-muted);
}

.task-reward {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: var(--spacing-xs);
}

.reward-amount {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--success-color);
}

.btn-sm {
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: 0.75rem;
}

.status {
    font-size: 0.75rem;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-sm);
    font-weight: 500;
}

.status.claimed {
    background: rgba(16, 185, 129, 0.2);
    color: var(--success-color);
}

.status.pending {
    background: rgba(245, 158, 11, 0.2);
    color: var(--warning-color);
}

/* News List */
.news-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.news-item {
    display: flex;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
    border-radius: var(--radius-md);
    transition: var(--transition-fast);
}

.news-item:hover {
    background: rgba(255, 255, 255, 0.02);
}

.news-image {
    width: 60px;
    height: 60px;
    border-radius: var(--radius-md);
    overflow: hidden;
    flex-shrink: 0;
}

.news-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.news-content {
    flex: 1;
}

.news-content h4 {
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
    line-height: 1.4;
}

.news-content p {
    font-size: 0.75rem;
    color: var(--text-secondary);
    margin-bottom: var(--spacing-xs);
    line-height: 1.4;
}

.news-date {
    font-size: 0.75rem;
    color: var(--text-muted);
}

/* Empty State */
.empty-state {
    text-align: center;
    padding: var(--spacing-xl);
    color: var(--text-muted);
}

.empty-state i {
    font-size: 3rem;
    margin-bottom: var(--spacing-md);
    opacity: 0.5;
}

.empty-state h4 {
    font-size: 1.125rem;
    margin-bottom: var(--spacing-sm);
    color: var(--text-secondary);
}

.empty-state p {
    margin-bottom: var(--spacing-lg);
}

/* Responsive Design */
@media (max-width: 1024px) {
    .dashboard-grid {
        grid-template-columns: 1fr;
    }
    
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 768px) {
    .sidebar {
        transform: translateX(-100%);
    }
    
    .sidebar.mobile-open {
        transform: translateX(0);
    }
    
    .main-content {
        margin-left: 0;
    }
    
    .mobile-menu-toggle {
        display: block;
    }
    
    .dashboard-content {
        padding: var(--spacing-lg);
    }
    
    .welcome-card {
        flex-direction: column;
        text-align: center;
        gap: var(--spacing-lg);
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .header-stats {
        display: none;
    }
    
    .user-info {
        display: none;
    }
}

@media (max-width: 480px) {
    .dashboard-content {
        padding: var(--spacing-md);
    }
    
    .welcome-card {
        padding: var(--spacing-lg);
    }
    
    .welcome-actions {
        flex-direction: column;
        width: 100%;
    }
    
    .dashboard-header {
        padding: var(--spacing-md);
    }
}
