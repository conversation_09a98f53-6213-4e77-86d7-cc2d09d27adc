// Investment Page JavaScript

document.addEventListener('DOMContentLoaded', function() {
    // Plan selection functionality
    const planCards = document.querySelectorAll('.plan-card');
    const selectPlanBtns = document.querySelectorAll('.select-plan-btn');
    const investmentFormContainer = document.getElementById('investment-form-container');
    const investmentForm = document.getElementById('investment-form');
    const cancelBtn = document.getElementById('cancel-investment');
    
    // Calculator inputs
    const calcAmounts = document.querySelectorAll('.calc-amount');
    
    // Form elements
    const selectedPlanIdInput = document.getElementById('selected-plan-id');
    const amountInput = document.getElementById('amount');
    const amountRange = document.getElementById('amount-range');
    
    // Summary elements
    const summaryPlan = document.getElementById('summary-plan');
    const summaryRate = document.getElementById('summary-rate');
    const summaryDuration = document.getElementById('summary-duration');
    const summaryDailyProfit = document.getElementById('summary-daily-profit');
    const summaryTotalProfit = document.getElementById('summary-total-profit');
    
    // Plan data
    const plans = {
        starter: {
            name: 'Starter Plan',
            min_amount: 100,
            max_amount: 999,
            daily_rate: 1.2,
            duration: 30
        },
        professional: {
            name: 'Professional Plan',
            min_amount: 1000,
            max_amount: 9999,
            daily_rate: 1.8,
            duration: 60
        },
        enterprise: {
            name: 'Enterprise Plan',
            min_amount: 10000,
            max_amount: 100000,
            daily_rate: 2.5,
            duration: 90
        }
    };
    
    let selectedPlan = null;
    
    // Calculator functionality
    calcAmounts.forEach(input => {
        input.addEventListener('input', function() {
            const planKey = this.dataset.plan;
            const amount = parseFloat(this.value) || 0;
            const plan = plans[planKey];
            
            if (plan && amount > 0) {
                const dailyProfit = amount * (plan.daily_rate / 100);
                const totalProfit = dailyProfit * plan.duration;
                
                const planCard = this.closest('.plan-card');
                const dailyProfitElement = planCard.querySelector('.daily-profit');
                const totalProfitElement = planCard.querySelector('.total-profit');
                
                if (dailyProfitElement) {
                    dailyProfitElement.textContent = '$' + dailyProfit.toFixed(2);
                }
                if (totalProfitElement) {
                    totalProfitElement.textContent = '$' + totalProfit.toFixed(2);
                }
            }
        });
    });
    
    // Plan selection
    selectPlanBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const planKey = this.dataset.plan;
            const plan = plans[planKey];
            
            if (plan) {
                selectedPlan = planKey;
                
                // Update form
                selectedPlanIdInput.value = planKey;
                amountInput.min = plan.min_amount;
                amountInput.max = plan.max_amount;
                amountInput.value = plan.min_amount;
                
                // Update amount range display
                amountRange.textContent = `Min: $${plan.min_amount.toLocaleString()} | Max: $${plan.max_amount.toLocaleString()}`;
                
                // Update summary
                summaryPlan.textContent = plan.name;
                summaryRate.textContent = plan.daily_rate + '%';
                summaryDuration.textContent = plan.duration + ' days';
                
                // Calculate initial profits
                updateProfitCalculation();
                
                // Show form
                investmentFormContainer.style.display = 'block';
                investmentFormContainer.scrollIntoView({ behavior: 'smooth' });
                
                // Update plan card selection
                planCards.forEach(card => card.classList.remove('selected'));
                this.closest('.plan-card').classList.add('selected');
            }
        });
    });
    
    // Amount input change
    if (amountInput) {
        amountInput.addEventListener('input', updateProfitCalculation);
    }
    
    // Cancel investment
    if (cancelBtn) {
        cancelBtn.addEventListener('click', function() {
            investmentFormContainer.style.display = 'none';
            planCards.forEach(card => card.classList.remove('selected'));
            selectedPlan = null;
        });
    }
    
    // Update profit calculation
    function updateProfitCalculation() {
        if (!selectedPlan) return;
        
        const plan = plans[selectedPlan];
        const amount = parseFloat(amountInput.value) || 0;
        
        if (amount > 0) {
            const dailyProfit = amount * (plan.daily_rate / 100);
            const totalProfit = dailyProfit * plan.duration;
            
            summaryDailyProfit.textContent = '$' + dailyProfit.toFixed(2);
            summaryTotalProfit.textContent = '$' + totalProfit.toFixed(2);
        } else {
            summaryDailyProfit.textContent = '$0.00';
            summaryTotalProfit.textContent = '$0.00';
        }
    }
    
    // Form validation
    if (investmentForm) {
        investmentForm.addEventListener('submit', function(e) {
            const amount = parseFloat(amountInput.value);
            const plan = plans[selectedPlan];
            
            if (!selectedPlan) {
                e.preventDefault();
                alert('Please select an investment plan');
                return;
            }
            
            if (amount < plan.min_amount || amount > plan.max_amount) {
                e.preventDefault();
                alert(`Investment amount must be between $${plan.min_amount} and $${plan.max_amount}`);
                return;
            }
            
            // Show loading state
            const submitBtn = this.querySelector('button[type="submit"]');
            if (submitBtn) {
                submitBtn.disabled = true;
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Processing...';
            }
        });
    }
    
    // Plan card hover effects
    planCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-8px)';
        });
        
        card.addEventListener('mouseleave', function() {
            if (!this.classList.contains('selected')) {
                this.style.transform = 'translateY(0)';
            }
        });
    });
    
    // Initialize calculators with default values
    calcAmounts.forEach(input => {
        input.dispatchEvent(new Event('input'));
    });
    
    // Smooth scrolling for anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
    
    // Add loading animation to buttons
    document.querySelectorAll('.btn').forEach(btn => {
        btn.addEventListener('click', function() {
            if (this.type === 'submit' || this.classList.contains('submit-btn')) {
                const originalText = this.innerHTML;
                this.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Loading...';
                this.disabled = true;
                
                // Re-enable after 3 seconds (fallback)
                setTimeout(() => {
                    this.innerHTML = originalText;
                    this.disabled = false;
                }, 3000);
            }
        });
    });
    
    // Number formatting for inputs
    document.querySelectorAll('input[type="number"]').forEach(input => {
        input.addEventListener('blur', function() {
            const value = parseFloat(this.value);
            if (!isNaN(value)) {
                this.value = value.toFixed(2);
            }
        });
    });
    
    // Tooltip functionality
    const tooltips = document.querySelectorAll('[data-tooltip]');
    tooltips.forEach(element => {
        element.addEventListener('mouseenter', function() {
            const tooltip = document.createElement('div');
            tooltip.className = 'tooltip';
            tooltip.textContent = this.dataset.tooltip;
            document.body.appendChild(tooltip);
            
            const rect = this.getBoundingClientRect();
            tooltip.style.left = rect.left + (rect.width / 2) - (tooltip.offsetWidth / 2) + 'px';
            tooltip.style.top = rect.top - tooltip.offsetHeight - 10 + 'px';
        });
        
        element.addEventListener('mouseleave', function() {
            const tooltip = document.querySelector('.tooltip');
            if (tooltip) {
                tooltip.remove();
            }
        });
    });
    
    // Auto-save form data to localStorage
    const formInputs = document.querySelectorAll('input, select, textarea');
    formInputs.forEach(input => {
        // Load saved data
        const savedValue = localStorage.getItem('invest_form_' + input.name);
        if (savedValue && input.type !== 'hidden') {
            input.value = savedValue;
        }
        
        // Save data on change
        input.addEventListener('change', function() {
            if (this.type !== 'hidden' && this.name) {
                localStorage.setItem('invest_form_' + this.name, this.value);
            }
        });
    });
    
    // Clear saved data on successful form submission
    if (investmentForm) {
        investmentForm.addEventListener('submit', function() {
            formInputs.forEach(input => {
                if (input.name) {
                    localStorage.removeItem('invest_form_' + input.name);
                }
            });
        });
    }
});

// Utility functions
function formatNumber(num) {
    return new Intl.NumberFormat('en-US', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    }).format(num);
}

function formatCurrency(amount, currency = 'USD') {
    return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: currency
    }).format(amount);
}

// Export functions for use in other scripts
window.InvestmentPage = {
    formatNumber,
    formatCurrency
};
