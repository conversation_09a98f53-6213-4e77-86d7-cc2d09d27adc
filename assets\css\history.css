/* History Page Styles */

.history-page {
    background: linear-gradient(135deg, var(--bg-primary), var(--bg-secondary));
    min-height: 100vh;
}

.history-section {
    padding: var(--spacing-2xl) 0;
}

.history-content {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-2xl);
}

.filters-section {
    margin-bottom: var(--spacing-xl);
}

.filters-card {
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-xl);
    padding: var(--spacing-xl);
    box-shadow: var(--shadow-lg);
}

.filters-card h3 {
    font-size: var(--font-size-lg);
    font-weight: 600;
    margin-bottom: var(--spacing-lg);
    color: var(--text-primary);
}

.filters-form {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-lg);
    align-items: end;
}

.filter-group {
    display: flex;
    flex-direction: column;
}

.filter-group label {
    font-size: var(--font-size-sm);
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
}

.filter-select {
    padding: var(--spacing-sm) var(--spacing-md);
    background: var(--bg-tertiary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    color: var(--text-primary);
    font-size: var(--font-size-sm);
    transition: var(--transition-fast);
}

.filter-select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.filter-actions {
    display: flex;
    gap: var(--spacing-sm);
}

.filter-actions .btn {
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: var(--font-size-sm);
    white-space: nowrap;
}

.transactions-section {
    flex: 1;
}

.transactions-card,
.investments-card {
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-lg);
    overflow: hidden;
}

.card-header {
    padding: var(--spacing-xl);
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.card-header h3 {
    font-size: var(--font-size-xl);
    font-weight: 700;
    color: var(--text-primary);
}

.header-info {
    font-size: var(--font-size-sm);
    color: var(--text-muted);
}

.header-action {
    color: var(--primary-color);
    text-decoration: none;
    font-size: var(--font-size-sm);
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    transition: var(--transition-fast);
}

.header-action:hover {
    color: var(--primary-light);
}

.transactions-table {
    display: flex;
    flex-direction: column;
}

.table-header {
    display: grid;
    grid-template-columns: 1.5fr 1fr 1fr 1.2fr 2fr;
    gap: var(--spacing-md);
    padding: var(--spacing-lg) var(--spacing-xl);
    background: var(--bg-tertiary);
    border-bottom: 1px solid var(--border-color);
}

.header-cell {
    font-size: var(--font-size-xs);
    font-weight: 600;
    color: var(--text-muted);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.table-body {
    display: flex;
    flex-direction: column;
}

.table-row {
    display: grid;
    grid-template-columns: 1.5fr 1fr 1fr 1.2fr 2fr;
    gap: var(--spacing-md);
    padding: var(--spacing-lg) var(--spacing-xl);
    border-bottom: 1px solid var(--border-color);
    transition: var(--transition-fast);
}

.table-row:hover {
    background: var(--bg-tertiary);
}

.table-row:last-child {
    border-bottom: none;
}

.table-cell {
    display: flex;
    align-items: center;
}

.transaction-type {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.type-icon {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-sm);
    color: var(--text-inverse);
}

.type-icon.success {
    background: linear-gradient(135deg, var(--success-color), var(--success-dark));
}

.type-icon.danger {
    background: linear-gradient(135deg, var(--error-color), var(--error-dark));
}

.type-icon.primary {
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
}

.type-name {
    font-size: var(--font-size-sm);
    font-weight: 500;
    color: var(--text-primary);
    text-transform: capitalize;
}

.amount {
    font-size: var(--font-size-base);
    font-weight: 700;
}

.amount.success {
    color: var(--success-color);
}

.amount.danger {
    color: var(--error-color);
}

.amount.primary {
    color: var(--primary-color);
}

.status {
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-full);
    font-size: var(--font-size-xs);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.status.pending {
    background: rgba(245, 158, 11, 0.1);
    color: var(--warning-color);
    border: 1px solid var(--warning-color);
}

.status.completed {
    background: rgba(16, 185, 129, 0.1);
    color: var(--success-color);
    border: 1px solid var(--success-color);
}

.status.failed {
    background: rgba(239, 68, 68, 0.1);
    color: var(--error-color);
    border: 1px solid var(--error-color);
}

.status.processing {
    background: rgba(99, 102, 241, 0.1);
    color: var(--primary-color);
    border: 1px solid var(--primary-color);
}

.transaction-date {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.date {
    font-size: var(--font-size-sm);
    font-weight: 500;
    color: var(--text-primary);
}

.time {
    font-size: var(--font-size-xs);
    color: var(--text-muted);
}

.description {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    line-height: 1.4;
}

.pagination {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-xl);
    border-top: 1px solid var(--border-color);
}

.pagination-btn {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-sm) var(--spacing-md);
    background: var(--bg-tertiary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    color: var(--text-primary);
    text-decoration: none;
    font-size: var(--font-size-sm);
    font-weight: 500;
    transition: var(--transition-fast);
}

.pagination-btn:hover {
    background: var(--primary-color);
    color: var(--text-inverse);
    border-color: var(--primary-color);
}

.pagination-info {
    font-size: var(--font-size-sm);
    color: var(--text-muted);
}

.empty-state {
    text-align: center;
    padding: var(--spacing-4xl);
}

.empty-icon {
    width: 80px;
    height: 80px;
    background: var(--bg-tertiary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto var(--spacing-lg);
    color: var(--text-muted);
    font-size: var(--font-size-2xl);
}

.empty-state h3 {
    font-size: var(--font-size-xl);
    font-weight: 600;
    margin-bottom: var(--spacing-sm);
    color: var(--text-primary);
}

.empty-state p {
    color: var(--text-secondary);
    margin-bottom: var(--spacing-xl);
}

.investments-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-lg);
    padding: var(--spacing-xl);
}

.investment-item {
    background: var(--bg-tertiary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    transition: var(--transition-base);
}

.investment-item:hover {
    border-color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: var(--shadow-glow);
}

.investment-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-md);
}

.investment-header h4 {
    font-size: var(--font-size-base);
    font-weight: 600;
    color: var(--text-primary);
}

.investment-status {
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-full);
    font-size: var(--font-size-xs);
    font-weight: 600;
    text-transform: uppercase;
}

.investment-status.active {
    background: rgba(16, 185, 129, 0.1);
    color: var(--success-color);
}

.investment-status.completed {
    background: rgba(99, 102, 241, 0.1);
    color: var(--primary-color);
}

.investment-details {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.detail-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.detail-label {
    font-size: var(--font-size-sm);
    color: var(--text-muted);
}

.detail-value {
    font-size: var(--font-size-sm);
    font-weight: 500;
    color: var(--text-primary);
}

@media (max-width: 1024px) {
    .table-header,
    .table-row {
        grid-template-columns: 1fr 1fr 1fr;
    }
    
    .description-cell,
    .date-cell {
        display: none;
    }
}

@media (max-width: 768px) {
    .filters-form {
        grid-template-columns: 1fr;
    }
    
    .filter-actions {
        justify-content: center;
    }
    
    .table-header,
    .table-row {
        grid-template-columns: 1fr 1fr;
        gap: var(--spacing-sm);
    }
    
    .status-cell {
        display: none;
    }
    
    .card-header {
        flex-direction: column;
        gap: var(--spacing-sm);
        text-align: center;
    }
    
    .investments-grid {
        grid-template-columns: 1fr;
    }
    
    .pagination {
        flex-direction: column;
        gap: var(--spacing-md);
    }
}
