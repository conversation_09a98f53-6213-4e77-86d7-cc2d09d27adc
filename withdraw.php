<?php
session_start();
require_once 'config/database.php';
require_once 'includes/functions.php';
require_once 'includes/language.php';

// Определение языка
$lang = detectLanguage();
loadLanguage($lang);

// Проверка авторизации
if (!isset($_SESSION['user_id'])) {
    redirect('login.php');
}

$user = getUserById($_SESSION['user_id']);
if (!$user) {
    session_destroy();
    redirect('login.php');
}

$errors = [];
$success = '';

// Получение баланса пользователя
$balance = getUserBalance($_SESSION['user_id']);

// Настройки вывода
$withdrawal_settings = [
    'min_amount' => 10,
    'max_amount' => 10000,
    'fee_percentage' => 2,
    'processing_time' => '24 hours'
];

// Обработка формы вывода
if ($_POST) {
    $amount = floatval($_POST['amount'] ?? 0);
    $wallet_address = trim($_POST['wallet_address'] ?? '');
    $payment_method = $_POST['payment_method'] ?? '';
    
    // Валидация
    if ($amount <= 0) {
        $errors[] = t('invalid_amount');
    }
    
    if ($amount < $withdrawal_settings['min_amount']) {
        $errors[] = "Minimum withdrawal amount is {$withdrawal_settings['min_amount']} USDT";
    }
    
    if ($amount > $withdrawal_settings['max_amount']) {
        $errors[] = "Maximum withdrawal amount is {$withdrawal_settings['max_amount']} USDT";
    }
    
    if (empty($wallet_address)) {
        $errors[] = t('wallet_address') . ' ' . t('required');
    }
    
    if (empty($payment_method)) {
        $errors[] = t('payment_method') . ' ' . t('required');
    }
    
    // Расчет комиссии
    $fee = $amount * ($withdrawal_settings['fee_percentage'] / 100);
    $total_amount = $amount + $fee;
    
    // Проверка баланса
    if ($total_amount > $balance['available_balance']) {
        $errors[] = t('insufficient_balance');
    }
    
    if (empty($errors)) {
        $db = getDB();
        
        try {
            $db->beginTransaction();
            
            // Создание запроса на вывод
            $stmt = $db->prepare("
                INSERT INTO transactions (
                    user_id, type, amount, fee, wallet_address, 
                    payment_method, description, status, created_at
                ) VALUES (?, 'withdrawal', ?, ?, ?, ?, ?, 'pending', NOW())
            ");
            
            $description = "Withdrawal request - {$amount} USDT to {$payment_method}";
            $stmt->execute([
                $_SESSION['user_id'],
                $amount,
                $fee,
                $wallet_address,
                $payment_method,
                $description
            ]);
            
            $transaction_id = $db->lastInsertId();
            
            // Блокировка средств (уменьшение доступного баланса)
            $stmt = $db->prepare("
                UPDATE user_balances 
                SET available_balance = available_balance - ? 
                WHERE user_id = ?
            ");
            $stmt->execute([$total_amount, $_SESSION['user_id']]);
            
            $db->commit();
            
            // Логирование
            logAction($_SESSION['user_id'], 'withdrawal_requested', "Withdrawal request: {$amount} USDT");
            
            setFlashMessage('success', t('withdrawal_requested'));
            redirect('dashboard.php');
            
        } catch (Exception $e) {
            $db->rollBack();
            $errors[] = 'Withdrawal request failed. Please try again.';
            logAction($_SESSION['user_id'], 'withdrawal_failed', "Withdrawal failed: " . $e->getMessage());
        }
    }
}

// Получение истории выводов
$db = getDB();
$stmt = $db->prepare("
    SELECT * FROM transactions 
    WHERE user_id = ? AND type = 'withdrawal' 
    ORDER BY created_at DESC 
    LIMIT 10
");
$stmt->execute([$_SESSION['user_id']]);
$recent_withdrawals = $stmt->fetchAll();

$pageTitle = t('withdraw_funds') . ' - Astragenix';
$pageDescription = t('withdraw_description');
$currentPage = 'withdraw';
$bodyClass = 'withdraw-page';
$additionalCSS = ['assets/css/withdraw.css'];
include 'includes/header.php';
?>

<!-- Withdrawal Section -->
<section class="withdraw-section">
    <div class="container">
        <!-- Page Header -->
        <div class="page-header">
            <div class="page-header-content">
                <h1 class="page-title"><?php echo t('withdraw_funds'); ?></h1>
                <p class="page-subtitle"><?php echo t('withdraw_description'); ?></p>
                <div class="breadcrumb">
                    <a href="dashboard.php"><?php echo t('dashboard'); ?></a>
                    <span class="breadcrumb-separator">/</span>
                    <span class="breadcrumb-current"><?php echo t('withdraw_funds'); ?></span>
                </div>
            </div>
        </div>

        <div class="withdraw-content">
            <!-- Balance Info -->
            <div class="balance-info">
                <div class="balance-card">
                    <div class="balance-icon">
                        <i class="fas fa-wallet"></i>
                    </div>
                    <div class="balance-details">
                        <h3><?php echo t('available_balance'); ?></h3>
                        <p class="balance-amount"><?php echo formatCurrency($balance['available_balance']); ?></p>
                        <span class="balance-note">
                            <?php echo t('total_balance'); ?>: <?php echo formatCurrency($balance['total_balance']); ?>
                        </span>
                    </div>
                </div>
            </div>

            <!-- Error Messages -->
            <?php if (!empty($errors)): ?>
                <div class="error-messages">
                    <ul>
                        <?php foreach ($errors as $error): ?>
                            <li><?php echo h($error); ?></li>
                        <?php endforeach; ?>
                    </ul>
                </div>
            <?php endif; ?>

            <!-- Withdrawal Form -->
            <div class="withdrawal-form-container">
                <div class="form-card">
                    <div class="form-header">
                        <h3><?php echo t('withdrawal_request'); ?></h3>
                        <p><?php echo t('fill_withdrawal_details'); ?></p>
                    </div>
                    
                    <form method="POST" class="withdrawal-form" id="withdrawal-form">
                        <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                        
                        <div class="form-group">
                            <label for="amount"><?php echo t('withdrawal_amount'); ?> (USDT)</label>
                            <input type="number" 
                                   id="amount" 
                                   name="amount" 
                                   class="form-input" 
                                   step="0.01" 
                                   min="<?php echo $withdrawal_settings['min_amount']; ?>"
                                   max="<?php echo min($withdrawal_settings['max_amount'], $balance['available_balance']); ?>"
                                   required>
                            <div class="input-help">
                                Min: $<?php echo $withdrawal_settings['min_amount']; ?> | 
                                Max: $<?php echo number_format(min($withdrawal_settings['max_amount'], $balance['available_balance']), 2); ?>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label for="payment_method"><?php echo t('payment_method'); ?></label>
                            <select id="payment_method" name="payment_method" class="form-input" required>
                                <option value=""><?php echo t('select_payment_method'); ?></option>
                                <option value="USDT_TRC20">USDT (TRC20)</option>
                                <option value="USDT_ERC20">USDT (ERC20)</option>
                                <option value="BTC">Bitcoin (BTC)</option>
                                <option value="ETH">Ethereum (ETH)</option>
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label for="wallet_address"><?php echo t('wallet_address'); ?></label>
                            <input type="text" 
                                   id="wallet_address" 
                                   name="wallet_address" 
                                   class="form-input" 
                                   placeholder="<?php echo t('enter_wallet_address'); ?>"
                                   required>
                            <div class="input-help">
                                <?php echo t('double_check_wallet_address'); ?>
                            </div>
                        </div>
                        
                        <div class="withdrawal-summary" id="withdrawal-summary" style="display: none;">
                            <h4><?php echo t('withdrawal_summary'); ?></h4>
                            <div class="summary-item">
                                <span><?php echo t('withdrawal_amount'); ?>:</span>
                                <span id="summary-amount">$0.00</span>
                            </div>
                            <div class="summary-item">
                                <span><?php echo t('processing_fee'); ?> (<?php echo $withdrawal_settings['fee_percentage']; ?>%):</span>
                                <span id="summary-fee">$0.00</span>
                            </div>
                            <div class="summary-item total">
                                <span><?php echo t('total_deducted'); ?>:</span>
                                <span id="summary-total">$0.00</span>
                            </div>
                            <div class="summary-note">
                                <i class="fas fa-info-circle"></i>
                                <?php echo t('processing_time'); ?>: <?php echo $withdrawal_settings['processing_time']; ?>
                            </div>
                        </div>
                        
                        <div class="form-actions">
                            <button type="button" class="btn btn-secondary" onclick="history.back()">
                                <?php echo t('cancel'); ?>
                            </button>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-paper-plane"></i>
                                <?php echo t('submit_withdrawal'); ?>
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Recent Withdrawals -->
            <?php if (!empty($recent_withdrawals)): ?>
                <div class="recent-withdrawals">
                    <h3><?php echo t('recent_withdrawals'); ?></h3>
                    <div class="withdrawals-list">
                        <?php foreach ($recent_withdrawals as $withdrawal): ?>
                            <div class="withdrawal-item">
                                <div class="withdrawal-info">
                                    <div class="withdrawal-amount">
                                        <?php echo formatCurrency($withdrawal['amount']); ?>
                                    </div>
                                    <div class="withdrawal-method">
                                        <?php echo h($withdrawal['payment_method']); ?>
                                    </div>
                                    <div class="withdrawal-date">
                                        <?php echo date('M j, Y H:i', strtotime($withdrawal['created_at'])); ?>
                                    </div>
                                </div>
                                <div class="withdrawal-status">
                                    <span class="status <?php echo $withdrawal['status']; ?>">
                                        <?php echo t($withdrawal['status']); ?>
                                    </span>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>
</section>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const amountInput = document.getElementById('amount');
    const summaryDiv = document.getElementById('withdrawal-summary');
    const summaryAmount = document.getElementById('summary-amount');
    const summaryFee = document.getElementById('summary-fee');
    const summaryTotal = document.getElementById('summary-total');
    
    const feePercentage = <?php echo $withdrawal_settings['fee_percentage']; ?>;
    
    function updateSummary() {
        const amount = parseFloat(amountInput.value) || 0;
        
        if (amount > 0) {
            const fee = amount * (feePercentage / 100);
            const total = amount + fee;
            
            summaryAmount.textContent = '$' + amount.toFixed(2);
            summaryFee.textContent = '$' + fee.toFixed(2);
            summaryTotal.textContent = '$' + total.toFixed(2);
            
            summaryDiv.style.display = 'block';
        } else {
            summaryDiv.style.display = 'none';
        }
    }
    
    amountInput.addEventListener('input', updateSummary);
});
</script>

<?php include 'includes/footer.php'; ?>
