<?php
session_start();
require_once '../../config/database.php';
require_once '../../includes/functions.php';

// Проверка авторизации и прав администратора
if (!isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Unauthorized']);
    exit;
}

$user = getUserById($_SESSION['user_id']);
if (!$user || $user['role'] !== 'admin') {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'Access denied']);
    exit;
}

try {
    $db = getDB();
    
    // Получаем время последней проверки из сессии
    $last_check = $_SESSION['admin_last_check'] ?? time() - 300; // По умолчанию 5 минут назад
    $last_check_date = date('Y-m-d H:i:s', $last_check);
    
    // Проверяем новые транзакции
    $stmt = $db->prepare("
        SELECT COUNT(*) 
        FROM transactions 
        WHERE status = 'pending' AND created_at > ?
    ");
    $stmt->execute([$last_check_date]);
    $new_transactions = $stmt->fetchColumn();
    
    // Проверяем новых пользователей
    $stmt = $db->prepare("
        SELECT COUNT(*) 
        FROM users 
        WHERE created_at > ?
    ");
    $stmt->execute([$last_check_date]);
    $new_users = $stmt->fetchColumn();
    
    // Проверяем новые инвестиции
    $stmt = $db->prepare("
        SELECT COUNT(*) 
        FROM investments 
        WHERE created_at > ?
    ");
    $stmt->execute([$last_check_date]);
    $new_investments = $stmt->fetchColumn();
    
    // Проверяем неудачные транзакции
    $stmt = $db->prepare("
        SELECT COUNT(*) 
        FROM transactions 
        WHERE status = 'failed' AND updated_at > ?
    ");
    $stmt->execute([$last_check_date]);
    $failed_transactions = $stmt->fetchColumn();
    
    // Проверяем системные уведомления
    $notifications = [];
    
    // Проверяем низкий баланс системы (если есть такая функция)
    $stmt = $db->query("
        SELECT COALESCE(SUM(available_balance), 0) as total_user_balance
        FROM user_balances
    ");
    $total_user_balance = $stmt->fetchColumn();
    
    // Проверяем количество активных инвестиций, которые скоро завершатся
    $stmt = $db->query("
        SELECT COUNT(*) 
        FROM investments 
        WHERE status = 'active' AND end_date <= DATE_ADD(NOW(), INTERVAL 7 DAY)
    ");
    $expiring_investments = $stmt->fetchColumn();
    
    if ($expiring_investments > 0) {
        $notifications[] = [
            'type' => 'warning',
            'message' => "{$expiring_investments} investment(s) expiring within 7 days"
        ];
    }
    
    // Проверяем пользователей без активности более 30 дней
    $stmt = $db->query("
        SELECT COUNT(*) 
        FROM users 
        WHERE last_activity < DATE_SUB(NOW(), INTERVAL 30 DAY) 
        AND status = 'active'
    ");
    $inactive_users = $stmt->fetchColumn();
    
    if ($inactive_users > 10) {
        $notifications[] = [
            'type' => 'info',
            'message' => "{$inactive_users} users inactive for 30+ days"
        ];
    }
    
    // Обновляем время последней проверки
    $_SESSION['admin_last_check'] = time();
    
    $response = [
        'success' => true,
        'new_transactions' => $new_transactions,
        'new_users' => $new_users,
        'new_investments' => $new_investments,
        'failed_transactions' => $failed_transactions,
        'expiring_investments' => $expiring_investments,
        'inactive_users' => $inactive_users,
        'total_user_balance' => $total_user_balance,
        'notifications' => $notifications,
        'timestamp' => time()
    ];
    
    echo json_encode($response);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Internal server error']);
    
    // Логируем ошибку
    error_log("Error checking updates: " . $e->getMessage());
}
?>
