    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="footer-content">
            <div class="container">
                <div class="footer-grid">
                    <!-- Company Info -->
                    <div class="footer-section">
                        <div class="footer-brand">
                            <img src="assets/images/logo.png" alt="Astragenix" class="footer-logo">
                            <h3>Astragenix</h3>
                        </div>
                        <p class="footer-description">
                            <?php echo t('footer_description'); ?>
                        </p>
                        <div class="social-links">
                            <a href="#" class="social-link" aria-label="Telegram">
                                <i class="fab fa-telegram"></i>
                            </a>
                            <a href="#" class="social-link" aria-label="Twitter">
                                <i class="fab fa-twitter"></i>
                            </a>
                            <a href="#" class="social-link" aria-label="Discord">
                                <i class="fab fa-discord"></i>
                            </a>
                            <a href="#" class="social-link" aria-label="YouTube">
                                <i class="fab fa-youtube"></i>
                            </a>
                        </div>
                    </div>

                    <!-- Quick Links -->
                    <div class="footer-section">
                        <h4 class="footer-title"><?php echo t('quick_links'); ?></h4>
                        <ul class="footer-links">
                            <li><a href="about.php"><?php echo t('about'); ?></a></li>
                            <li><a href="investment-plans.php"><?php echo t('investment_plans'); ?></a></li>
                            <li><a href="faq.php"><?php echo t('faq'); ?></a></li>
                            <li><a href="contact.php"><?php echo t('contact'); ?></a></li>
                            <li><a href="news.php"><?php echo t('news'); ?></a></li>
                        </ul>
                    </div>

                    <!-- Legal -->
                    <div class="footer-section">
                        <h4 class="footer-title"><?php echo t('legal'); ?></h4>
                        <ul class="footer-links">
                            <li><a href="terms.php"><?php echo t('terms_of_service'); ?></a></li>
                            <li><a href="privacy.php"><?php echo t('privacy_policy'); ?></a></li>
                            <li><a href="risk-warning.php"><?php echo t('risk_warning'); ?></a></li>
                            <li><a href="aml-policy.php"><?php echo t('aml_policy'); ?></a></li>
                        </ul>
                    </div>

                    <!-- Support -->
                    <div class="footer-section">
                        <h4 class="footer-title"><?php echo t('support'); ?></h4>
                        <ul class="footer-links">
                            <li><a href="support.php"><?php echo t('help_center'); ?></a></li>
                            <li><a href="contact.php"><?php echo t('contact_us'); ?></a></li>
                            <li>
                                <a href="mailto:<EMAIL>">
                                    <i class="fas fa-envelope"></i>
                                    <EMAIL>
                                </a>
                            </li>
                            <li>
                                <a href="tel:+1234567890">
                                    <i class="fas fa-phone"></i>
                                    +1 (234) 567-890
                                </a>
                            </li>
                        </ul>
                    </div>
                </div>

                <!-- Footer Stats -->
                <div class="footer-stats">
                    <div class="stat-item">
                        <div class="stat-number" data-target="50000">0</div>
                        <div class="stat-label"><?php echo t('active_investors'); ?></div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number" data-target="25000000">0</div>
                        <div class="stat-label"><?php echo t('total_invested'); ?> (USDT)</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number" data-target="99.8">0</div>
                        <div class="stat-label"><?php echo t('uptime'); ?> (%)</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number" data-target="24">0</div>
                        <div class="stat-label"><?php echo t('support_hours'); ?>/7</div>
                    </div>
                </div>

                <!-- Footer Bottom -->
                <div class="footer-bottom">
                    <div class="footer-bottom-content">
                        <div class="copyright">
                            <p>&copy; <?php echo date('Y'); ?> Astragenix. <?php echo t('all_rights_reserved'); ?></p>
                        </div>
                        <div class="footer-badges">
                            <div class="security-badge">
                                <i class="fas fa-shield-alt"></i>
                                <span><?php echo t('ssl_secured'); ?></span>
                            </div>
                            <div class="security-badge">
                                <i class="fas fa-lock"></i>
                                <span><?php echo t('encrypted'); ?></span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- Back to Top Button -->
    <button class="back-to-top" id="back-to-top" aria-label="Back to top">
        <i class="fas fa-chevron-up"></i>
    </button>

    <!-- Cookie Notice -->
    <div class="cookie-notice" id="cookie-notice">
        <div class="cookie-content">
            <div class="cookie-text">
                <i class="fas fa-cookie-bite"></i>
                <span><?php echo t('cookie_notice'); ?></span>
            </div>
            <div class="cookie-actions">
                <button class="btn btn-outline btn-sm" id="cookie-settings">
                    <?php echo t('settings'); ?>
                </button>
                <button class="btn btn-primary btn-sm" id="cookie-accept">
                    <?php echo t('accept'); ?>
                </button>
            </div>
        </div>
    </div>

    <!-- Notification Container -->
    <div class="notification-container" id="notification-container"></div>

    <!-- Scripts -->
    <script src="assets/js/modern.js"></script>
    <script src="assets/js/components.js"></script>
    <?php if (isset($additionalJS)): ?>
        <?php foreach ($additionalJS as $js): ?>
            <script src="<?php echo $js; ?>"></script>
        <?php endforeach; ?>
    <?php endif; ?>

    <?php
    // Auto-include page-specific JavaScript files
    if (isset($currentPage)) {
        $jsFile = "assets/js/{$currentPage}.js";
        if (file_exists($jsFile)) {
            echo "<script src=\"{$jsFile}\"></script>\n";
        }
    }
    ?>

    <!-- Analytics (replace with your tracking code) -->
    <script>
        // Google Analytics or other tracking code
        // gtag('config', 'GA_MEASUREMENT_ID');
    </script>

    <!-- Live Chat Widget (optional) -->
    <div class="live-chat-widget" id="live-chat">
        <button class="chat-toggle" id="chat-toggle">
            <i class="fas fa-comments"></i>
            <span class="chat-badge">1</span>
        </button>
        <div class="chat-window" id="chat-window">
            <div class="chat-header">
                <h4><?php echo t('live_support'); ?></h4>
                <button class="chat-close" id="chat-close">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="chat-body">
                <div class="chat-message bot">
                    <div class="message-avatar">
                        <i class="fas fa-robot"></i>
                    </div>
                    <div class="message-content">
                        <p><?php echo t('chat_welcome'); ?></p>
                    </div>
                </div>
            </div>
            <div class="chat-footer">
                <input type="text" placeholder="<?php echo t('type_message'); ?>" class="chat-input">
                <button class="chat-send">
                    <i class="fas fa-paper-plane"></i>
                </button>
            </div>
        </div>
    </div>

    <!-- Performance Monitoring -->
    <script>
        // Monitor page load performance
        window.addEventListener('load', function() {
            const loadTime = performance.now();
            console.log(`Page loaded in ${Math.round(loadTime)}ms`);
            
            // Hide loading screen
            const loadingScreen = document.getElementById('loading-screen');
            if (loadingScreen) {
                loadingScreen.style.opacity = '0';
                setTimeout(() => {
                    loadingScreen.style.display = 'none';
                }, 500);
            }
        });
    </script>
</body>
</html>
