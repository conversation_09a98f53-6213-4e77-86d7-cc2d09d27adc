<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Astragenix Logo Generator</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: #0f172a;
            color: white;
            padding: 20px;
            text-align: center;
        }
        
        .logo-container {
            display: inline-block;
            margin: 20px;
            padding: 20px;
            background: #1e293b;
            border-radius: 10px;
            border: 1px solid #334155;
        }
        
        .logo {
            width: 64px;
            height: 64px;
            background: linear-gradient(135deg, #6366f1, #8b5cf6);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 10px;
            position: relative;
            overflow: hidden;
        }
        
        .logo::before {
            content: '★';
            font-size: 24px;
            color: white;
            text-shadow: 0 0 10px rgba(255,255,255,0.5);
        }
        
        .logo::after {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
            animation: shine 3s infinite;
        }
        
        @keyframes shine {
            0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
            50% { transform: translateX(100%) translateY(100%) rotate(45deg); }
            100% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
        }
        
        .logo-text {
            font-size: 18px;
            font-weight: bold;
            background: linear-gradient(135deg, #6366f1, #8b5cf6);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .instructions {
            max-width: 600px;
            margin: 0 auto;
            text-align: left;
            background: #1e293b;
            padding: 20px;
            border-radius: 10px;
            border: 1px solid #334155;
        }
        
        .download-btn {
            background: linear-gradient(135deg, #6366f1, #8b5cf6);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px;
            font-size: 14px;
        }
        
        .download-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(99, 102, 241, 0.3);
        }
    </style>
</head>
<body>
    <h1>Astragenix Logo</h1>
    
    <div class="logo-container">
        <div class="logo" id="logo"></div>
        <div class="logo-text">Astragenix</div>
    </div>
    
    <div class="instructions">
        <h3>Инструкции по созданию логотипа:</h3>
        <ol>
            <li><strong>Для PNG версии:</strong> Сделайте скриншот логотипа выше или используйте любой онлайн конвертер SVG в PNG</li>
            <li><strong>Для использования в проекте:</strong> Сохраните файл как <code>assets/images/logo.png</code></li>
            <li><strong>Размеры:</strong> Рекомендуемые размеры 64x64px для основного использования</li>
            <li><strong>Альтернатива:</strong> Можете использовать любой другой логотип, заменив файл</li>
        </ol>
        
        <h3>Цветовая схема:</h3>
        <ul>
            <li>Основной градиент: #6366f1 → #8b5cf6</li>
            <li>Фон: #0f172a (темно-синий)</li>
            <li>Акцент: #06b6d4 (голубой)</li>
        </ul>
        
        <h3>Быстрое создание логотипа:</h3>
        <p>Если у вас нет времени на создание логотипа, можете использовать эмодзи или текстовый логотип:</p>
        <div style="background: #334155; padding: 10px; border-radius: 5px; margin: 10px 0;">
            <code>🚀 Astragenix</code> или <code>⭐ Astragenix</code> или <code>💎 Astragenix</code>
        </div>
    </div>
    
    <button class="download-btn" onclick="downloadSVG()">Скачать SVG</button>
    <button class="download-btn" onclick="createCanvas()">Создать PNG</button>
    
    <script>
        function downloadSVG() {
            const svgContent = `<svg width="64" height="64" viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
                <defs>
                    <linearGradient id="logoGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                        <stop offset="0%" style="stop-color:#6366f1;stop-opacity:1" />
                        <stop offset="100%" style="stop-color:#8b5cf6;stop-opacity:1" />
                    </linearGradient>
                </defs>
                <circle cx="32" cy="32" r="30" fill="url(#logoGradient)"/>
                <path d="M32 16 L36 26 L46 26 L38 32 L42 42 L32 36 L22 42 L26 32 L18 26 L28 26 Z" fill="white" opacity="0.9"/>
            </svg>`;
            
            const blob = new Blob([svgContent], { type: 'image/svg+xml' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'astragenix-logo.svg';
            a.click();
            URL.revokeObjectURL(url);
        }
        
        function createCanvas() {
            const canvas = document.createElement('canvas');
            canvas.width = 64;
            canvas.height = 64;
            const ctx = canvas.getContext('2d');
            
            // Create gradient
            const gradient = ctx.createLinearGradient(0, 0, 64, 64);
            gradient.addColorStop(0, '#6366f1');
            gradient.addColorStop(1, '#8b5cf6');
            
            // Draw circle
            ctx.fillStyle = gradient;
            ctx.beginPath();
            ctx.arc(32, 32, 30, 0, 2 * Math.PI);
            ctx.fill();
            
            // Draw star
            ctx.fillStyle = 'white';
            ctx.font = '24px Arial';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText('★', 32, 32);
            
            // Download
            canvas.toBlob(function(blob) {
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = 'astragenix-logo.png';
                a.click();
                URL.revokeObjectURL(url);
            });
        }
    </script>
</body>
</html>
