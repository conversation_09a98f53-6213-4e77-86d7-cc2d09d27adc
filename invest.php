<?php
session_start();
require_once 'config/database.php';
require_once 'includes/functions.php';
require_once 'includes/language.php';

// Определение языка
$lang = detectLanguage();
loadLanguage($lang);

// Проверка авторизации
if (!isset($_SESSION['user_id'])) {
    redirect('login.php');
}

$user = getUserById($_SESSION['user_id']);
if (!$user) {
    session_destroy();
    redirect('login.php');
}

$errors = [];
$success = '';

// Получение планов инвестиций
$plans = [
    'starter' => [
        'name' => t('starter_plan'),
        'min_amount' => 100,
        'max_amount' => 999,
        'daily_rate' => 1.2,
        'duration' => 30,
        'features' => [
            t('daily_profits'),
            t('instant_withdrawal'),
            t('24_7_support')
        ]
    ],
    'professional' => [
        'name' => t('professional_plan'),
        'min_amount' => 1000,
        'max_amount' => 9999,
        'daily_rate' => 1.8,
        'duration' => 60,
        'features' => [
            t('daily_profits'),
            t('instant_withdrawal'),
            t('priority_support'),
            t('bonus_rewards')
        ]
    ],
    'enterprise' => [
        'name' => t('enterprise_plan'),
        'min_amount' => 10000,
        'max_amount' => 100000,
        'daily_rate' => 2.5,
        'duration' => 90,
        'features' => [
            t('daily_profits'),
            t('instant_withdrawal'),
            t('vip_support'),
            t('exclusive_bonuses'),
            t('personal_manager')
        ]
    ]
];

// Обработка формы инвестиции
if ($_POST) {
    $plan_id = $_POST['plan_id'] ?? '';
    $amount = floatval($_POST['amount'] ?? 0);
    
    // Валидация
    if (empty($plan_id) || !isset($plans[$plan_id])) {
        $errors[] = 'Invalid investment plan selected';
    }
    
    if ($amount <= 0) {
        $errors[] = t('invalid_amount');
    }
    
    if (isset($plans[$plan_id])) {
        $plan = $plans[$plan_id];
        if ($amount < $plan['min_amount'] || $amount > $plan['max_amount']) {
            $errors[] = "Amount must be between {$plan['min_amount']} and {$plan['max_amount']} USDT";
        }
    }
    
    // Проверка баланса
    $balance = getUserBalance($_SESSION['user_id']);
    if ($amount > $balance['available_balance']) {
        $errors[] = t('insufficient_balance');
    }
    
    if (empty($errors)) {
        $db = getDB();
        
        try {
            $db->beginTransaction();
            
            // Создание инвестиции
            $stmt = $db->prepare("
                INSERT INTO investments (
                    user_id, plan_id, amount, daily_rate, duration_days, 
                    start_date, end_date, status, created_at
                ) VALUES (?, ?, ?, ?, ?, NOW(), DATE_ADD(NOW(), INTERVAL ? DAY), 'active', NOW())
            ");
            
            $stmt->execute([
                $_SESSION['user_id'],
                $plan_id,
                $amount,
                $plans[$plan_id]['daily_rate'],
                $plans[$plan_id]['duration'],
                $plans[$plan_id]['duration']
            ]);
            
            $investment_id = $db->lastInsertId();
            
            // Списание средств с баланса
            $stmt = $db->prepare("
                INSERT INTO transactions (
                    user_id, type, amount, description, status, 
                    reference_id, reference_type, created_at
                ) VALUES (?, 'investment', ?, ?, 'completed', ?, 'investment', NOW())
            ");
            
            $description = "Investment in {$plans[$plan_id]['name']} - {$amount} USDT";
            $stmt->execute([
                $_SESSION['user_id'],
                -$amount, // Отрицательная сумма для списания
                $description,
                $investment_id
            ]);
            
            $db->commit();
            
            // Логирование
            logAction($_SESSION['user_id'], 'investment_created', "Created investment: {$amount} USDT in {$plan_id} plan");
            
            setFlashMessage('success', t('investment_successful'));
            redirect('dashboard.php');
            
        } catch (Exception $e) {
            $db->rollBack();
            $errors[] = 'Investment creation failed. Please try again.';
            logAction($_SESSION['user_id'], 'investment_failed', "Investment failed: " . $e->getMessage());
        }
    }
}

// Получение баланса пользователя
$balance = getUserBalance($_SESSION['user_id']);

$pageTitle = t('make_investment') . ' - Astragenix';
$pageDescription = t('invest_description');
$currentPage = 'invest';
$bodyClass = 'invest-page';
$additionalCSS = ['assets/css/invest.css'];
include 'includes/header.php';
?>

<!-- Investment Section -->
<section class="invest-section">
    <div class="container">
        <!-- Page Header -->
        <div class="page-header">
            <div class="page-header-content">
                <h1 class="page-title"><?php echo t('make_investment'); ?></h1>
                <p class="page-subtitle"><?php echo t('invest_description'); ?></p>
                <div class="breadcrumb">
                    <a href="dashboard.php"><?php echo t('dashboard'); ?></a>
                    <span class="breadcrumb-separator">/</span>
                    <span class="breadcrumb-current"><?php echo t('make_investment'); ?></span>
                </div>
            </div>
        </div>

        <!-- Balance Info -->
        <div class="balance-info">
            <div class="balance-card">
                <div class="balance-icon">
                    <i class="fas fa-wallet"></i>
                </div>
                <div class="balance-details">
                    <h3><?php echo t('total_balance'); ?></h3>
                    <p class="balance-amount"><?php echo formatCurrency($balance['total_balance']); ?></p>
                    <span class="balance-available">
                        <?php echo t('available'); ?>: <?php echo formatCurrency($balance['available_balance']); ?>
                    </span>
                </div>
            </div>
        </div>

        <!-- Error Messages -->
        <?php if (!empty($errors)): ?>
            <div class="error-messages">
                <ul>
                    <?php foreach ($errors as $error): ?>
                        <li><?php echo h($error); ?></li>
                    <?php endforeach; ?>
                </ul>
            </div>
        <?php endif; ?>

        <!-- Investment Plans -->
        <div class="investment-plans">
            <h2 class="section-title"><?php echo t('choose_plan'); ?></h2>
            <div class="plans-grid">
                <?php foreach ($plans as $plan_key => $plan): ?>
                    <div class="plan-card <?php echo $plan_key === 'professional' ? 'popular' : ''; ?>" data-plan="<?php echo $plan_key; ?>">
                        <?php if ($plan_key === 'professional'): ?>
                            <div class="plan-badge"><?php echo t('most_popular'); ?></div>
                        <?php endif; ?>
                        
                        <div class="plan-header">
                            <h3 class="plan-name"><?php echo $plan['name']; ?></h3>
                            <div class="plan-rate">
                                <span class="rate-value"><?php echo $plan['daily_rate']; ?>%</span>
                                <span class="rate-period"><?php echo t('daily'); ?></span>
                            </div>
                        </div>
                        
                        <div class="plan-details">
                            <div class="plan-range">
                                <span class="range-label"><?php echo t('investment_range'); ?>:</span>
                                <span class="range-value">
                                    $<?php echo number_format($plan['min_amount']); ?> - 
                                    $<?php echo number_format($plan['max_amount']); ?>
                                </span>
                            </div>
                            
                            <div class="plan-duration">
                                <span class="duration-label"><?php echo t('duration'); ?>:</span>
                                <span class="duration-value"><?php echo $plan['duration']; ?> <?php echo t('days'); ?></span>
                            </div>
                        </div>
                        
                        <div class="plan-features">
                            <?php foreach ($plan['features'] as $feature): ?>
                                <div class="feature-item">
                                    <i class="fas fa-check"></i>
                                    <span><?php echo $feature; ?></span>
                                </div>
                            <?php endforeach; ?>
                        </div>
                        
                        <div class="plan-calculator">
                            <div class="calculator-input">
                                <label><?php echo t('investment_amount'); ?> (USDT)</label>
                                <input type="number" 
                                       class="calc-amount" 
                                       data-plan="<?php echo $plan_key; ?>"
                                       min="<?php echo $plan['min_amount']; ?>" 
                                       max="<?php echo $plan['max_amount']; ?>"
                                       value="<?php echo $plan['min_amount']; ?>">
                            </div>
                            
                            <div class="calculator-results">
                                <div class="result-item">
                                    <span class="result-label"><?php echo t('daily_profit'); ?>:</span>
                                    <span class="daily-profit">$<?php echo number_format($plan['min_amount'] * $plan['daily_rate'] / 100, 2); ?></span>
                                </div>
                                <div class="result-item">
                                    <span class="result-label"><?php echo t('total_profit'); ?>:</span>
                                    <span class="total-profit">$<?php echo number_format($plan['min_amount'] * $plan['daily_rate'] / 100 * $plan['duration'], 2); ?></span>
                                </div>
                            </div>
                        </div>
                        
                        <button type="button" class="btn btn-primary select-plan-btn" data-plan="<?php echo $plan_key; ?>">
                            <?php echo t('select_plan'); ?>
                        </button>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>

        <!-- Investment Form -->
        <div class="investment-form-container" id="investment-form-container" style="display: none;">
            <div class="form-card">
                <div class="form-header">
                    <h3><?php echo t('confirm_investment'); ?></h3>
                    <p><?php echo t('review_investment_details'); ?></p>
                </div>
                
                <form method="POST" class="investment-form" id="investment-form">
                    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                    <input type="hidden" name="plan_id" id="selected-plan-id">
                    
                    <div class="form-group">
                        <label for="amount"><?php echo t('investment_amount'); ?> (USDT)</label>
                        <input type="number" 
                               id="amount" 
                               name="amount" 
                               class="form-input" 
                               step="0.01" 
                               required>
                        <div class="input-help">
                            <span id="amount-range"></span>
                        </div>
                    </div>
                    
                    <div class="investment-summary">
                        <div class="summary-item">
                            <span><?php echo t('selected_plan'); ?>:</span>
                            <span id="summary-plan"></span>
                        </div>
                        <div class="summary-item">
                            <span><?php echo t('daily_return'); ?>:</span>
                            <span id="summary-rate"></span>
                        </div>
                        <div class="summary-item">
                            <span><?php echo t('duration'); ?>:</span>
                            <span id="summary-duration"></span>
                        </div>
                        <div class="summary-item">
                            <span><?php echo t('expected_daily_profit'); ?>:</span>
                            <span id="summary-daily-profit"></span>
                        </div>
                        <div class="summary-item total">
                            <span><?php echo t('total_expected_profit'); ?>:</span>
                            <span id="summary-total-profit"></span>
                        </div>
                    </div>
                    
                    <div class="form-actions">
                        <button type="button" class="btn btn-secondary" id="cancel-investment">
                            <?php echo t('cancel'); ?>
                        </button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-check"></i>
                            <?php echo t('confirm_investment'); ?>
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</section>

<?php include 'includes/footer.php'; ?>
