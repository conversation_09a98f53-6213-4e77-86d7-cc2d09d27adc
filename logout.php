<?php
session_start();
require_once 'config/database.php';
require_once 'includes/functions.php';

// Логируем выход пользователя
if (isset($_SESSION['user_id'])) {
    logAction($_SESSION['user_id'], 'user_logout', 'User logged out');
}

// Удаляем токен "Запомнить меня"
if (isset($_COOKIE['remember_token'])) {
    // Удаляем токен из базы данных
    if (isset($_SESSION['user_id'])) {
        $db = getDB();
        $stmt = $db->prepare("UPDATE users SET remember_token = NULL WHERE id = ?");
        $stmt->execute([$_SESSION['user_id']]);
    }
    
    // Удаляем cookie
    setcookie('remember_token', '', time() - 3600, '/', '', true, true);
}

// Очищаем сессию
session_destroy();

// Перенаправляем на главную страницу
redirect('index.php');
?>
