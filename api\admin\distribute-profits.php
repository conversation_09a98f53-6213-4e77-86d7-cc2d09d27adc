<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type, X-Requested-With');

session_start();
require_once '../../config/database.php';
require_once '../../includes/functions.php';

// Проверка авторизации и прав администратора
if (!isset($_SESSION['user_id']) || !isAdmin($_SESSION['user_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Unauthorized']);
    exit;
}

// Проверка метода запроса
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

// Получение данных из формы
$profitPercentage = floatval($_POST['profit_percentage'] ?? 0);
$note = trim($_POST['note'] ?? '');

// Валидация
if ($profitPercentage <= 0 || $profitPercentage > 10) {
    echo json_encode(['success' => false, 'message' => 'Invalid profit percentage. Must be between 0.1 and 10.']);
    exit;
}

try {
    $db = getDB();
    
    // Начинаем транзакцию БД
    $db->beginTransaction();
    
    // Получаем все активные инвестиции
    $stmt = $db->prepare("
        SELECT i.*, u.email, u.first_name, u.last_name
        FROM investments i
        JOIN users u ON i.user_id = u.id
        WHERE i.status = 'active' AND u.status = 'active'
        ORDER BY i.amount DESC
    ");
    $stmt->execute();
    $investments = $stmt->fetchAll();
    
    if (empty($investments)) {
        echo json_encode(['success' => false, 'message' => 'No active investments found']);
        exit;
    }
    
    $totalDistributed = 0;
    $investorCount = 0;
    $distributionDetails = [];
    
    // Обрабатываем каждую инвестицию
    foreach ($investments as $investment) {
        $profitAmount = $investment['amount'] * ($profitPercentage / 100);
        
        // Добавляем прибыль в транзакции
        $stmt = $db->prepare("
            INSERT INTO transactions (
                user_id, investment_id, type, amount, description, status, created_at
            ) VALUES (?, ?, 'profit', ?, ?, 'completed', NOW())
        ");
        
        $description = "Daily profit distribution ({$profitPercentage}%)";
        if ($note) {
            $description .= " - " . $note;
        }
        
        $stmt->execute([
            $investment['user_id'],
            $investment['id'],
            $profitAmount,
            $description
        ]);
        
        // Обновляем общую прибыль по инвестиции
        $stmt = $db->prepare("
            UPDATE investments 
            SET total_profit = total_profit + ?, last_profit_date = CURDATE()
            WHERE id = ?
        ");
        $stmt->execute([$profitAmount, $investment['id']]);
        
        // Обновляем ежедневную прибыль (если изменился процент)
        $newDailyProfit = $investment['amount'] * ($profitPercentage / 100);
        $stmt = $db->prepare("
            UPDATE investments 
            SET daily_profit = ?
            WHERE id = ?
        ");
        $stmt->execute([$newDailyProfit, $investment['id']]);
        
        $totalDistributed += $profitAmount;
        $investorCount++;
        
        $distributionDetails[] = [
            'user_id' => $investment['user_id'],
            'email' => $investment['email'],
            'name' => $investment['first_name'] . ' ' . $investment['last_name'],
            'investment_amount' => $investment['amount'],
            'profit_amount' => $profitAmount
        ];
    }
    
    // Обрабатываем реферальные комиссии
    $totalReferralCommissions = 0;
    foreach ($distributionDetails as $detail) {
        $referralCommissions = processReferralCommissions(
            $detail['user_id'], 
            $detail['profit_amount'], 
            'profit_referral'
        );
        $totalReferralCommissions += $referralCommissions;
    }
    
    // Записываем общую статистику распределения
    $stmt = $db->prepare("
        INSERT INTO profit_distributions (
            admin_id, profit_percentage, total_amount, investor_count, 
            referral_commissions, note, created_at
        ) VALUES (?, ?, ?, ?, ?, ?, NOW())
    ");
    $stmt->execute([
        $_SESSION['user_id'],
        $profitPercentage,
        $totalDistributed,
        $investorCount,
        $totalReferralCommissions,
        $note
    ]);
    
    // Логируем действие администратора
    logAction($_SESSION['user_id'], 'admin_profit_distribution', 
              "Distributed {$totalDistributed} USDT to {$investorCount} investors at {$profitPercentage}%");
    
    // Подтверждаем транзакцию БД
    $db->commit();
    
    echo json_encode([
        'success' => true,
        'message' => "Successfully distributed {$totalDistributed} USDT to {$investorCount} investors",
        'data' => [
            'total_distributed' => $totalDistributed,
            'investor_count' => $investorCount,
            'profit_percentage' => $profitPercentage,
            'referral_commissions' => $totalReferralCommissions,
            'distribution_details' => $distributionDetails
        ]
    ]);
    
} catch (Exception $e) {
    // Откатываем транзакцию БД в случае ошибки
    if ($db->inTransaction()) {
        $db->rollback();
    }
    
    error_log("Error distributing profits: " . $e->getMessage());
    
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Internal server error: ' . $e->getMessage()
    ]);
}

// Функция для обработки реферальных комиссий
function processReferralCommissions($userId, $profitAmount, $type = 'referral') {
    $db = getDB();
    $totalCommissions = 0;
    
    try {
        // Получаем всех рефереров пользователя
        $stmt = $db->prepare("
            SELECT referrer_id, level, commission_rate
            FROM referrals 
            WHERE referred_id = ? AND status = 'active'
            ORDER BY level ASC
        ");
        $stmt->execute([$userId]);
        $referrers = $stmt->fetchAll();
        
        foreach ($referrers as $referrer) {
            $commissionAmount = $profitAmount * $referrer['commission_rate'];
            
            if ($commissionAmount > 0) {
                // Добавляем реферальную комиссию
                $stmt = $db->prepare("
                    INSERT INTO transactions (
                        user_id, type, amount, description, status, created_at
                    ) VALUES (?, ?, ?, ?, 'completed', NOW())
                ");
                
                $description = "Referral commission (Level {$referrer['level']}) from profit distribution";
                $stmt->execute([$referrer['referrer_id'], $type, $commissionAmount, $description]);
                
                // Обновляем общую сумму заработанных комиссий
                $stmt = $db->prepare("
                    UPDATE referrals 
                    SET total_earned = total_earned + ?
                    WHERE referrer_id = ? AND referred_id = ?
                ");
                $stmt->execute([$commissionAmount, $referrer['referrer_id'], $userId]);
                
                $totalCommissions += $commissionAmount;
            }
        }
        
    } catch (Exception $e) {
        error_log("Error processing referral commissions: " . $e->getMessage());
    }
    
    return $totalCommissions;
}

// Создаем таблицу для статистики распределений, если её нет
function createProfitDistributionsTable() {
    $db = getDB();
    
    $sql = "
    CREATE TABLE IF NOT EXISTS profit_distributions (
        id INT PRIMARY KEY AUTO_INCREMENT,
        admin_id INT NOT NULL,
        profit_percentage DECIMAL(5,2) NOT NULL,
        total_amount DECIMAL(15,2) NOT NULL,
        investor_count INT NOT NULL,
        referral_commissions DECIMAL(15,2) DEFAULT 0,
        note TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (admin_id) REFERENCES users(id) ON DELETE CASCADE
    )";
    
    $db->exec($sql);
}

// Создаем таблицу при первом запуске
createProfitDistributionsTable();
?>
