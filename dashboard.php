<?php
session_start();
require_once 'config/database.php';
require_once 'includes/functions.php';
require_once 'includes/language.php';

// Проверка авторизации
if (!isset($_SESSION['user_id'])) {
    redirect('login.php');
}

// Определение языка
$lang = detectLanguage();
loadLanguage($lang);

// Получение данных пользователя
$user = getUserById($_SESSION['user_id']);
if (!$user) {
    session_destroy();
    redirect('login.php');
}

// Получение баланса и статистики
$balance = getUserBalance($user['id']);
$investments = getUserInvestments($user['id']);
$recentTransactions = getUserTransactions($user['id'], 10);

// Получение статистики рефералов
$db = getDB();
$stmt = $db->prepare("
    SELECT COUNT(*) as total_referrals,
           COALESCE(SUM(total_earned), 0) as total_referral_earnings
    FROM referrals 
    WHERE referrer_id = ? AND status = 'active'
");
$stmt->execute([$user['id']]);
$referralStats = $stmt->fetch();

// Получение активных заданий
$stmt = $db->prepare("
    SELECT t.*, ut.progress, ut.completed_at, ut.reward_claimed
    FROM tasks t
    LEFT JOIN user_tasks ut ON t.id = ut.task_id AND ut.user_id = ?
    WHERE t.is_active = 1
    ORDER BY t.reward_amount DESC
    LIMIT 5
");
$stmt->execute([$user['id']]);
$activeTasks = $stmt->fetchAll();

// Получение последних новостей
$stmt = $db->prepare("
    SELECT * FROM news 
    WHERE is_published = 1 
    ORDER BY published_at DESC 
    LIMIT 3
");
$stmt->execute();
$latestNews = $stmt->fetchAll();

// Расчет общей статистики
$totalInvested = array_sum(array_column($investments, 'amount'));
$totalProfit = $balance['profits'];
$activeInvestmentsCount = count($investments);
?>
<?php
$pageTitle = t('dashboard') . ' - Astragenix';
$pageDescription = t('dashboard_description');
$currentPage = 'dashboard';
$bodyClass = 'dashboard-page';
$additionalCSS = ['assets/css/dashboard.css'];
include 'includes/header.php';
?>
    <!-- Sidebar -->
    <aside class="sidebar">
        <div class="sidebar-header">
            <div class="sidebar-logo">
                <img src="assets/images/logo.png" alt="Astragenix" class="logo-img">
                <span class="logo-text">Astragenix</span>
            </div>
            <button class="sidebar-toggle" id="sidebar-toggle">
                <i class="fas fa-bars"></i>
            </button>
        </div>
        
        <nav class="sidebar-nav">
            <a href="dashboard.php" class="nav-item active">
                <i class="fas fa-tachometer-alt"></i>
                <span><?php echo t('dashboard'); ?></span>
            </a>
            <a href="investments.php" class="nav-item">
                <i class="fas fa-chart-line"></i>
                <span><?php echo t('investments'); ?></span>
            </a>
            <a href="mining.php" class="nav-item">
                <i class="fas fa-pickaxe"></i>
                <span><?php echo t('mining'); ?></span>
            </a>
            <a href="transactions.php" class="nav-item">
                <i class="fas fa-exchange-alt"></i>
                <span><?php echo t('transactions'); ?></span>
            </a>
            <a href="referrals.php" class="nav-item">
                <i class="fas fa-users"></i>
                <span><?php echo t('referrals'); ?></span>
            </a>
            <a href="tasks.php" class="nav-item">
                <i class="fas fa-tasks"></i>
                <span><?php echo t('tasks'); ?></span>
            </a>
            <a href="support.php" class="nav-item">
                <i class="fas fa-headset"></i>
                <span><?php echo t('support'); ?></span>
            </a>
            <a href="profile.php" class="nav-item">
                <i class="fas fa-user"></i>
                <span><?php echo t('profile'); ?></span>
            </a>
        </nav>
        
        <div class="sidebar-footer">
            <a href="logout.php" class="nav-item logout">
                <i class="fas fa-sign-out-alt"></i>
                <span><?php echo t('logout'); ?></span>
            </a>
        </div>
    </aside>
    
    <!-- Main Content -->
    <main class="main-content">
        <!-- Header -->
        <header class="dashboard-header">
            <div class="header-left">
                <button class="mobile-menu-toggle" id="mobile-menu-toggle">
                    <i class="fas fa-bars"></i>
                </button>
                <h1 class="page-title"><?php echo t('dashboard'); ?></h1>
            </div>
            
            <div class="header-right">
                <div class="header-stats">
                    <div class="stat-item">
                        <span class="stat-label"><?php echo t('total_balance'); ?></span>
                        <span class="stat-value"><?php echo formatCurrency($balance['total_balance']); ?></span>
                    </div>
                </div>
                
                <div class="user-menu">
                    <div class="user-avatar">
                        <img src="<?php echo $user['avatar'] ?: 'assets/images/default-avatar.png'; ?>" 
                             alt="<?php echo h($user['first_name']); ?>">
                    </div>
                    <div class="user-info">
                        <span class="user-name"><?php echo h($user['first_name'] . ' ' . $user['last_name']); ?></span>
                        <span class="user-email"><?php echo h($user['email']); ?></span>
                    </div>
                </div>
            </div>
        </header>
        
        <!-- Dashboard Content -->
        <div class="dashboard-content">
            <!-- Welcome Section -->
            <div class="welcome-section animate-fade-in">
                <div class="welcome-card">
                    <div class="welcome-content">
                        <h2><?php echo t('welcome_back'); ?>, <?php echo h($user['first_name']); ?>!</h2>
                        <p><?php echo t('dashboard_welcome_message'); ?></p>
                    </div>
                    <div class="welcome-actions">
                        <a href="invest.php" class="btn btn-primary">
                            <i class="fas fa-plus"></i>
                            <?php echo t('make_investment'); ?>
                        </a>
                        <a href="withdraw.php" class="btn btn-outline">
                            <i class="fas fa-money-bill-wave"></i>
                            <?php echo t('withdraw_funds'); ?>
                        </a>
                    </div>
                </div>
            </div>
            
            <!-- Stats Grid -->
            <div class="stats-grid animate-slide-up">
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-wallet"></i>
                    </div>
                    <div class="stat-info">
                        <h3><?php echo formatCurrency($balance['total_balance']); ?></h3>
                        <p><?php echo t('total_balance'); ?></p>
                    </div>
                    <div class="stat-trend positive">
                        <i class="fas fa-arrow-up"></i>
                        <span>+12.5%</span>
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <div class="stat-info">
                        <h3><?php echo $activeInvestmentsCount; ?></h3>
                        <p><?php echo t('active_investments'); ?></p>
                    </div>
                    <div class="stat-trend positive">
                        <i class="fas fa-arrow-up"></i>
                        <span>+2</span>
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-coins"></i>
                    </div>
                    <div class="stat-info">
                        <h3><?php echo formatCurrency($totalProfit); ?></h3>
                        <p><?php echo t('total_earned'); ?></p>
                    </div>
                    <div class="stat-trend positive">
                        <i class="fas fa-arrow-up"></i>
                        <span>+8.3%</span>
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="stat-info">
                        <h3><?php echo $referralStats['total_referrals']; ?></h3>
                        <p><?php echo t('referrals'); ?></p>
                    </div>
                    <div class="stat-trend positive">
                        <i class="fas fa-arrow-up"></i>
                        <span>+<?php echo formatCurrency($referralStats['total_referral_earnings']); ?></span>
                    </div>
                </div>
            </div>
            
            <!-- Main Dashboard Grid -->
            <div class="dashboard-grid">
                <!-- Investment Overview -->
                <div class="dashboard-card animate-fade-in-left">
                    <div class="card-header">
                        <h3><?php echo t('investment_overview'); ?></h3>
                        <a href="investments.php" class="card-action">
                            <?php echo t('view_all'); ?>
                            <i class="fas fa-arrow-right"></i>
                        </a>
                    </div>
                    <div class="card-content">
                        <?php if (!empty($investments)): ?>
                            <div class="investment-list">
                                <?php foreach (array_slice($investments, 0, 3) as $investment): ?>
                                    <div class="investment-item">
                                        <div class="investment-info">
                                            <h4><?php echo ucfirst($investment['plan_id']); ?> Plan</h4>
                                            <p><?php echo formatCurrency($investment['amount']); ?></p>
                                        </div>
                                        <div class="investment-profit">
                                            <span class="profit-amount">+<?php echo formatCurrency($investment['total_profit']); ?></span>
                                            <span class="profit-percentage">+<?php echo number_format(($investment['total_profit'] / $investment['amount']) * 100, 2); ?>%</span>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        <?php else: ?>
                            <div class="empty-state">
                                <i class="fas fa-chart-line"></i>
                                <h4><?php echo t('no_investments_yet'); ?></h4>
                                <p><?php echo t('start_investing_today'); ?></p>
                                <a href="invest.php" class="btn btn-primary">
                                    <?php echo t('make_first_investment'); ?>
                                </a>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
                
                <!-- Recent Transactions -->
                <div class="dashboard-card animate-fade-in-right">
                    <div class="card-header">
                        <h3><?php echo t('recent_transactions'); ?></h3>
                        <a href="transactions.php" class="card-action">
                            <?php echo t('view_all'); ?>
                            <i class="fas fa-arrow-right"></i>
                        </a>
                    </div>
                    <div class="card-content">
                        <?php if (!empty($recentTransactions)): ?>
                            <div class="transaction-list">
                                <?php foreach (array_slice($recentTransactions, 0, 5) as $transaction): ?>
                                    <div class="transaction-item">
                                        <div class="transaction-icon <?php echo $transaction['type']; ?>">
                                            <i class="fas fa-<?php echo getTransactionIcon($transaction['type']); ?>"></i>
                                        </div>
                                        <div class="transaction-info">
                                            <h4><?php echo t($transaction['type']); ?></h4>
                                            <p><?php echo formatDate($transaction['created_at']); ?></p>
                                        </div>
                                        <div class="transaction-amount <?php echo $transaction['type'] === 'withdrawal' ? 'negative' : 'positive'; ?>">
                                            <?php echo $transaction['type'] === 'withdrawal' ? '-' : '+'; ?>
                                            <?php echo formatCurrency($transaction['amount']); ?>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        <?php else: ?>
                            <div class="empty-state">
                                <i class="fas fa-exchange-alt"></i>
                                <h4><?php echo t('no_transactions_yet'); ?></h4>
                                <p><?php echo t('transactions_will_appear_here'); ?></p>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
                
                <!-- Active Tasks -->
                <div class="dashboard-card animate-fade-in-left">
                    <div class="card-header">
                        <h3><?php echo t('active_tasks'); ?></h3>
                        <a href="tasks.php" class="card-action">
                            <?php echo t('view_all'); ?>
                            <i class="fas fa-arrow-right"></i>
                        </a>
                    </div>
                    <div class="card-content">
                        <div class="task-list">
                            <?php foreach ($activeTasks as $task): ?>
                                <div class="task-item <?php echo $task['completed_at'] ? 'completed' : ''; ?>">
                                    <div class="task-info">
                                        <h4><?php echo h($task['name']); ?></h4>
                                        <p><?php echo h($task['description']); ?></p>
                                    </div>
                                    <div class="task-reward">
                                        <span class="reward-amount">+<?php echo formatCurrency($task['reward_amount']); ?></span>
                                        <?php if ($task['completed_at'] && !$task['reward_claimed']): ?>
                                            <button class="btn btn-sm btn-primary claim-reward" data-task-id="<?php echo $task['id']; ?>">
                                                <?php echo t('claim'); ?>
                                            </button>
                                        <?php elseif ($task['completed_at']): ?>
                                            <span class="status claimed"><?php echo t('claimed'); ?></span>
                                        <?php else: ?>
                                            <span class="status pending"><?php echo t('pending'); ?></span>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
                
                <!-- Latest News -->
                <div class="dashboard-card animate-fade-in-right">
                    <div class="card-header">
                        <h3><?php echo t('latest_news'); ?></h3>
                        <a href="news.php" class="card-action">
                            <?php echo t('view_all'); ?>
                            <i class="fas fa-arrow-right"></i>
                        </a>
                    </div>
                    <div class="card-content">
                        <div class="news-list">
                            <?php foreach ($latestNews as $news): ?>
                                <div class="news-item">
                                    <div class="news-image">
                                        <img src="<?php echo $news['image'] ?: 'assets/images/default-news.jpg'; ?>" 
                                             alt="<?php echo h($news['title']); ?>">
                                    </div>
                                    <div class="news-content">
                                        <h4><?php echo h($news['title']); ?></h4>
                                        <p><?php echo h($news['excerpt']); ?></p>
                                        <span class="news-date"><?php echo formatDate($news['published_at']); ?></span>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>
    
<?php
function getTransactionIcon($type) {
    $icons = [
        'deposit' => 'arrow-down',
        'withdrawal' => 'arrow-up',
        'profit' => 'chart-line',
        'bonus' => 'gift',
        'referral' => 'users'
    ];
    return $icons[$type] ?? 'exchange-alt';
}

include 'includes/footer.php';
?>
