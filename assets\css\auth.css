/* Authentication Pages Styles */

.auth-page {
    background: linear-gradient(135deg, var(--bg-primary), var(--bg-secondary));
    min-height: 100vh;
    overflow: hidden;
}

.auth-section {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-xl) 0;
    position: relative;
}

.auth-container {
    position: relative;
    width: 100%;
    max-width: 450px;
    margin: 0 auto;
}

.auth-background {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1;
    overflow: hidden;
}

.stars {
    position: absolute;
    width: 100%;
    height: 100%;
    background: transparent;
}

.stars::before,
.stars::after {
    content: '';
    position: absolute;
    width: 2px;
    height: 2px;
    background: var(--primary-color);
    border-radius: 50%;
    box-shadow: 
        25px 25px var(--primary-color),
        50px 75px var(--accent-color),
        75px 25px var(--primary-light),
        100px 50px var(--accent-color),
        125px 75px var(--primary-color),
        150px 25px var(--accent-color),
        175px 50px var(--primary-light),
        200px 75px var(--primary-color),
        225px 25px var(--accent-color),
        250px 50px var(--primary-light),
        275px 75px var(--primary-color),
        300px 25px var(--accent-color),
        325px 50px var(--primary-light),
        350px 75px var(--primary-color),
        375px 25px var(--accent-color),
        400px 50px var(--primary-light),
        425px 75px var(--primary-color),
        450px 25px var(--accent-color),
        475px 50px var(--primary-light),
        500px 75px var(--primary-color);
    animation: sparkle 3s linear infinite;
}

.stars::after {
    animation-delay: 1.5s;
}

@keyframes sparkle {
    0%, 100% { opacity: 0; }
    50% { opacity: 1; }
}

.floating-elements {
    position: absolute;
    width: 100%;
    height: 100%;
}

.floating-element {
    position: absolute;
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    border-radius: 50%;
    opacity: 0.1;
    animation: float 6s ease-in-out infinite;
}

.floating-element:nth-child(1) {
    top: 20%;
    left: 10%;
    animation-delay: 0s;
}

.floating-element:nth-child(2) {
    top: 60%;
    right: 10%;
    animation-delay: 2s;
}

.floating-element:nth-child(3) {
    bottom: 20%;
    left: 20%;
    animation-delay: 4s;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(180deg); }
}

.auth-content {
    position: relative;
    z-index: 2;
}

.auth-card {
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-2xl);
    padding: var(--spacing-3xl);
    box-shadow: var(--shadow-2xl);
    backdrop-filter: blur(20px);
    position: relative;
    overflow: hidden;
}

.auth-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
}

.auth-header {
    text-align: center;
    margin-bottom: var(--spacing-2xl);
}

.auth-logo {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-lg);
}

.auth-logo .logo-img {
    width: 40px;
    height: 40px;
}

.auth-logo .logo-text {
    font-size: var(--font-size-xl);
    font-weight: 700;
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.auth-title {
    font-size: var(--font-size-2xl);
    font-weight: 700;
    margin-bottom: var(--spacing-sm);
    color: var(--text-primary);
}

.auth-subtitle {
    color: var(--text-secondary);
    font-size: var(--font-size-base);
}

.auth-form {
    margin-bottom: var(--spacing-xl);
}

.form-group {
    margin-bottom: var(--spacing-lg);
}

.form-label {
    display: block;
    margin-bottom: var(--spacing-xs);
    font-weight: 500;
    color: var(--text-primary);
    font-size: var(--font-size-sm);
}

.form-input {
    width: 100%;
    padding: var(--spacing-md);
    background: var(--bg-tertiary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    color: var(--text-primary);
    font-size: var(--font-size-base);
    transition: var(--transition-base);
}

.form-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.form-input.error {
    border-color: var(--error-color);
}

.password-input-container {
    position: relative;
}

.password-toggle {
    position: absolute;
    right: var(--spacing-md);
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: var(--text-muted);
    cursor: pointer;
    font-size: var(--font-size-base);
    transition: var(--transition-fast);
}

.password-toggle:hover {
    color: var(--primary-color);
}

.form-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-xl);
    font-size: var(--font-size-sm);
}

.checkbox-container {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.checkbox-container input[type="checkbox"] {
    width: 16px;
    height: 16px;
    accent-color: var(--primary-color);
}

.forgot-password {
    color: var(--primary-color);
    text-decoration: none;
    transition: var(--transition-fast);
}

.forgot-password:hover {
    color: var(--primary-light);
}

.auth-footer {
    text-align: center;
    margin-top: var(--spacing-xl);
    padding-top: var(--spacing-lg);
    border-top: 1px solid var(--border-color);
}

.auth-link {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 500;
    transition: var(--transition-fast);
}

.auth-link:hover {
    color: var(--primary-light);
}

.bonus-info {
    margin-top: var(--spacing-xl);
}

.bonus-card {
    background: linear-gradient(135deg, var(--success-color), var(--success-dark));
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    color: white;
}

.bonus-card i {
    font-size: var(--font-size-xl);
}

.bonus-text h3 {
    font-size: var(--font-size-base);
    font-weight: 600;
    margin-bottom: var(--spacing-xs);
}

.bonus-text p {
    font-size: var(--font-size-sm);
    opacity: 0.9;
    margin: 0;
}

.error-messages {
    background: rgba(239, 68, 68, 0.1);
    border: 1px solid var(--error-color);
    border-radius: var(--radius-lg);
    padding: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
}

.error-messages ul {
    list-style: none;
    margin: 0;
    padding: 0;
}

.error-messages li {
    color: var(--error-color);
    font-size: var(--font-size-sm);
    margin-bottom: var(--spacing-xs);
}

.error-messages li:last-child {
    margin-bottom: 0;
}

.success-message {
    background: rgba(16, 185, 129, 0.1);
    border: 1px solid var(--success-color);
    border-radius: var(--radius-lg);
    padding: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
    color: var(--success-color);
    font-size: var(--font-size-sm);
}

@media (max-width: 768px) {
    .auth-section {
        padding: var(--spacing-lg) var(--spacing-md);
    }
    
    .auth-card {
        padding: var(--spacing-2xl);
    }
    
    .auth-title {
        font-size: var(--font-size-xl);
    }
    
    .form-options {
        flex-direction: column;
        gap: var(--spacing-md);
        align-items: flex-start;
    }
}
