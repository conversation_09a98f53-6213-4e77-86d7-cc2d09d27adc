// Astragenix Dashboard JavaScript

document.addEventListener('DOMContentLoaded', function() {
    initSidebar();
    initMobileMenu();
    initTaskClaiming();
    initRealTimeUpdates();
    initCharts();
    initNotifications();
    
    console.log('Dashboard initialized');
});

// Sidebar functionality
function initSidebar() {
    const sidebarToggle = document.getElementById('sidebar-toggle');
    const sidebar = document.querySelector('.sidebar');
    
    if (sidebarToggle && sidebar) {
        sidebarToggle.addEventListener('click', function() {
            sidebar.classList.toggle('collapsed');
            
            // Save state to localStorage
            localStorage.setItem('sidebarCollapsed', sidebar.classList.contains('collapsed'));
        });
        
        // Restore sidebar state
        const isCollapsed = localStorage.getItem('sidebarCollapsed') === 'true';
        if (isCollapsed) {
            sidebar.classList.add('collapsed');
        }
    }
    
    // Auto-collapse on small screens
    function checkScreenSize() {
        if (window.innerWidth <= 1024) {
            sidebar?.classList.add('collapsed');
        }
    }
    
    checkScreenSize();
    window.addEventListener('resize', checkScreenSize);
}

// Mobile menu functionality
function initMobileMenu() {
    const mobileMenuToggle = document.getElementById('mobile-menu-toggle');
    const sidebar = document.querySelector('.sidebar');
    
    if (mobileMenuToggle && sidebar) {
        mobileMenuToggle.addEventListener('click', function() {
            sidebar.classList.toggle('mobile-open');
        });
        
        // Close mobile menu when clicking outside
        document.addEventListener('click', function(e) {
            if (!sidebar.contains(e.target) && !mobileMenuToggle.contains(e.target)) {
                sidebar.classList.remove('mobile-open');
            }
        });
        
        // Close mobile menu when clicking nav links
        const navLinks = sidebar.querySelectorAll('.nav-item');
        navLinks.forEach(link => {
            link.addEventListener('click', function() {
                sidebar.classList.remove('mobile-open');
            });
        });
    }
}

// Task claiming functionality
function initTaskClaiming() {
    const claimButtons = document.querySelectorAll('.claim-reward');
    
    claimButtons.forEach(button => {
        button.addEventListener('click', function() {
            const taskId = this.getAttribute('data-task-id');
            claimTaskReward(taskId, this);
        });
    });
}

function claimTaskReward(taskId, button) {
    // Add loading state
    button.classList.add('loading');
    button.disabled = true;
    
    // Simulate API call
    fetch('api/claim-task-reward.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        },
        body: JSON.stringify({ task_id: taskId })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Update UI
            button.textContent = 'Claimed';
            button.classList.remove('btn-primary');
            button.classList.add('status', 'claimed');
            button.disabled = true;
            
            // Show success notification
            showNotification(data.message || 'Reward claimed successfully!', 'success');
            
            // Update balance if provided
            if (data.new_balance) {
                updateBalance(data.new_balance);
            }
            
            // Add celebration animation
            celebrateReward(button);
        } else {
            showNotification(data.message || 'Failed to claim reward', 'error');
            button.classList.remove('loading');
            button.disabled = false;
        }
    })
    .catch(error => {
        console.error('Error claiming reward:', error);
        showNotification('Network error. Please try again.', 'error');
        button.classList.remove('loading');
        button.disabled = false;
    });
}

function celebrateReward(element) {
    // Create confetti effect
    const confetti = document.createElement('div');
    confetti.className = 'confetti-container';
    confetti.innerHTML = `
        <div class="confetti"></div>
        <div class="confetti"></div>
        <div class="confetti"></div>
        <div class="confetti"></div>
        <div class="confetti"></div>
    `;
    
    // Add styles
    const style = document.createElement('style');
    style.textContent = `
        .confetti-container {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            overflow: hidden;
        }
        
        .confetti {
            position: absolute;
            width: 10px;
            height: 10px;
            background: var(--primary-color);
            animation: confetti-fall 2s ease-out forwards;
        }
        
        .confetti:nth-child(1) { left: 10%; animation-delay: 0s; background: #ff6b6b; }
        .confetti:nth-child(2) { left: 30%; animation-delay: 0.2s; background: #4ecdc4; }
        .confetti:nth-child(3) { left: 50%; animation-delay: 0.4s; background: #45b7d1; }
        .confetti:nth-child(4) { left: 70%; animation-delay: 0.6s; background: #96ceb4; }
        .confetti:nth-child(5) { left: 90%; animation-delay: 0.8s; background: #feca57; }
        
        @keyframes confetti-fall {
            0% {
                transform: translateY(-100px) rotate(0deg);
                opacity: 1;
            }
            100% {
                transform: translateY(100px) rotate(720deg);
                opacity: 0;
            }
        }
    `;
    document.head.appendChild(style);
    
    element.parentNode.style.position = 'relative';
    element.parentNode.appendChild(confetti);
    
    // Remove confetti after animation
    setTimeout(() => {
        confetti.remove();
        style.remove();
    }, 2000);
}

// Real-time updates
function initRealTimeUpdates() {
    // Update stats every 30 seconds
    setInterval(updateDashboardStats, 30000);
    
    // Update time displays
    setInterval(updateTimeDisplays, 1000);
}

function updateDashboardStats() {
    fetch('api/dashboard-stats.php')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                updateBalance(data.balance);
                updateStats(data.stats);
            }
        })
        .catch(error => {
            console.error('Error updating stats:', error);
        });
}

function updateBalance(balance) {
    const balanceElements = document.querySelectorAll('[data-balance]');
    balanceElements.forEach(element => {
        const currentValue = parseFloat(element.textContent.replace(/[^\d.-]/g, ''));
        const newValue = parseFloat(balance);
        
        if (currentValue !== newValue) {
            animateNumber(element, currentValue, newValue);
        }
    });
}

function updateStats(stats) {
    Object.keys(stats).forEach(key => {
        const element = document.querySelector(`[data-stat="${key}"]`);
        if (element) {
            const currentValue = parseFloat(element.textContent.replace(/[^\d.-]/g, ''));
            const newValue = parseFloat(stats[key]);
            
            if (currentValue !== newValue) {
                animateNumber(element, currentValue, newValue);
            }
        }
    });
}

function animateNumber(element, from, to) {
    const duration = 1000;
    const steps = 60;
    const stepValue = (to - from) / steps;
    let current = from;
    let step = 0;
    
    const timer = setInterval(() => {
        current += stepValue;
        step++;
        
        if (step >= steps) {
            current = to;
            clearInterval(timer);
        }
        
        // Format the number based on element type
        if (element.dataset.currency) {
            element.textContent = formatCurrency(current);
        } else {
            element.textContent = Math.round(current).toLocaleString();
        }
    }, duration / steps);
}

function updateTimeDisplays() {
    const timeElements = document.querySelectorAll('[data-time]');
    timeElements.forEach(element => {
        const timestamp = element.getAttribute('data-time');
        const timeAgo = getTimeAgo(new Date(timestamp));
        element.textContent = timeAgo;
    });
}

function getTimeAgo(date) {
    const now = new Date();
    const diffInSeconds = Math.floor((now - date) / 1000);
    
    if (diffInSeconds < 60) {
        return 'Just now';
    } else if (diffInSeconds < 3600) {
        const minutes = Math.floor(diffInSeconds / 60);
        return `${minutes} minute${minutes > 1 ? 's' : ''} ago`;
    } else if (diffInSeconds < 86400) {
        const hours = Math.floor(diffInSeconds / 3600);
        return `${hours} hour${hours > 1 ? 's' : ''} ago`;
    } else {
        const days = Math.floor(diffInSeconds / 86400);
        return `${days} day${days > 1 ? 's' : ''} ago`;
    }
}

// Charts initialization
function initCharts() {
    // Initialize profit chart if canvas exists
    const profitChartCanvas = document.getElementById('profitChart');
    if (profitChartCanvas) {
        initProfitChart(profitChartCanvas);
    }
    
    // Initialize investment distribution chart
    const distributionChartCanvas = document.getElementById('distributionChart');
    if (distributionChartCanvas) {
        initDistributionChart(distributionChartCanvas);
    }
}

function initProfitChart(canvas) {
    const ctx = canvas.getContext('2d');
    
    // Sample data - in real app, fetch from API
    const data = {
        labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
        datasets: [{
            label: 'Profit',
            data: [1200, 1900, 3000, 5000, 2000, 3000],
            borderColor: '#6366f1',
            backgroundColor: 'rgba(99, 102, 241, 0.1)',
            borderWidth: 2,
            fill: true,
            tension: 0.4
        }]
    };
    
    // Simple chart implementation (you can replace with Chart.js)
    drawLineChart(ctx, data);
}

function drawLineChart(ctx, data) {
    const canvas = ctx.canvas;
    const width = canvas.width;
    const height = canvas.height;
    const padding = 40;
    
    // Clear canvas
    ctx.clearRect(0, 0, width, height);
    
    // Set styles
    ctx.strokeStyle = '#6366f1';
    ctx.fillStyle = 'rgba(99, 102, 241, 0.1)';
    ctx.lineWidth = 2;
    
    // Calculate points
    const maxValue = Math.max(...data.datasets[0].data);
    const points = data.datasets[0].data.map((value, index) => ({
        x: padding + (index * (width - 2 * padding)) / (data.labels.length - 1),
        y: height - padding - (value / maxValue) * (height - 2 * padding)
    }));
    
    // Draw line
    ctx.beginPath();
    ctx.moveTo(points[0].x, points[0].y);
    points.forEach(point => {
        ctx.lineTo(point.x, point.y);
    });
    ctx.stroke();
    
    // Draw fill
    ctx.lineTo(points[points.length - 1].x, height - padding);
    ctx.lineTo(points[0].x, height - padding);
    ctx.closePath();
    ctx.fill();
    
    // Draw points
    ctx.fillStyle = '#6366f1';
    points.forEach(point => {
        ctx.beginPath();
        ctx.arc(point.x, point.y, 4, 0, 2 * Math.PI);
        ctx.fill();
    });
}

// Notifications
function initNotifications() {
    // Check for new notifications every minute
    setInterval(checkNotifications, 60000);
    
    // Initial check
    checkNotifications();
}

function checkNotifications() {
    fetch('api/notifications.php')
        .then(response => response.json())
        .then(data => {
            if (data.success && data.notifications.length > 0) {
                data.notifications.forEach(notification => {
                    showNotification(notification.message, notification.type);
                });
            }
        })
        .catch(error => {
            console.error('Error checking notifications:', error);
        });
}

// Utility functions
function formatCurrency(amount, currency = 'USDT') {
    return new Intl.NumberFormat('en-US', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    }).format(amount) + ' ' + currency;
}

// Quick actions
function initQuickActions() {
    // Quick invest button
    const quickInvestBtn = document.getElementById('quick-invest');
    if (quickInvestBtn) {
        quickInvestBtn.addEventListener('click', function() {
            // Open investment modal or redirect
            window.location.href = 'invest.php';
        });
    }
    
    // Quick withdraw button
    const quickWithdrawBtn = document.getElementById('quick-withdraw');
    if (quickWithdrawBtn) {
        quickWithdrawBtn.addEventListener('click', function() {
            // Open withdrawal modal or redirect
            window.location.href = 'withdraw.php';
        });
    }
}

// Initialize quick actions
initQuickActions();

// Keyboard shortcuts
document.addEventListener('keydown', function(e) {
    // Ctrl/Cmd + I for invest
    if ((e.ctrlKey || e.metaKey) && e.key === 'i') {
        e.preventDefault();
        window.location.href = 'invest.php';
    }
    
    // Ctrl/Cmd + W for withdraw
    if ((e.ctrlKey || e.metaKey) && e.key === 'w') {
        e.preventDefault();
        window.location.href = 'withdraw.php';
    }
    
    // Ctrl/Cmd + T for transactions
    if ((e.ctrlKey || e.metaKey) && e.key === 't') {
        e.preventDefault();
        window.location.href = 'transactions.php';
    }
});

// Export functions for global use
window.DashboardUtils = {
    updateBalance,
    updateStats,
    claimTaskReward,
    formatCurrency,
    showNotification: window.AstragenixUtils?.showNotification || console.log
};
