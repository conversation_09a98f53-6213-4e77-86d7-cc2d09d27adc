<svg width="40" height="40" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="logoGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#6366f1;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#8b5cf6;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Outer ring -->
  <circle cx="20" cy="20" r="18" stroke="url(#logoGradient)" stroke-width="2" fill="none" opacity="0.3"/>
  
  <!-- Inner circle -->
  <circle cx="20" cy="20" r="12" fill="url(#logoGradient)" opacity="0.8"/>
  
  <!-- Star shape in center -->
  <path d="M20 8 L22.5 15 L30 15 L24.5 19.5 L27 27 L20 22 L13 27 L15.5 19.5 L10 15 L17.5 15 Z" 
        fill="white" opacity="0.9"/>
  
  <!-- Orbital rings -->
  <circle cx="20" cy="20" r="25" stroke="url(#logoGradient)" stroke-width="1" fill="none" opacity="0.2" 
          stroke-dasharray="3,3">
    <animateTransform attributeName="transform" attributeType="XML" type="rotate" 
                      from="0 20 20" to="360 20 20" dur="20s" repeatCount="indefinite"/>
  </circle>
  
  <circle cx="20" cy="20" r="30" stroke="url(#logoGradient)" stroke-width="1" fill="none" opacity="0.1" 
          stroke-dasharray="5,5">
    <animateTransform attributeName="transform" attributeType="XML" type="rotate" 
                      from="360 20 20" to="0 20 20" dur="30s" repeatCount="indefinite"/>
  </circle>
</svg>
