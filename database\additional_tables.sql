-- Additional tables for Astragenix platform

-- User balances table
CREATE TABLE IF NOT EXISTS user_balances (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    total_balance DECIMAL(15,2) DEFAULT 0.00,
    available_balance DECIMAL(15,2) DEFAULT 0.00,
    invested_balance DECIMAL(15,2) DEFAULT 0.00,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_balance (user_id)
);

-- Investments table
CREATE TABLE IF NOT EXISTS investments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    plan_id VARCHAR(50) NOT NULL,
    amount DECIMAL(15,2) NOT NULL,
    daily_rate DECIMAL(5,2) NOT NULL,
    duration_days INT NOT NULL,
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    total_profit DECIMAL(15,2) DEFAULT 0.00,
    status ENUM('active', 'completed', 'cancelled') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_status (user_id, status),
    INDEX idx_status_date (status, end_date)
);

-- Transactions table (updated)
CREATE TABLE IF NOT EXISTS transactions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    type ENUM('deposit', 'withdrawal', 'profit', 'bonus', 'referral', 'investment') NOT NULL,
    amount DECIMAL(15,2) NOT NULL,
    fee DECIMAL(15,2) DEFAULT 0.00,
    wallet_address VARCHAR(255) NULL,
    payment_method VARCHAR(100) NULL,
    description TEXT NULL,
    status ENUM('pending', 'processing', 'completed', 'failed', 'cancelled') DEFAULT 'pending',
    reference_id INT NULL,
    reference_type VARCHAR(50) NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_type (user_id, type),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
);

-- Referral commissions table
CREATE TABLE IF NOT EXISTS referral_commissions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    referrer_id INT NOT NULL,
    referred_user_id INT NOT NULL,
    commission_amount DECIMAL(15,2) NOT NULL,
    commission_rate DECIMAL(5,2) NOT NULL,
    level INT DEFAULT 1,
    investment_id INT NULL,
    status ENUM('pending', 'paid') DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (referrer_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (referred_user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (investment_id) REFERENCES investments(id) ON DELETE SET NULL,
    INDEX idx_referrer (referrer_id),
    INDEX idx_referred (referred_user_id),
    INDEX idx_status (status)
);

-- Activity logs table
CREATE TABLE IF NOT EXISTS activity_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NULL,
    action VARCHAR(100) NOT NULL,
    details TEXT NULL,
    ip_address VARCHAR(45) NULL,
    user_agent TEXT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_user_action (user_id, action),
    INDEX idx_created_at (created_at)
);

-- Site settings table
CREATE TABLE IF NOT EXISTS site_settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    setting_key VARCHAR(100) NOT NULL UNIQUE,
    value TEXT NULL,
    description TEXT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Rate limits table
CREATE TABLE IF NOT EXISTS rate_limits (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NULL,
    action VARCHAR(100) NOT NULL,
    ip_address VARCHAR(45) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_action (user_id, action),
    INDEX idx_ip_action (ip_address, action),
    INDEX idx_created_at (created_at)
);

-- Permissions table
CREATE TABLE IF NOT EXISTS permissions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL UNIQUE,
    description TEXT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- User permissions table
CREATE TABLE IF NOT EXISTS user_permissions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    permission_id INT NOT NULL,
    granted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    granted_by INT NULL,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (permission_id) REFERENCES permissions(id) ON DELETE CASCADE,
    FOREIGN KEY (granted_by) REFERENCES users(id) ON DELETE SET NULL,
    UNIQUE KEY unique_user_permission (user_id, permission_id)
);

-- Notifications table
CREATE TABLE IF NOT EXISTS notifications (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    title VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    type ENUM('info', 'success', 'warning', 'error') DEFAULT 'info',
    is_read BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    read_at TIMESTAMP NULL,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_read (user_id, is_read),
    INDEX idx_created_at (created_at)
);

-- Investment plans table (for dynamic plans)
CREATE TABLE IF NOT EXISTS investment_plans (
    id INT AUTO_INCREMENT PRIMARY KEY,
    plan_key VARCHAR(50) NOT NULL UNIQUE,
    name VARCHAR(100) NOT NULL,
    description TEXT NULL,
    min_amount DECIMAL(15,2) NOT NULL,
    max_amount DECIMAL(15,2) NOT NULL,
    daily_rate DECIMAL(5,2) NOT NULL,
    duration_days INT NOT NULL,
    features JSON NULL,
    is_active BOOLEAN DEFAULT TRUE,
    sort_order INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_active_sort (is_active, sort_order)
);

-- Payment methods table
CREATE TABLE IF NOT EXISTS payment_methods (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    code VARCHAR(50) NOT NULL UNIQUE,
    type ENUM('crypto', 'bank', 'ewallet') NOT NULL,
    min_amount DECIMAL(15,2) DEFAULT 0.00,
    max_amount DECIMAL(15,2) DEFAULT 999999.99,
    fee_percentage DECIMAL(5,2) DEFAULT 0.00,
    fee_fixed DECIMAL(15,2) DEFAULT 0.00,
    processing_time VARCHAR(100) NULL,
    instructions TEXT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_active_type (is_active, type)
);

-- News/announcements table
CREATE TABLE IF NOT EXISTS news (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    slug VARCHAR(255) NOT NULL UNIQUE,
    content TEXT NOT NULL,
    excerpt TEXT NULL,
    featured_image VARCHAR(255) NULL,
    author_id INT NULL,
    status ENUM('draft', 'published', 'archived') DEFAULT 'draft',
    is_featured BOOLEAN DEFAULT FALSE,
    published_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (author_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_status_published (status, published_at),
    INDEX idx_featured (is_featured)
);

-- FAQ table
CREATE TABLE IF NOT EXISTS faq (
    id INT AUTO_INCREMENT PRIMARY KEY,
    category VARCHAR(100) NOT NULL,
    question VARCHAR(500) NOT NULL,
    answer TEXT NOT NULL,
    sort_order INT DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_category_active (category, is_active),
    INDEX idx_sort_order (sort_order)
);

-- Insert default site settings
INSERT IGNORE INTO site_settings (setting_key, value, description) VALUES
('site_name', 'Astragenix', 'Site name'),
('site_description', 'Advanced Crypto Investment Platform', 'Site description'),
('maintenance_mode', '0', 'Maintenance mode (0=off, 1=on)'),
('registration_enabled', '1', 'Registration enabled (0=off, 1=on)'),
('min_withdrawal', '10', 'Minimum withdrawal amount'),
('max_withdrawal', '10000', 'Maximum withdrawal amount'),
('withdrawal_fee', '2', 'Withdrawal fee percentage'),
('referral_commission_l1', '10', 'Level 1 referral commission percentage'),
('referral_commission_l2', '5', 'Level 2 referral commission percentage'),
('referral_commission_l3', '2', 'Level 3 referral commission percentage'),
('welcome_bonus', '50', 'Welcome bonus amount'),
('support_email', '<EMAIL>', 'Support email address'),
('admin_email', '<EMAIL>', 'Admin email address');

-- Insert default investment plans
INSERT IGNORE INTO investment_plans (plan_key, name, description, min_amount, max_amount, daily_rate, duration_days, features, sort_order) VALUES
('starter', 'Starter Plan', 'Perfect for beginners', 100.00, 999.99, 1.20, 30, '["Daily Profits", "Instant Withdrawal", "24/7 Support"]', 1),
('professional', 'Professional Plan', 'Best value option', 1000.00, 9999.99, 1.80, 60, '["Daily Profits", "Instant Withdrawal", "Priority Support", "Bonus Rewards"]', 2),
('enterprise', 'Enterprise Plan', 'Maximum returns', 10000.00, 100000.00, 2.50, 90, '["Daily Profits", "Instant Withdrawal", "VIP Support", "Exclusive Bonuses", "Personal Manager"]', 3);

-- Insert default payment methods
INSERT IGNORE INTO payment_methods (name, code, type, min_amount, max_amount, fee_percentage, processing_time, instructions) VALUES
('USDT (TRC20)', 'USDT_TRC20', 'crypto', 10.00, 50000.00, 1.00, '5-30 minutes', 'Send USDT to the provided TRC20 address'),
('USDT (ERC20)', 'USDT_ERC20', 'crypto', 10.00, 50000.00, 2.00, '10-60 minutes', 'Send USDT to the provided ERC20 address'),
('Bitcoin', 'BTC', 'crypto', 50.00, 100000.00, 1.50, '30-120 minutes', 'Send Bitcoin to the provided address'),
('Ethereum', 'ETH', 'crypto', 50.00, 100000.00, 1.50, '10-60 minutes', 'Send Ethereum to the provided address');

-- Insert default permissions
INSERT IGNORE INTO permissions (name, description) VALUES
('admin_access', 'Access to admin panel'),
('user_management', 'Manage users'),
('investment_management', 'Manage investments'),
('transaction_management', 'Manage transactions'),
('settings_management', 'Manage site settings'),
('news_management', 'Manage news and announcements'),
('support_access', 'Access to support features');

-- Insert default FAQ items
INSERT IGNORE INTO faq (category, question, answer, sort_order) VALUES
('getting_started', 'How do I register an account?', 'Click the "Register" button, fill in your details, verify your email, and complete the KYC process.', 1),
('getting_started', 'Is verification required?', 'Yes, we require identity verification to ensure security and comply with regulations.', 2),
('investments', 'What is the minimum investment amount?', 'The minimum investment starts at $100 USDT for our Starter Plan.', 1),
('investments', 'How are profits calculated?', 'Profits are calculated daily based on your investment amount and the plan\'s return rate.', 2),
('withdrawals', 'How long do withdrawals take?', 'Withdrawals are processed within 24 hours, often much faster.', 1),
('withdrawals', 'Are there withdrawal fees?', 'We charge minimal network fees for cryptocurrency withdrawals.', 2),
('security', 'How secure are my funds?', 'Funds are protected by multi-signature wallets, cold storage, and bank-grade security.', 1),
('security', 'What is two-factor authentication?', '2FA adds an extra security layer by requiring a code from your mobile device.', 2);
