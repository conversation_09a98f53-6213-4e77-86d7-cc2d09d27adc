<?php
session_start();
require_once '../../config/database.php';
require_once '../../includes/functions.php';

// Проверка авторизации и прав администратора
if (!isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Unauthorized']);
    exit;
}

$user = getUserById($_SESSION['user_id']);
if (!$user || $user['role'] !== 'admin') {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'Access denied']);
    exit;
}

$user_id = intval($_GET['id'] ?? 0);

if (!$user_id) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'User ID is required']);
    exit;
}

try {
    $db = getDB();
    
    // Получаем основные данные пользователя
    $stmt = $db->prepare("
        SELECT 
            u.*,
            COALESCE(ub.total_balance, 0) as total_balance,
            COALESCE(ub.available_balance, 0) as available_balance,
            COALESCE(ub.invested_balance, 0) as invested_balance,
            referrer.first_name as referred_by_first_name,
            referrer.last_name as referred_by_last_name,
            CONCAT(referrer.first_name, ' ', referrer.last_name) as referred_by_name
        FROM users u
        LEFT JOIN user_balances ub ON u.id = ub.user_id
        LEFT JOIN users referrer ON u.referred_by = referrer.id
        WHERE u.id = ?
    ");
    
    $stmt->execute([$user_id]);
    $user_data = $stmt->fetch();
    
    if (!$user_data) {
        http_response_code(404);
        echo json_encode(['success' => false, 'message' => 'User not found']);
        exit;
    }
    
    // Получаем статистику инвестиций
    $stmt = $db->prepare("
        SELECT 
            COUNT(*) as total_investments,
            COUNT(CASE WHEN status = 'active' THEN 1 END) as active_investments,
            COALESCE(SUM(amount), 0) as total_invested,
            COALESCE(SUM(CASE WHEN status = 'completed' THEN amount ELSE 0 END), 0) as completed_investments
        FROM investments 
        WHERE user_id = ?
    ");
    $stmt->execute([$user_id]);
    $investment_stats = $stmt->fetch();
    
    // Получаем статистику транзакций
    $stmt = $db->prepare("
        SELECT 
            COUNT(*) as total_transactions,
            COALESCE(SUM(CASE WHEN type = 'deposit' AND status = 'completed' THEN amount ELSE 0 END), 0) as total_deposits,
            COALESCE(SUM(CASE WHEN type = 'withdrawal' AND status = 'completed' THEN amount ELSE 0 END), 0) as total_withdrawals,
            COALESCE(SUM(CASE WHEN type = 'profit' AND status = 'completed' THEN amount ELSE 0 END), 0) as total_profits
        FROM transactions 
        WHERE user_id = ?
    ");
    $stmt->execute([$user_id]);
    $transaction_stats = $stmt->fetch();
    
    // Получаем статистику рефералов
    $stmt = $db->prepare("
        SELECT 
            COUNT(*) as total_referrals,
            COALESCE(SUM(commission_amount), 0) as total_referral_earnings
        FROM referral_commissions 
        WHERE referrer_id = ?
    ");
    $stmt->execute([$user_id]);
    $referral_stats = $stmt->fetch();
    
    // Получаем последние активности
    $stmt = $db->prepare("
        SELECT action, details, created_at
        FROM activity_logs 
        WHERE user_id = ?
        ORDER BY created_at DESC
        LIMIT 10
    ");
    $stmt->execute([$user_id]);
    $recent_activities = $stmt->fetchAll();
    
    // Получаем последние инвестиции
    $stmt = $db->prepare("
        SELECT plan_id, amount, daily_rate, duration_days, status, created_at
        FROM investments 
        WHERE user_id = ?
        ORDER BY created_at DESC
        LIMIT 5
    ");
    $stmt->execute([$user_id]);
    $recent_investments = $stmt->fetchAll();
    
    // Получаем последние транзакции
    $stmt = $db->prepare("
        SELECT type, amount, status, description, created_at
        FROM transactions 
        WHERE user_id = ?
        ORDER BY created_at DESC
        LIMIT 10
    ");
    $stmt->execute([$user_id]);
    $recent_transactions = $stmt->fetchAll();
    
    // Получаем рефералов пользователя
    $stmt = $db->prepare("
        SELECT id, first_name, last_name, email, created_at, status
        FROM users 
        WHERE referred_by = ?
        ORDER BY created_at DESC
        LIMIT 10
    ");
    $stmt->execute([$user_id]);
    $referrals = $stmt->fetchAll();
    
    // Формируем ответ
    $response = [
        'success' => true,
        'user' => [
            'id' => $user_data['id'],
            'first_name' => $user_data['first_name'],
            'last_name' => $user_data['last_name'],
            'email' => $user_data['email'],
            'phone' => $user_data['phone'],
            'status' => $user_data['status'],
            'role' => $user_data['role'],
            'referral_code' => $user_data['referral_code'],
            'referred_by' => $user_data['referred_by'],
            'referred_by_name' => $user_data['referred_by_name'],
            'total_balance' => $user_data['total_balance'],
            'available_balance' => $user_data['available_balance'],
            'invested_balance' => $user_data['invested_balance'],
            'created_at' => $user_data['created_at'],
            'updated_at' => $user_data['updated_at'],
            'last_activity' => $user_data['last_activity'],
            'statistics' => [
                'total_investments' => $investment_stats['total_investments'],
                'active_investments' => $investment_stats['active_investments'],
                'total_invested' => $investment_stats['total_invested'],
                'completed_investments' => $investment_stats['completed_investments'],
                'total_transactions' => $transaction_stats['total_transactions'],
                'total_deposits' => $transaction_stats['total_deposits'],
                'total_withdrawals' => $transaction_stats['total_withdrawals'],
                'total_profits' => $transaction_stats['total_profits'],
                'total_referrals' => $referral_stats['total_referrals'],
                'total_referral_earnings' => $referral_stats['total_referral_earnings']
            ],
            'recent_activities' => $recent_activities,
            'recent_investments' => $recent_investments,
            'recent_transactions' => $recent_transactions,
            'referrals' => $referrals
        ]
    ];
    
    echo json_encode($response);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Internal server error']);
    
    // Логируем ошибку
    error_log("Error getting user {$user_id}: " . $e->getMessage());
}
?>
