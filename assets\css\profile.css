/* Profile Page Styles */

.profile-page {
    background: linear-gradient(135deg, var(--bg-primary), var(--bg-secondary));
    min-height: 100vh;
}

.profile-section {
    padding: var(--spacing-2xl) 0;
}

.profile-content {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: var(--spacing-3xl);
    margin-top: var(--spacing-2xl);
}

.profile-overview {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-2xl);
}

.profile-card {
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-2xl);
    padding: var(--spacing-2xl);
    text-align: center;
    box-shadow: var(--shadow-lg);
}

.profile-avatar {
    position: relative;
    display: inline-block;
    margin-bottom: var(--spacing-lg);
}

.profile-avatar img {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    border: 4px solid var(--primary-color);
    object-fit: cover;
}

.avatar-badge {
    position: absolute;
    bottom: 0;
    right: 0;
    width: 36px;
    height: 36px;
    background: linear-gradient(135deg, var(--warning-color), var(--warning-dark));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-inverse);
    font-size: var(--font-size-sm);
    border: 3px solid var(--bg-card);
}

.profile-info h2 {
    font-size: var(--font-size-xl);
    font-weight: 700;
    margin-bottom: var(--spacing-xs);
    color: var(--text-primary);
}

.profile-email {
    color: var(--text-secondary);
    margin-bottom: var(--spacing-md);
    font-size: var(--font-size-base);
}

.profile-meta {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.meta-item {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-xs);
    font-size: var(--font-size-sm);
    color: var(--text-muted);
}

.meta-item i {
    color: var(--primary-color);
}

.stats-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-md);
}

.stat-card {
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    text-align: center;
    transition: var(--transition-base);
}

.stat-card:hover {
    transform: translateY(-4px);
    border-color: var(--primary-color);
    box-shadow: var(--shadow-glow);
}

.stat-icon {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto var(--spacing-sm);
    color: var(--text-inverse);
    font-size: var(--font-size-base);
}

.stat-info h3 {
    font-size: var(--font-size-lg);
    font-weight: 700;
    margin-bottom: var(--spacing-xs);
    color: var(--text-primary);
}

.stat-info p {
    font-size: var(--font-size-xs);
    color: var(--text-muted);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.profile-forms {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-2xl);
}

.form-section {
    width: 100%;
}

.form-card {
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-2xl);
    padding: var(--spacing-3xl);
    box-shadow: var(--shadow-lg);
}

.form-header {
    margin-bottom: var(--spacing-2xl);
}

.form-header h3 {
    font-size: var(--font-size-xl);
    font-weight: 700;
    margin-bottom: var(--spacing-sm);
    color: var(--text-primary);
}

.form-header p {
    color: var(--text-secondary);
    font-size: var(--font-size-base);
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-lg);
}

.form-group {
    margin-bottom: var(--spacing-lg);
}

.form-group label {
    display: block;
    margin-bottom: var(--spacing-xs);
    font-weight: 500;
    color: var(--text-primary);
    font-size: var(--font-size-sm);
}

.form-input {
    width: 100%;
    padding: var(--spacing-md);
    background: var(--bg-tertiary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    color: var(--text-primary);
    font-size: var(--font-size-base);
    transition: var(--transition-base);
}

.form-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.form-input:disabled {
    background: var(--bg-secondary);
    color: var(--text-muted);
    cursor: not-allowed;
}

.password-input-container {
    position: relative;
}

.password-toggle {
    position: absolute;
    right: var(--spacing-md);
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: var(--text-muted);
    cursor: pointer;
    font-size: var(--font-size-base);
    transition: var(--transition-fast);
}

.password-toggle:hover {
    color: var(--primary-color);
}

.input-help {
    margin-top: var(--spacing-xs);
    font-size: var(--font-size-xs);
    color: var(--text-muted);
}

.form-actions {
    margin-top: var(--spacing-xl);
    display: flex;
    justify-content: flex-end;
}

.form-actions .btn {
    min-width: 140px;
    padding: var(--spacing-md) var(--spacing-xl);
    font-weight: 600;
}

.error-messages {
    background: rgba(239, 68, 68, 0.1);
    border: 1px solid var(--error-color);
    border-radius: var(--radius-lg);
    padding: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
}

.error-messages ul {
    list-style: none;
    margin: 0;
    padding: 0;
}

.error-messages li {
    color: var(--error-color);
    font-size: var(--font-size-sm);
    margin-bottom: var(--spacing-xs);
}

.error-messages li:last-child {
    margin-bottom: 0;
}

.success-message {
    background: rgba(16, 185, 129, 0.1);
    border: 1px solid var(--success-color);
    border-radius: var(--radius-lg);
    padding: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
    color: var(--success-color);
    font-size: var(--font-size-sm);
}

@media (max-width: 1024px) {
    .profile-content {
        grid-template-columns: 1fr;
        gap: var(--spacing-2xl);
    }
    
    .stats-grid {
        grid-template-columns: repeat(4, 1fr);
    }
}

@media (max-width: 768px) {
    .profile-card {
        padding: var(--spacing-xl);
    }
    
    .form-card {
        padding: var(--spacing-2xl);
    }
    
    .form-row {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }
    
    .stats-grid {
        grid-template-columns: 1fr 1fr;
        gap: var(--spacing-sm);
    }
    
    .stat-card {
        padding: var(--spacing-md);
    }
    
    .profile-avatar img {
        width: 100px;
        height: 100px;
    }
    
    .avatar-badge {
        width: 30px;
        height: 30px;
        font-size: var(--font-size-xs);
    }
}
