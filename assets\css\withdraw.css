/* Withdrawal Page Styles */

.withdraw-page {
    background: linear-gradient(135deg, var(--bg-primary), var(--bg-secondary));
    min-height: 100vh;
}

.withdraw-section {
    padding: var(--spacing-2xl) 0;
}

.withdraw-content {
    max-width: 800px;
    margin: 0 auto;
}

.balance-info {
    margin-bottom: var(--spacing-3xl);
}

.balance-card {
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-xl);
    padding: var(--spacing-xl);
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
    box-shadow: var(--shadow-lg);
    max-width: 400px;
    margin: 0 auto;
}

.balance-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, var(--success-color), var(--success-dark));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-inverse);
    font-size: var(--font-size-xl);
}

.balance-details h3 {
    font-size: var(--font-size-base);
    color: var(--text-secondary);
    margin-bottom: var(--spacing-xs);
    font-weight: 500;
}

.balance-amount {
    font-size: var(--font-size-2xl);
    font-weight: 700;
    color: var(--success-color);
    margin-bottom: var(--spacing-xs);
}

.balance-note {
    font-size: var(--font-size-sm);
    color: var(--text-muted);
}

.withdrawal-form-container {
    margin-bottom: var(--spacing-3xl);
}

.form-card {
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-2xl);
    padding: var(--spacing-3xl);
    box-shadow: var(--shadow-xl);
}

.form-header {
    text-align: center;
    margin-bottom: var(--spacing-2xl);
}

.form-header h3 {
    font-size: var(--font-size-2xl);
    font-weight: 700;
    margin-bottom: var(--spacing-sm);
    color: var(--text-primary);
}

.form-header p {
    color: var(--text-secondary);
}

.form-group {
    margin-bottom: var(--spacing-lg);
}

.form-group label {
    display: block;
    margin-bottom: var(--spacing-xs);
    font-weight: 500;
    color: var(--text-primary);
    font-size: var(--font-size-sm);
}

.form-input {
    width: 100%;
    padding: var(--spacing-md);
    background: var(--bg-tertiary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    color: var(--text-primary);
    font-size: var(--font-size-base);
    transition: var(--transition-base);
}

.form-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.input-help {
    margin-top: var(--spacing-xs);
    font-size: var(--font-size-xs);
    color: var(--text-muted);
}

.withdrawal-summary {
    background: var(--bg-tertiary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
}

.withdrawal-summary h4 {
    font-size: var(--font-size-lg);
    font-weight: 600;
    margin-bottom: var(--spacing-md);
    color: var(--text-primary);
}

.summary-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-sm) 0;
    border-bottom: 1px solid var(--border-color);
    margin-bottom: var(--spacing-sm);
}

.summary-item:last-of-type {
    border-bottom: none;
    margin-bottom: 0;
}

.summary-item.total {
    font-weight: 700;
    font-size: var(--font-size-lg);
    color: var(--primary-color);
    border-top: 2px solid var(--border-color);
    padding-top: var(--spacing-md);
    margin-top: var(--spacing-md);
}

.summary-note {
    background: rgba(99, 102, 241, 0.1);
    border: 1px solid var(--primary-color);
    border-radius: var(--radius-md);
    padding: var(--spacing-sm);
    margin-top: var(--spacing-md);
    font-size: var(--font-size-sm);
    color: var(--primary-color);
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.form-actions {
    display: flex;
    gap: var(--spacing-lg);
    justify-content: center;
}

.form-actions .btn {
    min-width: 140px;
    padding: var(--spacing-md) var(--spacing-xl);
    font-weight: 600;
}

.recent-withdrawals {
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-xl);
    padding: var(--spacing-2xl);
    box-shadow: var(--shadow-lg);
}

.recent-withdrawals h3 {
    font-size: var(--font-size-xl);
    font-weight: 700;
    margin-bottom: var(--spacing-lg);
    color: var(--text-primary);
}

.withdrawals-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.withdrawal-item {
    background: var(--bg-tertiary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: var(--transition-fast);
}

.withdrawal-item:hover {
    border-color: var(--primary-color);
    transform: translateY(-2px);
}

.withdrawal-info {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.withdrawal-amount {
    font-size: var(--font-size-lg);
    font-weight: 700;
    color: var(--text-primary);
}

.withdrawal-method {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    font-weight: 500;
}

.withdrawal-date {
    font-size: var(--font-size-xs);
    color: var(--text-muted);
}

.withdrawal-status .status {
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-full);
    font-size: var(--font-size-xs);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.status.pending {
    background: rgba(245, 158, 11, 0.1);
    color: var(--warning-color);
    border: 1px solid var(--warning-color);
}

.status.completed {
    background: rgba(16, 185, 129, 0.1);
    color: var(--success-color);
    border: 1px solid var(--success-color);
}

.status.failed {
    background: rgba(239, 68, 68, 0.1);
    color: var(--error-color);
    border: 1px solid var(--error-color);
}

.status.processing {
    background: rgba(99, 102, 241, 0.1);
    color: var(--primary-color);
    border: 1px solid var(--primary-color);
}

@media (max-width: 768px) {
    .withdraw-content {
        padding: 0 var(--spacing-md);
    }
    
    .balance-card {
        flex-direction: column;
        text-align: center;
        gap: var(--spacing-md);
    }
    
    .form-card {
        padding: var(--spacing-2xl);
    }
    
    .form-actions {
        flex-direction: column;
    }
    
    .withdrawal-item {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-md);
    }
    
    .withdrawal-status {
        align-self: flex-end;
    }
    
    .recent-withdrawals {
        padding: var(--spacing-xl);
    }
}
