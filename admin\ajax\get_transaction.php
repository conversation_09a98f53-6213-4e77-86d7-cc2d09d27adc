<?php
session_start();
require_once '../../config/database.php';
require_once '../../includes/functions.php';

// Проверка авторизации и прав администратора
if (!isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Unauthorized']);
    exit;
}

$user = getUserById($_SESSION['user_id']);
if (!$user || $user['role'] !== 'admin') {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'Access denied']);
    exit;
}

$transaction_id = intval($_GET['id'] ?? 0);

if (!$transaction_id) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Transaction ID is required']);
    exit;
}

try {
    $db = getDB();
    
    // Получаем транзакцию с данными пользователя
    $stmt = $db->prepare("
        SELECT 
            t.*,
            u.first_name,
            u.last_name,
            u.email,
            CONCAT(u.first_name, ' ', u.last_name) as user_name
        FROM transactions t
        JOIN users u ON t.user_id = u.id
        WHERE t.id = ?
    ");
    
    $stmt->execute([$transaction_id]);
    $transaction = $stmt->fetch();
    
    if (!$transaction) {
        http_response_code(404);
        echo json_encode(['success' => false, 'message' => 'Transaction not found']);
        exit;
    }
    
    // Форматируем данные для отправки
    $response = [
        'success' => true,
        'transaction' => [
            'id' => $transaction['id'],
            'user_id' => $transaction['user_id'],
            'user_name' => $transaction['user_name'],
            'user_email' => $transaction['email'],
            'type' => ucfirst($transaction['type']),
            'amount' => $transaction['amount'],
            'fee' => $transaction['fee'],
            'wallet_address' => $transaction['wallet_address'],
            'payment_method' => $transaction['payment_method'],
            'description' => $transaction['description'],
            'status' => $transaction['status'],
            'reference_id' => $transaction['reference_id'],
            'reference_type' => $transaction['reference_type'],
            'created_at' => $transaction['created_at'],
            'updated_at' => $transaction['updated_at']
        ]
    ];
    
    // Если есть связанная инвестиция, получаем её данные
    if ($transaction['reference_type'] === 'investment' && $transaction['reference_id']) {
        $stmt = $db->prepare("
            SELECT plan_id, amount as investment_amount, daily_rate, duration_days, start_date, end_date
            FROM investments 
            WHERE id = ?
        ");
        $stmt->execute([$transaction['reference_id']]);
        $investment = $stmt->fetch();
        
        if ($investment) {
            $response['transaction']['investment'] = $investment;
        }
    }
    
    echo json_encode($response);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Internal server error']);
    
    // Логируем ошибку
    error_log("Error getting transaction {$transaction_id}: " . $e->getMessage());
}
?>
