<?php
session_start();
require_once '../../config/database.php';
require_once '../../includes/functions.php';

// Проверка авторизации и прав администратора
if (!isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Unauthorized']);
    exit;
}

$user = getUserById($_SESSION['user_id']);
if (!$user || $user['role'] !== 'admin') {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'Access denied']);
    exit;
}

$db = getDB();
$action = $_POST['action'] ?? '';

try {
    switch ($action) {
        case 'update_transaction_status':
            $transaction_id = intval($_POST['transaction_id'] ?? 0);
            $status = $_POST['status'] ?? '';
            
            if (!$transaction_id || !in_array($status, ['pending', 'completed', 'failed', 'processing'])) {
                throw new Exception('Invalid parameters');
            }
            
            // Получаем транзакцию
            $stmt = $db->prepare("SELECT * FROM transactions WHERE id = ?");
            $stmt->execute([$transaction_id]);
            $transaction = $stmt->fetch();
            
            if (!$transaction) {
                throw new Exception('Transaction not found');
            }
            
            // Обновляем статус
            $stmt = $db->prepare("UPDATE transactions SET status = ?, updated_at = NOW() WHERE id = ?");
            $stmt->execute([$status, $transaction_id]);
            
            // Если транзакция одобрена, обновляем баланс пользователя
            if ($status === 'completed' && $transaction['status'] === 'pending') {
                if ($transaction['type'] === 'deposit') {
                    // Пополнение баланса
                    $stmt = $db->prepare("
                        UPDATE user_balances 
                        SET total_balance = total_balance + ?, 
                            available_balance = available_balance + ?,
                            updated_at = NOW()
                        WHERE user_id = ?
                    ");
                    $stmt->execute([$transaction['amount'], $transaction['amount'], $transaction['user_id']]);
                    
                } elseif ($transaction['type'] === 'withdrawal') {
                    // Вывод средств уже заблокирован при создании запроса
                    // Просто обновляем статус
                }
            }
            
            // Логируем действие
            logAction($_SESSION['user_id'], 'transaction_status_updated', 
                "Updated transaction #{$transaction_id} status to {$status}");
            
            echo json_encode(['success' => true, 'message' => 'Transaction updated successfully']);
            break;
            
        case 'update_user_status':
            $user_id = intval($_POST['user_id'] ?? 0);
            $status = $_POST['status'] ?? '';
            
            if (!$user_id || !in_array($status, ['active', 'suspended', 'banned'])) {
                throw new Exception('Invalid parameters');
            }
            
            $stmt = $db->prepare("UPDATE users SET status = ?, updated_at = NOW() WHERE id = ?");
            $stmt->execute([$status, $user_id]);
            
            logAction($_SESSION['user_id'], 'user_status_updated', 
                "Updated user #{$user_id} status to {$status}");
            
            echo json_encode(['success' => true, 'message' => 'User status updated successfully']);
            break;
            
        case 'delete_user':
            $user_id = intval($_POST['user_id'] ?? 0);
            
            if (!$user_id || $user_id === $_SESSION['user_id']) {
                throw new Exception('Invalid user ID or cannot delete yourself');
            }
            
            // Проверяем, есть ли активные инвестиции
            $stmt = $db->prepare("SELECT COUNT(*) FROM investments WHERE user_id = ? AND status = 'active'");
            $stmt->execute([$user_id]);
            $active_investments = $stmt->fetchColumn();
            
            if ($active_investments > 0) {
                throw new Exception('Cannot delete user with active investments');
            }
            
            // Удаляем пользователя (каскадное удаление настроено в БД)
            $stmt = $db->prepare("DELETE FROM users WHERE id = ?");
            $stmt->execute([$user_id]);
            
            logAction($_SESSION['user_id'], 'user_deleted', "Deleted user #{$user_id}");
            
            echo json_encode(['success' => true, 'message' => 'User deleted successfully']);
            break;
            
        case 'distribute_profits':
            $amount_per_user = floatval($_POST['amount_per_user'] ?? 0);
            $description = $_POST['description'] ?? 'Profit distribution';
            
            if ($amount_per_user <= 0) {
                throw new Exception('Invalid amount');
            }
            
            // Получаем всех активных пользователей с активными инвестициями
            $stmt = $db->query("
                SELECT DISTINCT u.id, u.first_name, u.last_name, u.email
                FROM users u
                JOIN investments i ON u.id = i.user_id
                WHERE u.status = 'active' AND i.status = 'active'
            ");
            $users = $stmt->fetchAll();
            
            $total_distributed = 0;
            $users_count = 0;
            
            $db->beginTransaction();
            
            foreach ($users as $user) {
                // Добавляем прибыль в транзакции
                $stmt = $db->prepare("
                    INSERT INTO transactions (
                        user_id, type, amount, description, status, created_at
                    ) VALUES (?, 'profit', ?, ?, 'completed', NOW())
                ");
                $stmt->execute([$user['id'], $amount_per_user, $description]);
                
                // Обновляем баланс
                $stmt = $db->prepare("
                    UPDATE user_balances 
                    SET total_balance = total_balance + ?, 
                        available_balance = available_balance + ?,
                        updated_at = NOW()
                    WHERE user_id = ?
                ");
                $stmt->execute([$amount_per_user, $amount_per_user, $user['id']]);
                
                $total_distributed += $amount_per_user;
                $users_count++;
            }
            
            $db->commit();
            
            logAction($_SESSION['user_id'], 'profits_distributed', 
                "Distributed ${amount_per_user} to {$users_count} users. Total: ${total_distributed}");
            
            echo json_encode([
                'success' => true, 
                'message' => "Profits distributed successfully to {$users_count} users",
                'total_distributed' => $total_distributed,
                'users_count' => $users_count
            ]);
            break;
            
        case 'update_investment_status':
            $investment_id = intval($_POST['investment_id'] ?? 0);
            $status = $_POST['status'] ?? '';
            
            if (!$investment_id || !in_array($status, ['active', 'completed', 'cancelled'])) {
                throw new Exception('Invalid parameters');
            }
            
            $stmt = $db->prepare("UPDATE investments SET status = ?, updated_at = NOW() WHERE id = ?");
            $stmt->execute([$status, $investment_id]);
            
            logAction($_SESSION['user_id'], 'investment_status_updated', 
                "Updated investment #{$investment_id} status to {$status}");
            
            echo json_encode(['success' => true, 'message' => 'Investment status updated successfully']);
            break;
            
        case 'send_notification':
            $user_id = intval($_POST['user_id'] ?? 0);
            $title = trim($_POST['title'] ?? '');
            $message = trim($_POST['message'] ?? '');
            $type = $_POST['type'] ?? 'info';
            
            if (!$title || !$message) {
                throw new Exception('Title and message are required');
            }
            
            if (!in_array($type, ['info', 'success', 'warning', 'error'])) {
                $type = 'info';
            }
            
            if ($user_id > 0) {
                // Отправка уведомления конкретному пользователю
                $stmt = $db->prepare("
                    INSERT INTO notifications (user_id, title, message, type, created_at) 
                    VALUES (?, ?, ?, ?, NOW())
                ");
                $stmt->execute([$user_id, $title, $message, $type]);
                $recipients = 1;
            } else {
                // Отправка уведомления всем пользователям
                $stmt = $db->query("SELECT id FROM users WHERE status = 'active'");
                $users = $stmt->fetchAll();
                
                $stmt = $db->prepare("
                    INSERT INTO notifications (user_id, title, message, type, created_at) 
                    VALUES (?, ?, ?, ?, NOW())
                ");
                
                $recipients = 0;
                foreach ($users as $user) {
                    $stmt->execute([$user['id'], $title, $message, $type]);
                    $recipients++;
                }
            }
            
            logAction($_SESSION['user_id'], 'notification_sent', 
                "Sent notification '{$title}' to {$recipients} user(s)");
            
            echo json_encode([
                'success' => true, 
                'message' => "Notification sent to {$recipients} user(s)"
            ]);
            break;
            
        case 'update_site_setting':
            $key = $_POST['key'] ?? '';
            $value = $_POST['value'] ?? '';
            
            if (!$key) {
                throw new Exception('Setting key is required');
            }
            
            setSiteSetting($key, $value);
            
            logAction($_SESSION['user_id'], 'site_setting_updated', 
                "Updated setting '{$key}' to '{$value}'");
            
            echo json_encode(['success' => true, 'message' => 'Setting updated successfully']);
            break;
            
        default:
            throw new Exception('Invalid action');
    }
    
} catch (Exception $e) {
    if ($db->inTransaction()) {
        $db->rollBack();
    }
    
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => $e->getMessage()]);
    
    // Логируем ошибку
    logAction($_SESSION['user_id'], 'admin_action_failed', 
        "Action '{$action}' failed: " . $e->getMessage());
}
?>
