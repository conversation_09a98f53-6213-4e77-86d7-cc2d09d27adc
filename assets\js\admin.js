// Astragenix Admin Panel JavaScript

document.addEventListener('DOMContentLoaded', function() {
    initTransactionActions();
    initProfitDistribution();
    initAdminModals();
    initAdminTables();
    initBulkActions();
    
    console.log('Admin panel initialized');
});

// Transaction approval/rejection
function initTransactionActions() {
    const approveButtons = document.querySelectorAll('.approve-transaction');
    const rejectButtons = document.querySelectorAll('.reject-transaction');
    
    approveButtons.forEach(button => {
        button.addEventListener('click', function() {
            const transactionId = this.getAttribute('data-id');
            handleTransaction(transactionId, 'approve', this);
        });
    });
    
    rejectButtons.forEach(button => {
        button.addEventListener('click', function() {
            const transactionId = this.getAttribute('data-id');
            handleTransaction(transactionId, 'reject', this);
        });
    });
}

function handleTransaction(transactionId, action, button) {
    // Show confirmation dialog
    const actionText = action === 'approve' ? 'approve' : 'reject';
    if (!confirm(`Are you sure you want to ${actionText} this transaction?`)) {
        return;
    }
    
    // Add loading state
    button.classList.add('loading');
    button.disabled = true;
    
    // Send request
    fetch('../api/admin/handle-transaction.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        },
        body: JSON.stringify({
            transaction_id: transactionId,
            action: action
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Remove the transaction row with animation
            const transactionItem = button.closest('.transaction-item, tr');
            transactionItem.style.transition = 'opacity 0.3s ease, transform 0.3s ease';
            transactionItem.style.opacity = '0';
            transactionItem.style.transform = 'translateX(100px)';
            
            setTimeout(() => {
                transactionItem.remove();
            }, 300);
            
            // Show success notification
            showNotification(data.message || `Transaction ${actionText}d successfully!`, 'success');
            
            // Update pending count
            updatePendingCount();
        } else {
            showNotification(data.message || `Failed to ${actionText} transaction`, 'error');
            button.classList.remove('loading');
            button.disabled = false;
        }
    })
    .catch(error => {
        console.error('Error handling transaction:', error);
        showNotification('Network error. Please try again.', 'error');
        button.classList.remove('loading');
        button.disabled = false;
    });
}

// Profit distribution
function initProfitDistribution() {
    const distributeButton = document.getElementById('distribute-profits');
    const distributionForm = document.getElementById('profit-distribution-form');
    
    if (distributeButton) {
        distributeButton.addEventListener('click', function() {
            if (distributionForm) {
                // Show form if it exists
                distributionForm.scrollIntoView({ behavior: 'smooth' });
            } else {
                // Open profit distribution modal or redirect
                openProfitDistributionModal();
            }
        });
    }
    
    // Handle distribution form submission
    if (distributionForm) {
        distributionForm.addEventListener('submit', function(e) {
            e.preventDefault();
            handleProfitDistribution(this);
        });
    }
}

function openProfitDistributionModal() {
    const modal = createModal('Distribute Profits', `
        <form id="profit-distribution-modal-form">
            <div class="form-group">
                <label for="profit-percentage">Profit Percentage (%)</label>
                <input type="number" id="profit-percentage" name="profit_percentage" 
                       min="0" max="10" step="0.1" value="1.5" required>
                <small>Percentage of investment amount to distribute as profit</small>
            </div>
            
            <div class="form-group">
                <label for="distribution-note">Distribution Note</label>
                <textarea id="distribution-note" name="note" rows="3" 
                          placeholder="Optional note for this distribution..."></textarea>
            </div>
            
            <div class="distribution-preview" id="distribution-preview" style="display: none;">
                <h4>Distribution Preview</h4>
                <div class="distribution-summary">
                    <div class="summary-item">
                        <div class="summary-number" id="preview-investors">0</div>
                        <div class="summary-label">Active Investors</div>
                    </div>
                    <div class="summary-item">
                        <div class="summary-number" id="preview-amount">$0.00</div>
                        <div class="summary-label">Total Distribution</div>
                    </div>
                </div>
            </div>
        </form>
    `, [
        {
            text: 'Preview',
            class: 'btn btn-outline',
            onclick: () => previewProfitDistribution()
        },
        {
            text: 'Distribute',
            class: 'btn btn-primary',
            onclick: () => executeProfitDistribution()
        }
    ]);
    
    // Add input listener for real-time preview
    const percentageInput = modal.querySelector('#profit-percentage');
    percentageInput.addEventListener('input', debounce(previewProfitDistribution, 500));
}

function previewProfitDistribution() {
    const percentage = document.getElementById('profit-percentage').value;
    
    if (!percentage || percentage <= 0) {
        document.getElementById('distribution-preview').style.display = 'none';
        return;
    }
    
    fetch('../api/admin/preview-profit-distribution.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        },
        body: JSON.stringify({ profit_percentage: percentage })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            document.getElementById('preview-investors').textContent = data.investor_count;
            document.getElementById('preview-amount').textContent = formatCurrency(data.total_amount);
            document.getElementById('distribution-preview').style.display = 'block';
        }
    })
    .catch(error => {
        console.error('Error previewing distribution:', error);
    });
}

function executeProfitDistribution() {
    const form = document.getElementById('profit-distribution-modal-form');
    const formData = new FormData(form);
    
    if (!confirm('Are you sure you want to distribute profits to all active investors?')) {
        return;
    }
    
    // Show loading state
    const distributeBtn = document.querySelector('.modal-footer .btn-primary');
    distributeBtn.classList.add('loading');
    distributeBtn.disabled = true;
    
    fetch('../api/admin/distribute-profits.php', {
        method: 'POST',
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        },
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification(data.message || 'Profits distributed successfully!', 'success');
            closeModal();
            
            // Refresh page after short delay
            setTimeout(() => {
                window.location.reload();
            }, 2000);
        } else {
            showNotification(data.message || 'Failed to distribute profits', 'error');
            distributeBtn.classList.remove('loading');
            distributeBtn.disabled = false;
        }
    })
    .catch(error => {
        console.error('Error distributing profits:', error);
        showNotification('Network error. Please try again.', 'error');
        distributeBtn.classList.remove('loading');
        distributeBtn.disabled = false;
    });
}

// Admin modals
function initAdminModals() {
    // Close modal when clicking outside
    document.addEventListener('click', function(e) {
        if (e.target.classList.contains('admin-modal')) {
            closeModal();
        }
    });
    
    // Close modal with Escape key
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            closeModal();
        }
    });
}

function createModal(title, content, buttons = []) {
    // Remove existing modal
    const existingModal = document.querySelector('.admin-modal');
    if (existingModal) {
        existingModal.remove();
    }
    
    const modal = document.createElement('div');
    modal.className = 'admin-modal';
    modal.innerHTML = `
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">${title}</h3>
                <button class="modal-close" onclick="closeModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                ${content}
            </div>
            <div class="modal-footer">
                ${buttons.map(btn => `
                    <button class="${btn.class}" onclick="${btn.onclick ? btn.onclick.toString().slice(6, -1) : 'closeModal()'}">${btn.text}</button>
                `).join('')}
                <button class="btn btn-outline" onclick="closeModal()">Cancel</button>
            </div>
        </div>
    `;
    
    document.body.appendChild(modal);
    
    // Show modal with animation
    setTimeout(() => {
        modal.classList.add('active');
    }, 10);
    
    return modal;
}

function closeModal() {
    const modal = document.querySelector('.admin-modal');
    if (modal) {
        modal.classList.remove('active');
        setTimeout(() => {
            modal.remove();
        }, 300);
    }
}

// Admin tables
function initAdminTables() {
    // Add sorting functionality
    const sortableHeaders = document.querySelectorAll('.admin-table th[data-sort]');
    sortableHeaders.forEach(header => {
        header.style.cursor = 'pointer';
        header.addEventListener('click', function() {
            sortTable(this);
        });
    });
    
    // Add row selection
    const selectAllCheckbox = document.querySelector('.select-all');
    if (selectAllCheckbox) {
        selectAllCheckbox.addEventListener('change', function() {
            const rowCheckboxes = document.querySelectorAll('.row-select');
            rowCheckboxes.forEach(checkbox => {
                checkbox.checked = this.checked;
            });
            updateBulkActions();
        });
    }
    
    const rowCheckboxes = document.querySelectorAll('.row-select');
    rowCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', updateBulkActions);
    });
}

function sortTable(header) {
    const table = header.closest('table');
    const tbody = table.querySelector('tbody');
    const rows = Array.from(tbody.querySelectorAll('tr'));
    const columnIndex = Array.from(header.parentNode.children).indexOf(header);
    const sortDirection = header.dataset.sort === 'asc' ? 'desc' : 'asc';
    
    // Update sort indicators
    table.querySelectorAll('th').forEach(th => {
        th.classList.remove('sort-asc', 'sort-desc');
    });
    header.classList.add(`sort-${sortDirection}`);
    header.dataset.sort = sortDirection;
    
    // Sort rows
    rows.sort((a, b) => {
        const aValue = a.children[columnIndex].textContent.trim();
        const bValue = b.children[columnIndex].textContent.trim();
        
        // Try to parse as numbers
        const aNum = parseFloat(aValue.replace(/[^\d.-]/g, ''));
        const bNum = parseFloat(bValue.replace(/[^\d.-]/g, ''));
        
        if (!isNaN(aNum) && !isNaN(bNum)) {
            return sortDirection === 'asc' ? aNum - bNum : bNum - aNum;
        }
        
        // Sort as strings
        return sortDirection === 'asc' 
            ? aValue.localeCompare(bValue)
            : bValue.localeCompare(aValue);
    });
    
    // Reorder rows in DOM
    rows.forEach(row => tbody.appendChild(row));
}

// Bulk actions
function initBulkActions() {
    const bulkActionSelect = document.getElementById('bulk-action');
    const bulkActionButton = document.getElementById('bulk-action-apply');
    
    if (bulkActionButton) {
        bulkActionButton.addEventListener('click', function() {
            const action = bulkActionSelect.value;
            const selectedRows = getSelectedRows();
            
            if (!action) {
                showNotification('Please select an action', 'warning');
                return;
            }
            
            if (selectedRows.length === 0) {
                showNotification('Please select at least one item', 'warning');
                return;
            }
            
            executeBulkAction(action, selectedRows);
        });
    }
}

function updateBulkActions() {
    const selectedCount = document.querySelectorAll('.row-select:checked').length;
    const bulkActionsContainer = document.querySelector('.bulk-actions');
    
    if (bulkActionsContainer) {
        if (selectedCount > 0) {
            bulkActionsContainer.style.display = 'flex';
            bulkActionsContainer.querySelector('.selected-count').textContent = selectedCount;
        } else {
            bulkActionsContainer.style.display = 'none';
        }
    }
}

function getSelectedRows() {
    const selectedCheckboxes = document.querySelectorAll('.row-select:checked');
    return Array.from(selectedCheckboxes).map(checkbox => ({
        id: checkbox.value,
        row: checkbox.closest('tr')
    }));
}

function executeBulkAction(action, selectedRows) {
    const actionText = action.replace('_', ' ');
    
    if (!confirm(`Are you sure you want to ${actionText} ${selectedRows.length} item(s)?`)) {
        return;
    }
    
    const ids = selectedRows.map(row => row.id);
    
    fetch('../api/admin/bulk-action.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        },
        body: JSON.stringify({
            action: action,
            ids: ids
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification(data.message || `Bulk action completed successfully!`, 'success');
            
            // Remove processed rows
            selectedRows.forEach(row => {
                row.row.style.transition = 'opacity 0.3s ease';
                row.row.style.opacity = '0';
                setTimeout(() => {
                    row.row.remove();
                }, 300);
            });
            
            // Reset bulk actions
            setTimeout(() => {
                updateBulkActions();
            }, 300);
        } else {
            showNotification(data.message || 'Bulk action failed', 'error');
        }
    })
    .catch(error => {
        console.error('Error executing bulk action:', error);
        showNotification('Network error. Please try again.', 'error');
    });
}

// Update pending count
function updatePendingCount() {
    fetch('../api/admin/pending-count.php')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const countElements = document.querySelectorAll('.pending-count');
                countElements.forEach(element => {
                    element.textContent = data.count;
                });
                
                // Update alert if count is 0
                if (data.count === 0) {
                    const alert = document.querySelector('.alert-warning');
                    if (alert) {
                        alert.style.transition = 'opacity 0.3s ease';
                        alert.style.opacity = '0';
                        setTimeout(() => {
                            alert.remove();
                        }, 300);
                    }
                }
            }
        })
        .catch(error => {
            console.error('Error updating pending count:', error);
        });
}

// Utility functions
function formatCurrency(amount, currency = 'USDT') {
    return new Intl.NumberFormat('en-US', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    }).format(amount) + ' ' + currency;
}

function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Export functions for global use
window.AdminUtils = {
    createModal,
    closeModal,
    handleTransaction,
    executeProfitDistribution,
    formatCurrency,
    showNotification: window.AstragenixUtils?.showNotification || console.log
};
