<?php
// Система мультиязычности

$translations = [];
$currentLanguage = 'en';

// Функция для определения языка пользователя
function detectLanguage() {
    // Проверяем сессию
    if (isset($_SESSION['language'])) {
        return $_SESSION['language'];
    }
    
    // Проверяем GET параметр
    if (isset($_GET['lang']) && in_array($_GET['lang'], ['en', 'ru'])) {
        $_SESSION['language'] = $_GET['lang'];
        return $_GET['lang'];
    }
    
    // Определяем по IP (упрощенная версия)
    $userIP = getUserIP();
    if (isRussianIP($userIP)) {
        $_SESSION['language'] = 'ru';
        return 'ru';
    }
    
    // Определяем по браузеру
    $browserLang = substr($_SERVER['HTTP_ACCEPT_LANGUAGE'] ?? '', 0, 2);
    if ($browserLang === 'ru') {
        $_SESSION['language'] = 'ru';
        return 'ru';
    }
    
    // По умолчанию английский
    $_SESSION['language'] = 'en';
    return 'en';
}

// Функция для проверки российского IP (упрощенная)
function isRussianIP($ip) {
    // Здесь можно интегрировать с сервисом геолокации
    // Для примера возвращаем false
    return false;
}

// Функция для загрузки языкового файла
function loadLanguage($lang) {
    global $translations, $currentLanguage;
    
    $currentLanguage = $lang;
    $langFile = "languages/{$lang}.php";
    
    if (file_exists($langFile)) {
        include $langFile;
    } else {
        include "languages/en.php"; // Fallback на английский
    }
}

// Функция для получения перевода
function t($key, $params = []) {
    global $translations;
    
    $text = $translations[$key] ?? $key;
    
    // Замена параметров
    foreach ($params as $param => $value) {
        $text = str_replace("{{$param}}", $value, $text);
    }
    
    return $text;
}

// Функция для получения текущего языка
function getCurrentLanguage() {
    global $currentLanguage;
    return $currentLanguage;
}

// Функция для переключения языка
function switchLanguage($lang) {
    if (in_array($lang, ['en', 'ru'])) {
        $_SESSION['language'] = $lang;
        return true;
    }
    return false;
}
?>
