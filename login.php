<?php
session_start();
require_once 'config/database.php';
require_once 'includes/functions.php';
require_once 'includes/language.php';

// Определение языка
$lang = detectLanguage();
loadLanguage($lang);

// Проверка, если пользователь уже авторизован
if (isset($_SESSION['user_id'])) {
    redirect('dashboard.php');
}

$errors = [];
$success = '';

// Обработка формы входа
if ($_POST) {
    $email = trim($_POST['email'] ?? '');
    $password = $_POST['password'] ?? '';
    $rememberMe = isset($_POST['remember_me']);
    
    // Валидация
    if (empty($email)) {
        $errors[] = t('email') . ' ' . t('required');
    }
    
    if (empty($password)) {
        $errors[] = t('password') . ' ' . t('required');
    }
    
    // Если нет ошибок валидации, проверяем пользователя
    if (empty($errors)) {
        $user = getUserByEmail($email);
        
        if ($user && verifyPassword($password, $user['password'])) {
            // Проверяем статус пользователя
            if ($user['status'] !== 'active') {
                $errors[] = t('account_suspended');
            } else {
                // Успешная авторизация
                $_SESSION['user_id'] = $user['id'];
                $_SESSION['user_email'] = $user['email'];
                $_SESSION['user_role'] = $user['role'];
                
                // Обновляем информацию о последнем входе
                $db = getDB();
                $stmt = $db->prepare("
                    UPDATE users 
                    SET last_login_at = NOW(), last_login_ip = ? 
                    WHERE id = ?
                ");
                $stmt->execute([getUserIP(), $user['id']]);
                
                // Логируем вход
                logAction($user['id'], 'user_login', 'User logged in');
                
                // Обрабатываем "Запомнить меня"
                if ($rememberMe) {
                    $token = generateRandomString(64);
                    setcookie('remember_token', $token, time() + (30 * 24 * 60 * 60), '/', '', true, true);
                    
                    // Сохраняем токен в БД (в реальном проекте)
                    $stmt = $db->prepare("
                        UPDATE users 
                        SET remember_token = ? 
                        WHERE id = ?
                    ");
                    $stmt->execute([hash('sha256', $token), $user['id']]);
                }
                
                setFlashMessage('success', t('login_successful'));
                
                // Перенаправляем в зависимости от роли
                if ($user['role'] === 'admin') {
                    redirect('admin/dashboard.php');
                } else {
                    redirect('dashboard.php');
                }
            }
        } else {
            $errors[] = t('invalid_credentials');
            
            // Логируем неудачную попытку входа
            logAction(null, 'failed_login', "Failed login attempt for email: $email");
        }
    }
}

// Проверяем токен "Запомнить меня" при загрузке страницы
if (isset($_COOKIE['remember_token']) && !isset($_SESSION['user_id'])) {
    $token = $_COOKIE['remember_token'];
    $hashedToken = hash('sha256', $token);
    
    $db = getDB();
    $stmt = $db->prepare("SELECT * FROM users WHERE remember_token = ? AND status = 'active'");
    $stmt->execute([$hashedToken]);
    $user = $stmt->fetch();
    
    if ($user) {
        $_SESSION['user_id'] = $user['id'];
        $_SESSION['user_email'] = $user['email'];
        $_SESSION['user_role'] = $user['role'];
        
        redirect('dashboard.php');
    } else {
        // Удаляем недействительный токен
        setcookie('remember_token', '', time() - 3600, '/', '', true, true);
    }
}
?>
<?php
$pageTitle = t('login') . ' - Astragenix';
$pageDescription = t('login_description');
$currentPage = 'login';
$bodyClass = 'auth-page';
$additionalCSS = ['assets/css/auth.css'];
include 'includes/header.php';
?>
    <!-- Auth Section -->
    <section class="auth-section">
        <div class="container">
            <div class="auth-container">
                <div class="auth-background">
                    <div class="stars"></div>
                    <div class="floating-elements">
                        <div class="floating-element"></div>
                        <div class="floating-element"></div>
                        <div class="floating-element"></div>
                    </div>
                </div>

                <div class="auth-content">
                    <div class="auth-card animate-scale-in">
                        <div class="auth-header">
                            <div class="auth-logo">
                                <img src="assets/images/logo.png" alt="Astragenix" class="logo-img">
                                <span class="logo-text">Astragenix</span>
                            </div>
                            <h1 class="auth-title"><?php echo t('welcome_back'); ?></h1>
                            <p class="auth-subtitle"><?php echo t('sign_in_to_continue'); ?></p>
                        </div>
                
                <?php if (!empty($errors)): ?>
                    <div class="alert alert-error animate-shake">
                        <i class="fas fa-exclamation-triangle"></i>
                        <ul>
                            <?php foreach ($errors as $error): ?>
                                <li><?php echo h($error); ?></li>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                <?php endif; ?>
                
                <?php if ($success): ?>
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle"></i>
                        <div><?php echo h($success); ?></div>
                    </div>
                <?php endif; ?>
                
                <form method="POST" class="auth-form" data-validate>
                    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                    
                    <div class="form-group">
                        <label for="email"><?php echo t('email'); ?></label>
                        <input type="email" id="email" name="email" 
                               value="<?php echo h($_POST['email'] ?? ''); ?>" 
                               required autofocus>
                        <i class="fas fa-envelope form-icon"></i>
                    </div>
                    
                    <div class="form-group">
                        <label for="password"><?php echo t('password'); ?></label>
                        <input type="password" id="password" name="password" required>
                        <i class="fas fa-lock form-icon"></i>
                        <button type="button" class="password-toggle" onclick="togglePassword('password')">
                            <i class="fas fa-eye"></i>
                        </button>
                    </div>
                    
                    <div class="form-options">
                        <div class="checkbox-group">
                            <label class="checkbox-label">
                                <input type="checkbox" name="remember_me">
                                <span class="checkmark"></span>
                                <?php echo t('remember_me'); ?>
                            </label>
                        </div>
                        
                        <a href="forgot-password.php" class="auth-link">
                            <?php echo t('forgot_password'); ?>
                        </a>
                    </div>
                    
                    <button type="submit" class="btn btn-primary btn-large btn-full">
                        <i class="fas fa-sign-in-alt"></i>
                        <?php echo t('sign_in'); ?>
                    </button>
                </form>
                
                <div class="auth-divider">
                    <span><?php echo t('or'); ?></span>
                </div>
                
                <div class="social-login">
                    <button class="btn btn-outline btn-social">
                        <i class="fab fa-google"></i>
                        <?php echo t('continue_with_google'); ?>
                    </button>
                </div>
                
                <div class="auth-footer">
                    <p><?php echo t('dont_have_account'); ?> 
                       <a href="register.php" class="auth-link"><?php echo t('create_account'); ?></a>
                    </p>
                </div>
                
                <div class="quick-stats">
                    <div class="stat-grid">
                        <div class="stat-item">
                            <div class="stat-icon">
                                <i class="fas fa-users"></i>
                            </div>
                            <div class="stat-info">
                                <div class="stat-number">50K+</div>
                                <div class="stat-label"><?php echo t('active_users'); ?></div>
                            </div>
                        </div>
                        
                        <div class="stat-item">
                            <div class="stat-icon">
                                <i class="fas fa-chart-line"></i>
                            </div>
                            <div class="stat-info">
                                <div class="stat-number">99.8%</div>
                                <div class="stat-label"><?php echo t('uptime'); ?></div>
                            </div>
                        </div>
                        
                        <div class="stat-item">
                            <div class="stat-icon">
                                <i class="fas fa-shield-alt"></i>
                            </div>
                            <div class="stat-info">
                                <div class="stat-number">100%</div>
                                <div class="stat-label"><?php echo t('secure'); ?></div>
                            </div>
                        </div>
                    </div>
                </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    
<?php include 'includes/footer.php'; ?>

<style>
.form-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-xl);
}

.auth-divider {
    text-align: center;
    margin: var(--spacing-lg) 0;
    position: relative;
}

.auth-divider::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background: var(--dark-border);
}

.auth-divider span {
    background: rgba(30, 41, 59, 0.95);
    padding: 0 var(--spacing-md);
    color: var(--text-muted);
    font-size: 0.875rem;
}

.social-login {
    margin-bottom: var(--spacing-lg);
}

.btn-social {
    width: 100%;
    justify-content: center;
    gap: var(--spacing-sm);
}

.quick-stats {
    margin-top: var(--spacing-lg);
    padding-top: var(--spacing-lg);
    border-top: 1px solid var(--dark-border);
}

.stat-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: var(--spacing-md);
}

.stat-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm);
    background: rgba(255, 255, 255, 0.02);
    border-radius: var(--radius-md);
}

.stat-icon {
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--gradient-primary);
    border-radius: 50%;
    font-size: 0.875rem;
}

.stat-info {
    flex: 1;
}

.stat-number {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--text-primary);
    line-height: 1;
}

.stat-label {
    font-size: 0.75rem;
    color: var(--text-muted);
    line-height: 1;
}

@media (max-width: 480px) {
    .form-options {
        flex-direction: column;
        gap: var(--spacing-sm);
        align-items: flex-start;
    }
    
    .stat-grid {
        grid-template-columns: 1fr;
    }
    
    .stat-item {
        justify-content: center;
        text-align: center;
    }
}
</style>
