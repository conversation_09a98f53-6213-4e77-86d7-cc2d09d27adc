// Admin Users Management JavaScript

document.addEventListener('DOMContentLoaded', function() {
    initializeUserManagement();
});

function initializeUserManagement() {
    // Initialize select all functionality
    const selectAllCheckbox = document.getElementById('select-all');
    if (selectAllCheckbox) {
        selectAllCheckbox.addEventListener('change', function() {
            toggleAllUsers(this);
        });
    }
    
    // Initialize individual checkboxes
    const userCheckboxes = document.querySelectorAll('.user-checkbox');
    userCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', updateSelectAllState);
    });
    
    // Initialize search functionality
    initializeSearch();
}

// Toggle all user checkboxes
function toggleAllUsers(selectAllCheckbox) {
    const userCheckboxes = document.querySelectorAll('.user-checkbox');
    userCheckboxes.forEach(checkbox => {
        checkbox.checked = selectAllCheckbox.checked;
    });
    updateBulkActionsVisibility();
}

// Update select all checkbox state
function updateSelectAllState() {
    const selectAllCheckbox = document.getElementById('select-all');
    const userCheckboxes = document.querySelectorAll('.user-checkbox');
    const checkedBoxes = document.querySelectorAll('.user-checkbox:checked');
    
    if (checkedBoxes.length === 0) {
        selectAllCheckbox.indeterminate = false;
        selectAllCheckbox.checked = false;
    } else if (checkedBoxes.length === userCheckboxes.length) {
        selectAllCheckbox.indeterminate = false;
        selectAllCheckbox.checked = true;
    } else {
        selectAllCheckbox.indeterminate = true;
        selectAllCheckbox.checked = false;
    }
    
    updateBulkActionsVisibility();
}

// Update bulk actions visibility
function updateBulkActionsVisibility() {
    const checkedBoxes = document.querySelectorAll('.user-checkbox:checked');
    const bulkActionsBtn = document.querySelector('[onclick="showBulkActionsModal()"]');
    
    if (bulkActionsBtn) {
        if (checkedBoxes.length > 0) {
            bulkActionsBtn.style.display = 'inline-flex';
            bulkActionsBtn.innerHTML = `<i class="fas fa-tasks"></i> Bulk Actions (${checkedBoxes.length})`;
        } else {
            bulkActionsBtn.style.display = 'inline-flex';
            bulkActionsBtn.innerHTML = '<i class="fas fa-tasks"></i> Bulk Actions';
        }
    }
}

// View user details
function viewUser(userId) {
    fetch(`ajax/get_user.php?id=${userId}`)
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showUserModal(data.user, 'view');
        } else {
            showNotification('Failed to load user details', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('An error occurred', 'error');
    });
}

// Edit user
function editUser(userId) {
    fetch(`ajax/get_user.php?id=${userId}`)
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showUserModal(data.user, 'edit');
        } else {
            showNotification('Failed to load user details', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('An error occurred', 'error');
    });
}

// Suspend user
function suspendUser(userId) {
    if (confirm('Are you sure you want to suspend this user?')) {
        updateUserStatus(userId, 'suspended');
    }
}

// Activate user
function activateUser(userId) {
    if (confirm('Are you sure you want to activate this user?')) {
        updateUserStatus(userId, 'active');
    }
}

// Delete user
function deleteUser(userId) {
    if (confirm('Are you sure you want to delete this user? This action cannot be undone.')) {
        const formData = new FormData();
        formData.append('action', 'delete_user');
        formData.append('user_id', userId);
        
        fetch('ajax/admin_actions.php', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification('User deleted successfully', 'success');
                location.reload(); // Reload page to update list
            } else {
                showNotification(data.message || 'Failed to delete user', 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showNotification('An error occurred', 'error');
        });
    }
}

// Update user status
function updateUserStatus(userId, status) {
    const formData = new FormData();
    formData.append('action', 'update_user_status');
    formData.append('user_id', userId);
    formData.append('status', status);
    
    fetch('ajax/admin_actions.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('User status updated successfully', 'success');
            location.reload(); // Reload page to update list
        } else {
            showNotification(data.message || 'Failed to update user status', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('An error occurred', 'error');
    });
}

// Show user modal
function showUserModal(user, mode = 'view') {
    const modal = document.createElement('div');
    modal.className = 'admin-modal user-modal';
    
    const isEditable = mode === 'edit';
    
    modal.innerHTML = `
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">
                    ${isEditable ? 'Edit User' : 'User Details'} - ${user.first_name} ${user.last_name}
                </h3>
                <button class="modal-close" onclick="closeModal(this)">&times;</button>
            </div>
            <div class="modal-body">
                <form id="user-form" ${isEditable ? 'onsubmit="saveUser(event)"' : ''}>
                    <input type="hidden" name="user_id" value="${user.id}">
                    
                    <div class="form-grid">
                        <div class="form-group">
                            <label>First Name</label>
                            <input type="text" 
                                   name="first_name" 
                                   value="${user.first_name}" 
                                   ${isEditable ? '' : 'readonly'}
                                   class="form-input">
                        </div>
                        
                        <div class="form-group">
                            <label>Last Name</label>
                            <input type="text" 
                                   name="last_name" 
                                   value="${user.last_name}" 
                                   ${isEditable ? '' : 'readonly'}
                                   class="form-input">
                        </div>
                        
                        <div class="form-group">
                            <label>Email</label>
                            <input type="email" 
                                   name="email" 
                                   value="${user.email}" 
                                   ${isEditable ? '' : 'readonly'}
                                   class="form-input">
                        </div>
                        
                        <div class="form-group">
                            <label>Phone</label>
                            <input type="text" 
                                   name="phone" 
                                   value="${user.phone || ''}" 
                                   ${isEditable ? '' : 'readonly'}
                                   class="form-input">
                        </div>
                        
                        <div class="form-group">
                            <label>Status</label>
                            ${isEditable ? `
                                <select name="status" class="form-input">
                                    <option value="active" ${user.status === 'active' ? 'selected' : ''}>Active</option>
                                    <option value="suspended" ${user.status === 'suspended' ? 'selected' : ''}>Suspended</option>
                                    <option value="banned" ${user.status === 'banned' ? 'selected' : ''}>Banned</option>
                                </select>
                            ` : `
                                <input type="text" value="${user.status}" readonly class="form-input">
                            `}
                        </div>
                        
                        <div class="form-group">
                            <label>Role</label>
                            ${isEditable ? `
                                <select name="role" class="form-input">
                                    <option value="user" ${user.role === 'user' ? 'selected' : ''}>User</option>
                                    <option value="admin" ${user.role === 'admin' ? 'selected' : ''}>Admin</option>
                                </select>
                            ` : `
                                <input type="text" value="${user.role}" readonly class="form-input">
                            `}
                        </div>
                        
                        <div class="form-group">
                            <label>Total Balance</label>
                            <input type="text" 
                                   value="$${parseFloat(user.total_balance || 0).toFixed(2)}" 
                                   readonly 
                                   class="form-input">
                        </div>
                        
                        <div class="form-group">
                            <label>Available Balance</label>
                            <input type="text" 
                                   value="$${parseFloat(user.available_balance || 0).toFixed(2)}" 
                                   readonly 
                                   class="form-input">
                        </div>
                        
                        <div class="form-group">
                            <label>Joined Date</label>
                            <input type="text" 
                                   value="${new Date(user.created_at).toLocaleDateString()}" 
                                   readonly 
                                   class="form-input">
                        </div>
                        
                        <div class="form-group">
                            <label>Last Activity</label>
                            <input type="text" 
                                   value="${user.last_activity ? new Date(user.last_activity).toLocaleString() : 'Never'}" 
                                   readonly 
                                   class="form-input">
                        </div>
                        
                        <div class="form-group">
                            <label>Referral Code</label>
                            <input type="text" 
                                   value="${user.referral_code || 'Not set'}" 
                                   readonly 
                                   class="form-input">
                        </div>
                        
                        <div class="form-group">
                            <label>Referred By</label>
                            <input type="text" 
                                   value="${user.referred_by_name || 'Direct signup'}" 
                                   readonly 
                                   class="form-input">
                        </div>
                    </div>
                    
                    ${user.statistics ? `
                        <div class="user-statistics">
                            <h4>Statistics</h4>
                            <div class="stats-grid">
                                <div class="stat-item">
                                    <span class="stat-label">Total Invested:</span>
                                    <span class="stat-value">$${parseFloat(user.statistics.total_invested || 0).toFixed(2)}</span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-label">Active Investments:</span>
                                    <span class="stat-value">${user.statistics.active_investments || 0}</span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-label">Total Profits:</span>
                                    <span class="stat-value">$${parseFloat(user.statistics.total_profits || 0).toFixed(2)}</span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-label">Referrals:</span>
                                    <span class="stat-value">${user.statistics.total_referrals || 0}</span>
                                </div>
                            </div>
                        </div>
                    ` : ''}
                </form>
            </div>
            <div class="modal-footer">
                ${isEditable ? `
                    <button type="submit" form="user-form" class="btn btn-primary">
                        <i class="fas fa-save"></i>
                        Save Changes
                    </button>
                ` : ''}
                <button class="btn btn-secondary" onclick="closeModal(this)">Close</button>
            </div>
        </div>
    `;
    
    document.body.appendChild(modal);
    modal.classList.add('active');
}

// Save user changes
function saveUser(event) {
    event.preventDefault();
    
    const form = event.target;
    const formData = new FormData(form);
    formData.append('action', 'update_user');
    
    fetch('ajax/admin_actions.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('User updated successfully', 'success');
            closeModal(form);
            location.reload(); // Reload page to update list
        } else {
            showNotification(data.message || 'Failed to update user', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('An error occurred', 'error');
    });
}

// Show bulk actions modal
function showBulkActionsModal() {
    const checkedBoxes = document.querySelectorAll('.user-checkbox:checked');
    
    if (checkedBoxes.length === 0) {
        showNotification('Please select users first', 'warning');
        return;
    }
    
    const modal = document.createElement('div');
    modal.className = 'admin-modal bulk-actions-modal';
    
    modal.innerHTML = `
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">Bulk Actions (${checkedBoxes.length} users selected)</h3>
                <button class="modal-close" onclick="closeModal(this)">&times;</button>
            </div>
            <div class="modal-body">
                <div class="bulk-actions">
                    <button class="btn btn-success" onclick="bulkUpdateStatus('active')">
                        <i class="fas fa-check"></i>
                        Activate Selected
                    </button>
                    <button class="btn btn-warning" onclick="bulkUpdateStatus('suspended')">
                        <i class="fas fa-pause"></i>
                        Suspend Selected
                    </button>
                    <button class="btn btn-danger" onclick="bulkUpdateStatus('banned')">
                        <i class="fas fa-ban"></i>
                        Ban Selected
                    </button>
                    <button class="btn btn-primary" onclick="sendBulkNotification()">
                        <i class="fas fa-bell"></i>
                        Send Notification
                    </button>
                    <button class="btn btn-outline" onclick="exportSelectedUsers()">
                        <i class="fas fa-download"></i>
                        Export Selected
                    </button>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="closeModal(this)">Cancel</button>
            </div>
        </div>
    `;
    
    document.body.appendChild(modal);
    modal.classList.add('active');
}

// Bulk update status
function bulkUpdateStatus(status) {
    const checkedBoxes = document.querySelectorAll('.user-checkbox:checked');
    const userIds = Array.from(checkedBoxes).map(cb => cb.value);
    
    if (confirm(`Are you sure you want to ${status} ${userIds.length} users?`)) {
        const promises = userIds.map(userId => {
            const formData = new FormData();
            formData.append('action', 'update_user_status');
            formData.append('user_id', userId);
            formData.append('status', status);
            
            return fetch('ajax/admin_actions.php', {
                method: 'POST',
                body: formData
            }).then(response => response.json());
        });
        
        Promise.all(promises)
        .then(results => {
            const successful = results.filter(r => r.success).length;
            showNotification(`${successful} users updated successfully`, 'success');
            location.reload();
        })
        .catch(error => {
            console.error('Error:', error);
            showNotification('An error occurred during bulk update', 'error');
        });
    }
}

// Export users
function exportUsers() {
    const params = new URLSearchParams(window.location.search);
    params.set('export', '1');
    
    window.open(`ajax/export_users.php?${params.toString()}`, '_blank');
}

// Export selected users
function exportSelectedUsers() {
    const checkedBoxes = document.querySelectorAll('.user-checkbox:checked');
    const userIds = Array.from(checkedBoxes).map(cb => cb.value);
    
    if (userIds.length === 0) {
        showNotification('No users selected', 'warning');
        return;
    }
    
    const form = document.createElement('form');
    form.method = 'POST';
    form.action = 'ajax/export_users.php';
    form.target = '_blank';
    
    userIds.forEach(id => {
        const input = document.createElement('input');
        input.type = 'hidden';
        input.name = 'user_ids[]';
        input.value = id;
        form.appendChild(input);
    });
    
    document.body.appendChild(form);
    form.submit();
    document.body.removeChild(form);
}

// Initialize search functionality
function initializeSearch() {
    const searchInput = document.getElementById('search');
    if (searchInput) {
        let searchTimeout;
        
        searchInput.addEventListener('input', function() {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                // Auto-submit form after 500ms of no typing
                this.form.submit();
            }, 500);
        });
    }
}

// Send bulk notification
function sendBulkNotification() {
    const checkedBoxes = document.querySelectorAll('.user-checkbox:checked');
    const userIds = Array.from(checkedBoxes).map(cb => cb.value);
    
    // Show notification form modal
    const modal = document.createElement('div');
    modal.className = 'admin-modal notification-modal';
    
    modal.innerHTML = `
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">Send Notification to ${userIds.length} Users</h3>
                <button class="modal-close" onclick="closeModal(this)">&times;</button>
            </div>
            <div class="modal-body">
                <form id="notification-form" onsubmit="sendNotificationToUsers(event)">
                    <div class="form-group">
                        <label for="notification-title">Title</label>
                        <input type="text" 
                               id="notification-title" 
                               name="title" 
                               class="form-input" 
                               required>
                    </div>
                    
                    <div class="form-group">
                        <label for="notification-message">Message</label>
                        <textarea id="notification-message" 
                                  name="message" 
                                  class="form-input" 
                                  rows="4" 
                                  required></textarea>
                    </div>
                    
                    <div class="form-group">
                        <label for="notification-type">Type</label>
                        <select id="notification-type" name="type" class="form-input">
                            <option value="info">Info</option>
                            <option value="success">Success</option>
                            <option value="warning">Warning</option>
                            <option value="error">Error</option>
                        </select>
                    </div>
                    
                    <input type="hidden" name="user_ids" value="${userIds.join(',')}">
                </form>
            </div>
            <div class="modal-footer">
                <button type="submit" form="notification-form" class="btn btn-primary">
                    <i class="fas fa-paper-plane"></i>
                    Send Notification
                </button>
                <button class="btn btn-secondary" onclick="closeModal(this)">Cancel</button>
            </div>
        </div>
    `;
    
    document.body.appendChild(modal);
    modal.classList.add('active');
}

// Send notification to users
function sendNotificationToUsers(event) {
    event.preventDefault();
    
    const form = event.target;
    const formData = new FormData(form);
    const userIds = formData.get('user_ids').split(',');
    
    // Send notification to each user
    const promises = userIds.map(userId => {
        const notificationData = new FormData();
        notificationData.append('action', 'send_notification');
        notificationData.append('user_id', userId);
        notificationData.append('title', formData.get('title'));
        notificationData.append('message', formData.get('message'));
        notificationData.append('type', formData.get('type'));
        
        return fetch('ajax/admin_actions.php', {
            method: 'POST',
            body: notificationData
        }).then(response => response.json());
    });
    
    Promise.all(promises)
    .then(results => {
        const successful = results.filter(r => r.success).length;
        showNotification(`Notification sent to ${successful} users`, 'success');
        closeModal(form);
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('An error occurred while sending notifications', 'error');
    });
}

// Utility function to show notifications
function showNotification(message, type = 'info') {
    // Use the global notification function from admin.js
    if (window.AdminPanel && window.AdminPanel.showNotification) {
        window.AdminPanel.showNotification(message, type);
    } else {
        alert(message); // Fallback
    }
}
