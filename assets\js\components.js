// Additional component functions for Astragenix

// Investment calculators
function initCalculators() {
    const calculators = document.querySelectorAll('.calc-amount');
    
    calculators.forEach(calc => {
        calc.addEventListener('input', function() {
            const amount = parseFloat(this.value) || 0;
            const plan = this.getAttribute('data-plan');
            const card = this.closest('.plan-card');
            
            let dailyRate = 0;
            let duration = 30;
            
            switch(plan) {
                case 'starter':
                    dailyRate = 0.012;
                    duration = 30;
                    break;
                case 'professional':
                    dailyRate = 0.018;
                    duration = 60;
                    break;
                case 'enterprise':
                    dailyRate = 0.025;
                    duration = 90;
                    break;
            }
            
            const dailyProfit = amount * dailyRate;
            const totalProfit = dailyProfit * duration;
            
            const dailyProfitEl = card.querySelector('.daily-profit');
            const totalProfitEl = card.querySelector('.total-profit');
            
            if (dailyProfitEl) {
                dailyProfitEl.textContent = dailyProfit.toFixed(2) + ' USDT';
            }
            
            if (totalProfitEl) {
                totalProfitEl.textContent = totalProfit.toFixed(2) + ' USDT';
            }
        });
        
        // Trigger initial calculation
        calc.dispatchEvent(new Event('input'));
    });
}

// Cookie notice
function initCookieNotice() {
    const cookieNotice = document.getElementById('cookie-notice');
    const cookieAccept = document.getElementById('cookie-accept');
    const cookieSettings = document.getElementById('cookie-settings');
    
    // Show cookie notice if not accepted
    if (!localStorage.getItem('cookiesAccepted')) {
        setTimeout(() => {
            if (cookieNotice) {
                cookieNotice.style.display = 'block';
                cookieNotice.classList.add('animate-slide-in-up');
            }
        }, 2000);
    }
    
    if (cookieAccept) {
        cookieAccept.addEventListener('click', function() {
            localStorage.setItem('cookiesAccepted', 'true');
            if (cookieNotice) {
                cookieNotice.style.display = 'none';
            }
        });
    }
    
    if (cookieSettings) {
        cookieSettings.addEventListener('click', function() {
            // Open cookie settings modal (implement as needed)
            console.log('Cookie settings clicked');
        });
    }
}

// Live chat widget
function initLiveChat() {
    const chatToggle = document.getElementById('chat-toggle');
    const chatWindow = document.getElementById('chat-window');
    const chatClose = document.getElementById('chat-close');
    
    if (chatToggle) {
        chatToggle.addEventListener('click', function() {
            if (chatWindow) {
                chatWindow.classList.toggle('active');
            }
        });
    }
    
    if (chatClose) {
        chatClose.addEventListener('click', function() {
            if (chatWindow) {
                chatWindow.classList.remove('active');
            }
        });
    }
}

// Back to top button
function initBackToTop() {
    const backToTop = document.getElementById('back-to-top');
    
    if (backToTop) {
        window.addEventListener('scroll', function() {
            if (window.scrollY > 300) {
                backToTop.classList.add('visible');
            } else {
                backToTop.classList.remove('visible');
            }
        });
        
        backToTop.addEventListener('click', function() {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        });
    }
}

// Language switcher
function initLanguageSwitcher() {
    const langToggle = document.getElementById('lang-toggle');
    const langDropdown = document.getElementById('lang-dropdown');
    const languageSwitcher = document.querySelector('.language-switcher');
    
    if (langToggle && langDropdown) {
        langToggle.addEventListener('click', function(e) {
            e.stopPropagation();
            if (languageSwitcher) {
                languageSwitcher.classList.toggle('active');
            }
        });
        
        // Close dropdown when clicking outside
        document.addEventListener('click', function() {
            if (languageSwitcher) {
                languageSwitcher.classList.remove('active');
            }
        });
        
        langDropdown.addEventListener('click', function(e) {
            e.stopPropagation();
        });
    }
}

// Performance monitoring
function monitorPerformance() {
    // Monitor page load time
    window.addEventListener('load', function() {
        const loadTime = performance.now();
        console.log(`Page loaded in ${Math.round(loadTime)}ms`);
        
        // Send analytics if needed
        if (typeof gtag !== 'undefined') {
            gtag('event', 'page_load_time', {
                value: Math.round(loadTime)
            });
        }
    });
}

// Utility functions
function showNotification(message, type = 'info') {
    const container = document.getElementById('notification-container');
    if (!container) return;
    
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <div class="notification-content">
            <span class="notification-message">${message}</span>
            <button class="notification-close">&times;</button>
        </div>
    `;
    
    container.appendChild(notification);
    
    // Auto remove after 5 seconds
    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, 5000);
    
    // Manual close
    const closeBtn = notification.querySelector('.notification-close');
    if (closeBtn) {
        closeBtn.addEventListener('click', function() {
            notification.remove();
        });
    }
}

// Helper function for safe element selection
function safeQuerySelector(selector) {
    try {
        return document.querySelector(selector);
    } catch (e) {
        console.warn(`Invalid selector: ${selector}`);
        return null;
    }
}

// Form validation
function initFormValidation() {
    const forms = document.querySelectorAll('form[data-validate]');
    
    forms.forEach(form => {
        form.addEventListener('submit', function(e) {
            if (!validateForm(this)) {
                e.preventDefault();
            }
        });
    });
}

function validateForm(form) {
    let isValid = true;
    const inputs = form.querySelectorAll('input[required], textarea[required], select[required]');
    
    inputs.forEach(input => {
        if (!input.value.trim()) {
            showFieldError(input, 'This field is required');
            isValid = false;
        } else if (input.type === 'email' && !isValidEmail(input.value)) {
            showFieldError(input, 'Please enter a valid email address');
            isValid = false;
        } else {
            clearFieldError(input);
        }
    });
    
    return isValid;
}

function showFieldError(input, message) {
    clearFieldError(input);
    
    const errorDiv = document.createElement('div');
    errorDiv.className = 'form-error';
    errorDiv.textContent = message;
    
    input.parentNode.appendChild(errorDiv);
    input.classList.add('error');
}

function clearFieldError(input) {
    const existingError = input.parentNode.querySelector('.form-error');
    if (existingError) {
        existingError.remove();
    }
    input.classList.remove('error');
}

function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

// Modal functionality
function initModals() {
    const modalTriggers = document.querySelectorAll('[data-modal]');
    const modals = document.querySelectorAll('.modal');
    
    modalTriggers.forEach(trigger => {
        trigger.addEventListener('click', function(e) {
            e.preventDefault();
            const modalId = this.getAttribute('data-modal');
            const modal = document.getElementById(modalId);
            if (modal) {
                openModal(modal);
            }
        });
    });
    
    modals.forEach(modal => {
        const closeBtn = modal.querySelector('.modal-close');
        if (closeBtn) {
            closeBtn.addEventListener('click', function() {
                closeModal(modal);
            });
        }
        
        modal.addEventListener('click', function(e) {
            if (e.target === modal) {
                closeModal(modal);
            }
        });
    });
}

function openModal(modal) {
    modal.classList.add('active');
    document.body.classList.add('modal-open');
}

function closeModal(modal) {
    modal.classList.remove('active');
    document.body.classList.remove('modal-open');
}

// Tooltips
function initTooltips() {
    const tooltipTriggers = document.querySelectorAll('[data-tooltip]');
    
    tooltipTriggers.forEach(trigger => {
        trigger.addEventListener('mouseenter', function() {
            showTooltip(this);
        });
        
        trigger.addEventListener('mouseleave', function() {
            hideTooltip(this);
        });
    });
}

function showTooltip(element) {
    const tooltipText = element.getAttribute('data-tooltip');
    const tooltip = document.createElement('div');
    tooltip.className = 'tooltip-popup';
    tooltip.textContent = tooltipText;
    
    document.body.appendChild(tooltip);
    
    const rect = element.getBoundingClientRect();
    tooltip.style.left = rect.left + (rect.width / 2) - (tooltip.offsetWidth / 2) + 'px';
    tooltip.style.top = rect.top - tooltip.offsetHeight - 10 + 'px';
    
    element._tooltip = tooltip;
}

function hideTooltip(element) {
    if (element._tooltip) {
        element._tooltip.remove();
        delete element._tooltip;
    }
}

// Export functions for use in other scripts
window.AstragenixComponents = {
    showNotification,
    safeQuerySelector,
    openModal,
    closeModal,
    validateForm,
    isValidEmail
};
