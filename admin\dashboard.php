<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';
require_once '../includes/language.php';

// Проверка авторизации и прав администратора
if (!isset($_SESSION['user_id']) || !isAdmin($_SESSION['user_id'])) {
    redirect('../login.php');
}

// Определение языка
$lang = detectLanguage();
loadLanguage($lang);

// Получение данных пользователя
$user = getUserById($_SESSION['user_id']);

// Получение статистики
$db = getDB();

// Общая статистика
$stmt = $db->prepare("
    SELECT 
        (SELECT COUNT(*) FROM users WHERE status = 'active') as total_users,
        (SELECT COUNT(*) FROM users WHERE DATE(created_at) = CURDATE()) as new_users_today,
        (SELECT COUNT(*) FROM investments WHERE status = 'active') as active_investments,
        (SELECT COALESCE(SUM(amount), 0) FROM investments WHERE status = 'active') as total_invested,
        (SELECT COALESCE(SUM(amount), 0) FROM transactions WHERE type = 'profit' AND status = 'completed') as total_profits_paid,
        (SELECT COUNT(*) FROM transactions WHERE status = 'pending') as pending_transactions
");
$stmt->execute();
$stats = $stmt->fetch();

// Последние пользователи
$stmt = $db->prepare("
    SELECT id, first_name, last_name, email, created_at, status
    FROM users 
    ORDER BY created_at DESC 
    LIMIT 10
");
$stmt->execute();
$recentUsers = $stmt->fetchAll();

// Ожидающие транзакции
$stmt = $db->prepare("
    SELECT t.*, u.first_name, u.last_name, u.email
    FROM transactions t
    JOIN users u ON t.user_id = u.id
    WHERE t.status = 'pending'
    ORDER BY t.created_at DESC
    LIMIT 10
");
$stmt->execute();
$pendingTransactions = $stmt->fetchAll();

// Активные инвестиции
$stmt = $db->prepare("
    SELECT i.*, u.first_name, u.last_name, u.email
    FROM investments i
    JOIN users u ON i.user_id = u.id
    WHERE i.status = 'active'
    ORDER BY i.amount DESC
    LIMIT 10
");
$stmt->execute();
$topInvestments = $stmt->fetchAll();
?>
<!DOCTYPE html>
<html lang="<?php echo $lang; ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo t('admin_panel'); ?> - Astragenix</title>
    <link rel="stylesheet" href="../assets/css/main.css">
    <link rel="stylesheet" href="../assets/css/dashboard.css">
    <link rel="stylesheet" href="../assets/css/admin.css">
    <link rel="stylesheet" href="../assets/css/animations.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="dashboard-page admin-page">
    <!-- Sidebar -->
    <aside class="sidebar">
        <div class="sidebar-header">
            <div class="sidebar-logo">
                <img src="../assets/images/logo.png" alt="Astragenix" class="logo-img">
                <span class="logo-text">Astragenix Admin</span>
            </div>
            <button class="sidebar-toggle" id="sidebar-toggle">
                <i class="fas fa-bars"></i>
            </button>
        </div>
        
        <nav class="sidebar-nav">
            <a href="dashboard.php" class="nav-item active">
                <i class="fas fa-tachometer-alt"></i>
                <span><?php echo t('dashboard'); ?></span>
            </a>
            <a href="users.php" class="nav-item">
                <i class="fas fa-users"></i>
                <span><?php echo t('user_management'); ?></span>
            </a>
            <a href="transactions.php" class="nav-item">
                <i class="fas fa-exchange-alt"></i>
                <span><?php echo t('transaction_management'); ?></span>
            </a>
            <a href="investments.php" class="nav-item">
                <i class="fas fa-chart-line"></i>
                <span><?php echo t('investments'); ?></span>
            </a>
            <a href="profit-distribution.php" class="nav-item">
                <i class="fas fa-coins"></i>
                <span><?php echo t('profit_distribution'); ?></span>
            </a>
            <a href="news.php" class="nav-item">
                <i class="fas fa-newspaper"></i>
                <span><?php echo t('news_management'); ?></span>
            </a>
            <a href="support.php" class="nav-item">
                <i class="fas fa-headset"></i>
                <span><?php echo t('support_tickets'); ?></span>
            </a>
            <a href="settings.php" class="nav-item">
                <i class="fas fa-cog"></i>
                <span><?php echo t('site_settings'); ?></span>
            </a>
        </nav>
        
        <div class="sidebar-footer">
            <a href="../dashboard.php" class="nav-item">
                <i class="fas fa-user"></i>
                <span><?php echo t('user_panel'); ?></span>
            </a>
            <a href="../logout.php" class="nav-item logout">
                <i class="fas fa-sign-out-alt"></i>
                <span><?php echo t('logout'); ?></span>
            </a>
        </div>
    </aside>
    
    <!-- Main Content -->
    <main class="main-content">
        <!-- Header -->
        <header class="dashboard-header">
            <div class="header-left">
                <button class="mobile-menu-toggle" id="mobile-menu-toggle">
                    <i class="fas fa-bars"></i>
                </button>
                <h1 class="page-title"><?php echo t('admin_dashboard'); ?></h1>
            </div>
            
            <div class="header-right">
                <div class="admin-actions">
                    <button class="btn btn-primary" id="distribute-profits">
                        <i class="fas fa-coins"></i>
                        <?php echo t('distribute_profits'); ?>
                    </button>
                </div>
                
                <div class="user-menu">
                    <div class="user-avatar">
                        <img src="<?php echo $user['avatar'] ?: '../assets/images/default-avatar.png'; ?>" 
                             alt="<?php echo h($user['first_name']); ?>">
                    </div>
                    <div class="user-info">
                        <span class="user-name"><?php echo h($user['first_name'] . ' ' . $user['last_name']); ?></span>
                        <span class="user-role">Administrator</span>
                    </div>
                </div>
            </div>
        </header>
        
        <!-- Dashboard Content -->
        <div class="dashboard-content">
            <!-- Stats Grid -->
            <div class="stats-grid animate-fade-in">
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="stat-info">
                        <h3><?php echo number_format($stats['total_users']); ?></h3>
                        <p><?php echo t('total_users'); ?></p>
                    </div>
                    <div class="stat-trend positive">
                        <i class="fas fa-arrow-up"></i>
                        <span>+<?php echo $stats['new_users_today']; ?> today</span>
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <div class="stat-info">
                        <h3><?php echo number_format($stats['active_investments']); ?></h3>
                        <p><?php echo t('active_investments'); ?></p>
                    </div>
                    <div class="stat-trend positive">
                        <i class="fas fa-arrow-up"></i>
                        <span>Active</span>
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-wallet"></i>
                    </div>
                    <div class="stat-info">
                        <h3><?php echo formatCurrency($stats['total_invested']); ?></h3>
                        <p><?php echo t('total_invested'); ?></p>
                    </div>
                    <div class="stat-trend positive">
                        <i class="fas fa-arrow-up"></i>
                        <span>Growing</span>
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-coins"></i>
                    </div>
                    <div class="stat-info">
                        <h3><?php echo formatCurrency($stats['total_profits_paid']); ?></h3>
                        <p><?php echo t('total_profits_paid'); ?></p>
                    </div>
                    <div class="stat-trend positive">
                        <i class="fas fa-arrow-up"></i>
                        <span>Distributed</span>
                    </div>
                </div>
            </div>
            
            <!-- Alert for pending transactions -->
            <?php if ($stats['pending_transactions'] > 0): ?>
                <div class="alert alert-warning animate-slide-down">
                    <i class="fas fa-exclamation-triangle"></i>
                    <div>
                        <strong>Attention!</strong> 
                        You have <?php echo $stats['pending_transactions']; ?> pending transaction(s) that require review.
                        <a href="transactions.php" class="alert-link">Review now</a>
                    </div>
                </div>
            <?php endif; ?>
            
            <!-- Main Dashboard Grid -->
            <div class="dashboard-grid">
                <!-- Recent Users -->
                <div class="dashboard-card animate-fade-in-left">
                    <div class="card-header">
                        <h3><?php echo t('recent_users'); ?></h3>
                        <a href="users.php" class="card-action">
                            <?php echo t('view_all'); ?>
                            <i class="fas fa-arrow-right"></i>
                        </a>
                    </div>
                    <div class="card-content">
                        <div class="table-responsive">
                            <table class="admin-table">
                                <thead>
                                    <tr>
                                        <th><?php echo t('user'); ?></th>
                                        <th><?php echo t('email'); ?></th>
                                        <th><?php echo t('status'); ?></th>
                                        <th><?php echo t('registered'); ?></th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($recentUsers as $recentUser): ?>
                                        <tr>
                                            <td>
                                                <div class="user-cell">
                                                    <div class="user-avatar-sm">
                                                        <img src="../assets/images/default-avatar.png" alt="">
                                                    </div>
                                                    <span><?php echo h($recentUser['first_name'] . ' ' . $recentUser['last_name']); ?></span>
                                                </div>
                                            </td>
                                            <td><?php echo h($recentUser['email']); ?></td>
                                            <td>
                                                <span class="status-badge <?php echo $recentUser['status']; ?>">
                                                    <?php echo ucfirst($recentUser['status']); ?>
                                                </span>
                                            </td>
                                            <td><?php echo formatDate($recentUser['created_at']); ?></td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                
                <!-- Pending Transactions -->
                <div class="dashboard-card animate-fade-in-right">
                    <div class="card-header">
                        <h3><?php echo t('pending_transactions'); ?></h3>
                        <a href="transactions.php" class="card-action">
                            <?php echo t('view_all'); ?>
                            <i class="fas fa-arrow-right"></i>
                        </a>
                    </div>
                    <div class="card-content">
                        <?php if (!empty($pendingTransactions)): ?>
                            <div class="transaction-list">
                                <?php foreach ($pendingTransactions as $transaction): ?>
                                    <div class="transaction-item">
                                        <div class="transaction-icon <?php echo $transaction['type']; ?>">
                                            <i class="fas fa-<?php echo getTransactionIcon($transaction['type']); ?>"></i>
                                        </div>
                                        <div class="transaction-info">
                                            <h4><?php echo h($transaction['first_name'] . ' ' . $transaction['last_name']); ?></h4>
                                            <p><?php echo ucfirst($transaction['type']); ?> - <?php echo formatDate($transaction['created_at']); ?></p>
                                        </div>
                                        <div class="transaction-amount">
                                            <?php echo formatCurrency($transaction['amount']); ?>
                                        </div>
                                        <div class="transaction-actions">
                                            <button class="btn btn-sm btn-success approve-transaction" 
                                                    data-id="<?php echo $transaction['id']; ?>">
                                                <i class="fas fa-check"></i>
                                            </button>
                                            <button class="btn btn-sm btn-danger reject-transaction" 
                                                    data-id="<?php echo $transaction['id']; ?>">
                                                <i class="fas fa-times"></i>
                                            </button>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        <?php else: ?>
                            <div class="empty-state">
                                <i class="fas fa-check-circle"></i>
                                <h4>No pending transactions</h4>
                                <p>All transactions have been processed.</p>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
                
                <!-- Top Investments -->
                <div class="dashboard-card animate-fade-in-left">
                    <div class="card-header">
                        <h3><?php echo t('top_investments'); ?></h3>
                        <a href="investments.php" class="card-action">
                            <?php echo t('view_all'); ?>
                            <i class="fas fa-arrow-right"></i>
                        </a>
                    </div>
                    <div class="card-content">
                        <div class="investment-list">
                            <?php foreach ($topInvestments as $investment): ?>
                                <div class="investment-item">
                                    <div class="investment-info">
                                        <h4><?php echo h($investment['first_name'] . ' ' . $investment['last_name']); ?></h4>
                                        <p><?php echo ucfirst($investment['plan_id']); ?> Plan</p>
                                    </div>
                                    <div class="investment-amount">
                                        <span class="amount"><?php echo formatCurrency($investment['amount']); ?></span>
                                        <span class="profit">+<?php echo formatCurrency($investment['total_profit']); ?></span>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
                
                <!-- Quick Actions -->
                <div class="dashboard-card animate-fade-in-right">
                    <div class="card-header">
                        <h3><?php echo t('quick_actions'); ?></h3>
                    </div>
                    <div class="card-content">
                        <div class="quick-actions-grid">
                            <button class="quick-action-btn" onclick="location.href='profit-distribution.php'">
                                <i class="fas fa-coins"></i>
                                <span><?php echo t('distribute_profits'); ?></span>
                            </button>
                            
                            <button class="quick-action-btn" onclick="location.href='users.php'">
                                <i class="fas fa-user-plus"></i>
                                <span><?php echo t('add_user'); ?></span>
                            </button>
                            
                            <button class="quick-action-btn" onclick="location.href='news.php'">
                                <i class="fas fa-plus"></i>
                                <span><?php echo t('add_news'); ?></span>
                            </button>
                            
                            <button class="quick-action-btn" onclick="location.href='settings.php'">
                                <i class="fas fa-cog"></i>
                                <span><?php echo t('site_settings'); ?></span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>
    
    <script src="../assets/js/main.js"></script>
    <script src="../assets/js/dashboard.js"></script>
    <script src="../assets/js/admin.js"></script>
</body>
</html>

<?php
function getTransactionIcon($type) {
    $icons = [
        'deposit' => 'arrow-down',
        'withdrawal' => 'arrow-up',
        'profit' => 'chart-line',
        'bonus' => 'gift',
        'referral' => 'users'
    ];
    return $icons[$type] ?? 'exchange-alt';
}
?>
