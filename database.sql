-- Создание базы данных Astragenix
CREATE DATABASE IF NOT EXISTS astragenix CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE astragenix;

-- Таблица пользователей
CREATE TABLE users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    email VARCHAR(255) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    first_name VARCHAR(100) NOT NULL,
    last_name VARCHAR(100) NOT NULL,
    phone VARCHAR(20),
    avatar VARCHAR(255),
    referral_code VARCHAR(20) UNIQUE NOT NULL,
    referred_by INT,
    registration_ip VARCHAR(45),
    last_login_ip VARCHAR(45),
    last_login_at TIMESTAMP NULL,
    email_verified BOOLEAN DEFAULT FALSE,
    phone_verified BOOLEAN DEFAULT FALSE,
    two_factor_enabled BOOLEAN DEFAULT FALSE,
    two_factor_secret VARCHAR(32),
    status ENUM('active', 'suspended', 'banned') DEFAULT 'active',
    role ENUM('user', 'admin') DEFAULT 'user',
    language VARCHAR(5) DEFAULT 'en',
    timezone VARCHAR(50) DEFAULT 'UTC',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (referred_by) REFERENCES users(id) ON DELETE SET NULL
);

-- Таблица инвестиций
CREATE TABLE investments (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    plan_id VARCHAR(50) NOT NULL,
    amount DECIMAL(15,2) NOT NULL,
    daily_profit DECIMAL(15,2) NOT NULL,
    total_profit DECIMAL(15,2) DEFAULT 0,
    last_profit_date DATE,
    status ENUM('active', 'completed', 'cancelled') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Таблица транзакций
CREATE TABLE transactions (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    investment_id INT NULL,
    type ENUM('deposit', 'withdrawal', 'profit', 'bonus', 'referral') NOT NULL,
    amount DECIMAL(15,2) NOT NULL,
    fee DECIMAL(15,2) DEFAULT 0,
    description TEXT,
    payment_method VARCHAR(50),
    payment_address VARCHAR(255),
    transaction_hash VARCHAR(255),
    screenshot VARCHAR(255),
    admin_notes TEXT,
    status ENUM('pending', 'processing', 'completed', 'cancelled', 'rejected') DEFAULT 'pending',
    processed_by INT NULL,
    processed_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (investment_id) REFERENCES investments(id) ON DELETE SET NULL,
    FOREIGN KEY (processed_by) REFERENCES users(id) ON DELETE SET NULL
);

-- Таблица рефералов
CREATE TABLE referrals (
    id INT PRIMARY KEY AUTO_INCREMENT,
    referrer_id INT NOT NULL,
    referred_id INT NOT NULL,
    level INT NOT NULL DEFAULT 1,
    commission_rate DECIMAL(5,4) NOT NULL,
    total_earned DECIMAL(15,2) DEFAULT 0,
    status ENUM('active', 'inactive') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (referrer_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (referred_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_referral (referrer_id, referred_id)
);

-- Таблица заданий
CREATE TABLE tasks (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    type ENUM('daily_login', 'weekly_login', 'investment', 'referral', 'social', 'mining') NOT NULL,
    reward_amount DECIMAL(15,2) NOT NULL,
    requirement_value INT DEFAULT 1,
    is_repeatable BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Таблица выполненных заданий пользователями
CREATE TABLE user_tasks (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    task_id INT NOT NULL,
    progress INT DEFAULT 0,
    completed_at TIMESTAMP NULL,
    reward_claimed BOOLEAN DEFAULT FALSE,
    claimed_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (task_id) REFERENCES tasks(id) ON DELETE CASCADE
);

-- Таблица достижений
CREATE TABLE achievements (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    icon VARCHAR(100),
    requirement_type ENUM('investment_amount', 'total_profit', 'referral_count', 'login_streak') NOT NULL,
    requirement_value DECIMAL(15,2) NOT NULL,
    reward_amount DECIMAL(15,2) NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Таблица достижений пользователей
CREATE TABLE user_achievements (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    achievement_id INT NOT NULL,
    progress DECIMAL(15,2) DEFAULT 0,
    completed_at TIMESTAMP NULL,
    reward_claimed BOOLEAN DEFAULT FALSE,
    claimed_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (achievement_id) REFERENCES achievements(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_achievement (user_id, achievement_id)
);

-- Таблица новостей
CREATE TABLE news (
    id INT PRIMARY KEY AUTO_INCREMENT,
    title VARCHAR(255) NOT NULL,
    content TEXT NOT NULL,
    excerpt TEXT,
    image VARCHAR(255),
    author_id INT NOT NULL,
    is_published BOOLEAN DEFAULT FALSE,
    is_featured BOOLEAN DEFAULT FALSE,
    views_count INT DEFAULT 0,
    published_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (author_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Таблица тикетов поддержки
CREATE TABLE support_tickets (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    subject VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    priority ENUM('low', 'medium', 'high', 'urgent') DEFAULT 'medium',
    status ENUM('open', 'in_progress', 'resolved', 'closed') DEFAULT 'open',
    assigned_to INT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (assigned_to) REFERENCES users(id) ON DELETE SET NULL
);

-- Таблица ответов на тикеты
CREATE TABLE support_replies (
    id INT PRIMARY KEY AUTO_INCREMENT,
    ticket_id INT NOT NULL,
    user_id INT NOT NULL,
    message TEXT NOT NULL,
    is_admin_reply BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (ticket_id) REFERENCES support_tickets(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Таблица настроек сайта
CREATE TABLE site_settings (
    id INT PRIMARY KEY AUTO_INCREMENT,
    setting_key VARCHAR(100) UNIQUE NOT NULL,
    setting_value TEXT,
    setting_type ENUM('string', 'number', 'boolean', 'json') DEFAULT 'string',
    description TEXT,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Таблица логов активности
CREATE TABLE activity_logs (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NULL,
    action VARCHAR(100) NOT NULL,
    details TEXT,
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
);

-- Таблица лидербордов
CREATE TABLE leaderboards (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    category ENUM('referrals', 'investments', 'profits', 'mining') NOT NULL,
    value DECIMAL(15,2) NOT NULL,
    rank_position INT,
    period ENUM('daily', 'weekly', 'monthly', 'all_time') NOT NULL,
    period_start DATE NOT NULL,
    period_end DATE NOT NULL,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_leaderboard (user_id, category, period, period_start)
);

-- Таблица майнинга
CREATE TABLE mining_sessions (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    amount_mined DECIMAL(15,2) NOT NULL,
    mining_power DECIMAL(10,2) DEFAULT 1.0,
    session_duration INT NOT NULL, -- в секундах
    bonus_multiplier DECIMAL(5,2) DEFAULT 1.0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Вставка базовых настроек
INSERT INTO site_settings (setting_key, setting_value, setting_type, description) VALUES
('site_name', 'Astragenix', 'string', 'Название сайта'),
('site_description', 'Advanced Crypto Investment Platform', 'string', 'Описание сайта'),
('maintenance_mode', '0', 'boolean', 'Режим обслуживания'),
('registration_enabled', '1', 'boolean', 'Разрешена ли регистрация'),
('min_withdrawal', '10', 'number', 'Минимальная сумма вывода'),
('withdrawal_fee', '2', 'number', 'Комиссия за вывод в %'),
('referral_bonus', '50', 'number', 'Реферальный бонус в USDT'),
('starter_bonus', '50', 'number', 'Стартовый бонус в USDT'),
('mining_rate', '0.001', 'number', 'Базовая скорость майнинга в час');

-- Вставка базовых заданий
INSERT INTO tasks (name, description, type, reward_amount, requirement_value, is_repeatable) VALUES
('Daily Login', 'Login to your account daily', 'daily_login', 1.00, 1, TRUE),
('Weekly Login', 'Login 7 days in a row', 'weekly_login', 10.00, 7, TRUE),
('First Investment', 'Make your first investment', 'investment', 25.00, 1, FALSE),
('Invite 5 Friends', 'Invite 5 friends to join', 'referral', 50.00, 5, FALSE),
('Follow on Twitter', 'Follow us on Twitter', 'social', 5.00, 1, FALSE),
('Join Telegram', 'Join our Telegram channel', 'social', 5.00, 1, FALSE),
('Mine 100 USDT', 'Mine a total of 100 USDT', 'mining', 20.00, 100, FALSE);

-- Вставка достижений
INSERT INTO achievements (name, description, icon, requirement_type, requirement_value, reward_amount) VALUES
('First Investor', 'Make your first investment', 'fa-trophy', 'investment_amount', 100, 25.00),
('Big Investor', 'Invest a total of 10,000 USDT', 'fa-crown', 'investment_amount', 10000, 500.00),
('Profit Master', 'Earn 1,000 USDT in profits', 'fa-chart-line', 'total_profit', 1000, 100.00),
('Referral King', 'Refer 50 active users', 'fa-users', 'referral_count', 50, 250.00),
('Login Streak', 'Login 30 days in a row', 'fa-calendar', 'login_streak', 30, 75.00);

-- Создание индексов для оптимизации
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_referral_code ON users(referral_code);
CREATE INDEX idx_investments_user_id ON investments(user_id);
CREATE INDEX idx_transactions_user_id ON transactions(user_id);
CREATE INDEX idx_transactions_status ON transactions(status);
CREATE INDEX idx_referrals_referrer ON referrals(referrer_id);
CREATE INDEX idx_user_tasks_user_id ON user_tasks(user_id);
CREATE INDEX idx_activity_logs_user_id ON activity_logs(user_id);
CREATE INDEX idx_leaderboards_category ON leaderboards(category, period);
