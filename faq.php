<?php
session_start();
require_once 'config/database.php';
require_once 'includes/functions.php';
require_once 'includes/language.php';

// Определение языка
$lang = detectLanguage();
loadLanguage($lang);

// Проверка авторизации
$isLoggedIn = isset($_SESSION['user_id']);
$user = null;
if ($isLoggedIn) {
    $user = getUserById($_SESSION['user_id']);
}

// Настройки страницы
$pageTitle = 'Astragenix - ' . t('faq');
$pageDescription = t('faq_description');
$currentPage = 'faq';
$bodyClass = 'faq-page';

// Подключение заголовка
include 'includes/header.php';
?>

    <!-- Page Header -->
    <section class="page-header">
        <div class="container">
            <div class="page-header-content">
                <h1 class="page-title scroll-reveal"><?php echo t('frequently_asked_questions'); ?></h1>
                <p class="page-subtitle scroll-reveal"><?php echo t('faq_subtitle_detailed'); ?></p>
                <nav class="breadcrumb scroll-reveal">
                    <a href="index.php"><?php echo t('home'); ?></a>
                    <span class="breadcrumb-separator">/</span>
                    <span class="breadcrumb-current"><?php echo t('faq'); ?></span>
                </nav>
            </div>
        </div>
    </section>

    <!-- FAQ Search -->
    <section class="faq-search-section">
        <div class="container">
            <div class="faq-search-container scroll-reveal">
                <div class="search-box">
                    <input type="text" id="faq-search" class="search-input" 
                           placeholder="<?php echo t('search_faq'); ?>">
                    <button class="search-button">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
                <p class="search-help"><?php echo t('search_help'); ?></p>
            </div>
        </div>
    </section>

    <!-- FAQ Categories -->
    <section class="faq-categories-section">
        <div class="container">
            <div class="faq-categories scroll-reveal">
                <button class="category-btn active" data-category="all">
                    <i class="fas fa-th-large"></i>
                    <span><?php echo t('all_categories'); ?></span>
                </button>
                <button class="category-btn" data-category="getting-started">
                    <i class="fas fa-rocket"></i>
                    <span><?php echo t('getting_started'); ?></span>
                </button>
                <button class="category-btn" data-category="investments">
                    <i class="fas fa-chart-line"></i>
                    <span><?php echo t('investments'); ?></span>
                </button>
                <button class="category-btn" data-category="withdrawals">
                    <i class="fas fa-money-bill-wave"></i>
                    <span><?php echo t('withdrawals'); ?></span>
                </button>
                <button class="category-btn" data-category="security">
                    <i class="fas fa-shield-alt"></i>
                    <span><?php echo t('security'); ?></span>
                </button>
                <button class="category-btn" data-category="account">
                    <i class="fas fa-user"></i>
                    <span><?php echo t('account'); ?></span>
                </button>
            </div>
        </div>
    </section>

    <!-- FAQ Content -->
    <section class="faq-content-section">
        <div class="container">
            <div class="faq-accordion">
                <!-- Getting Started -->
                <div class="faq-category" data-category="getting-started">
                    <h3 class="category-title scroll-reveal">
                        <i class="fas fa-rocket"></i>
                        <?php echo t('getting_started'); ?>
                    </h3>
                    
                    <div class="faq-item scroll-reveal stagger-item">
                        <div class="faq-question">
                            <h4><?php echo t('how_to_register'); ?></h4>
                            <i class="fas fa-chevron-down"></i>
                        </div>
                        <div class="faq-answer">
                            <p><?php echo t('register_answer'); ?></p>
                        </div>
                    </div>
                    
                    <div class="faq-item scroll-reveal stagger-item">
                        <div class="faq-question">
                            <h4><?php echo t('verification_required'); ?></h4>
                            <i class="fas fa-chevron-down"></i>
                        </div>
                        <div class="faq-answer">
                            <p><?php echo t('verification_answer'); ?></p>
                        </div>
                    </div>
                    
                    <div class="faq-item scroll-reveal stagger-item">
                        <div class="faq-question">
                            <h4><?php echo t('first_investment'); ?></h4>
                            <i class="fas fa-chevron-down"></i>
                        </div>
                        <div class="faq-answer">
                            <p><?php echo t('first_investment_answer'); ?></p>
                        </div>
                    </div>
                </div>

                <!-- Investments -->
                <div class="faq-category" data-category="investments">
                    <h3 class="category-title scroll-reveal">
                        <i class="fas fa-chart-line"></i>
                        <?php echo t('investments'); ?>
                    </h3>
                    
                    <div class="faq-item scroll-reveal stagger-item">
                        <div class="faq-question">
                            <h4><?php echo t('minimum_investment_amount'); ?></h4>
                            <i class="fas fa-chevron-down"></i>
                        </div>
                        <div class="faq-answer">
                            <p><?php echo t('minimum_investment_detailed'); ?></p>
                        </div>
                    </div>
                    
                    <div class="faq-item scroll-reveal stagger-item">
                        <div class="faq-question">
                            <h4><?php echo t('investment_plans_difference'); ?></h4>
                            <i class="fas fa-chevron-down"></i>
                        </div>
                        <div class="faq-answer">
                            <p><?php echo t('plans_difference_answer'); ?></p>
                        </div>
                    </div>
                    
                    <div class="faq-item scroll-reveal stagger-item">
                        <div class="faq-question">
                            <h4><?php echo t('profit_calculation'); ?></h4>
                            <i class="fas fa-chevron-down"></i>
                        </div>
                        <div class="faq-answer">
                            <p><?php echo t('profit_calculation_answer'); ?></p>
                        </div>
                    </div>
                    
                    <div class="faq-item scroll-reveal stagger-item">
                        <div class="faq-question">
                            <h4><?php echo t('reinvestment_option'); ?></h4>
                            <i class="fas fa-chevron-down"></i>
                        </div>
                        <div class="faq-answer">
                            <p><?php echo t('reinvestment_answer'); ?></p>
                        </div>
                    </div>
                </div>

                <!-- Withdrawals -->
                <div class="faq-category" data-category="withdrawals">
                    <h3 class="category-title scroll-reveal">
                        <i class="fas fa-money-bill-wave"></i>
                        <?php echo t('withdrawals'); ?>
                    </h3>
                    
                    <div class="faq-item scroll-reveal stagger-item">
                        <div class="faq-question">
                            <h4><?php echo t('withdrawal_process'); ?></h4>
                            <i class="fas fa-chevron-down"></i>
                        </div>
                        <div class="faq-answer">
                            <p><?php echo t('withdrawal_process_answer'); ?></p>
                        </div>
                    </div>
                    
                    <div class="faq-item scroll-reveal stagger-item">
                        <div class="faq-question">
                            <h4><?php echo t('withdrawal_fees'); ?></h4>
                            <i class="fas fa-chevron-down"></i>
                        </div>
                        <div class="faq-answer">
                            <p><?php echo t('withdrawal_fees_answer'); ?></p>
                        </div>
                    </div>
                    
                    <div class="faq-item scroll-reveal stagger-item">
                        <div class="faq-question">
                            <h4><?php echo t('withdrawal_limits'); ?></h4>
                            <i class="fas fa-chevron-down"></i>
                        </div>
                        <div class="faq-answer">
                            <p><?php echo t('withdrawal_limits_answer'); ?></p>
                        </div>
                    </div>
                </div>

                <!-- Security -->
                <div class="faq-category" data-category="security">
                    <h3 class="category-title scroll-reveal">
                        <i class="fas fa-shield-alt"></i>
                        <?php echo t('security'); ?>
                    </h3>
                    
                    <div class="faq-item scroll-reveal stagger-item">
                        <div class="faq-question">
                            <h4><?php echo t('fund_security'); ?></h4>
                            <i class="fas fa-chevron-down"></i>
                        </div>
                        <div class="faq-answer">
                            <p><?php echo t('fund_security_answer'); ?></p>
                        </div>
                    </div>
                    
                    <div class="faq-item scroll-reveal stagger-item">
                        <div class="faq-question">
                            <h4><?php echo t('two_factor_auth'); ?></h4>
                            <i class="fas fa-chevron-down"></i>
                        </div>
                        <div class="faq-answer">
                            <p><?php echo t('two_factor_answer'); ?></p>
                        </div>
                    </div>
                    
                    <div class="faq-item scroll-reveal stagger-item">
                        <div class="faq-question">
                            <h4><?php echo t('password_security'); ?></h4>
                            <i class="fas fa-chevron-down"></i>
                        </div>
                        <div class="faq-answer">
                            <p><?php echo t('password_security_answer'); ?></p>
                        </div>
                    </div>
                </div>

                <!-- Account -->
                <div class="faq-category" data-category="account">
                    <h3 class="category-title scroll-reveal">
                        <i class="fas fa-user"></i>
                        <?php echo t('account_management'); ?>
                    </h3>
                    
                    <div class="faq-item scroll-reveal stagger-item">
                        <div class="faq-question">
                            <h4><?php echo t('profile_update'); ?></h4>
                            <i class="fas fa-chevron-down"></i>
                        </div>
                        <div class="faq-answer">
                            <p><?php echo t('profile_update_answer'); ?></p>
                        </div>
                    </div>
                    
                    <div class="faq-item scroll-reveal stagger-item">
                        <div class="faq-question">
                            <h4><?php echo t('forgot_password'); ?></h4>
                            <i class="fas fa-chevron-down"></i>
                        </div>
                        <div class="faq-answer">
                            <p><?php echo t('forgot_password_answer'); ?></p>
                        </div>
                    </div>
                    
                    <div class="faq-item scroll-reveal stagger-item">
                        <div class="faq-question">
                            <h4><?php echo t('account_closure'); ?></h4>
                            <i class="fas fa-chevron-down"></i>
                        </div>
                        <div class="faq-answer">
                            <p><?php echo t('account_closure_answer'); ?></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Still Have Questions -->
    <section class="faq-contact-section">
        <div class="container">
            <div class="faq-contact-content text-center scroll-reveal">
                <h2 class="section-title"><?php echo t('still_have_questions'); ?></h2>
                <p class="section-subtitle"><?php echo t('contact_support_text'); ?></p>
                
                <div class="contact-options">
                    <a href="contact.php" class="btn btn-primary btn-lg">
                        <i class="fas fa-envelope"></i>
                        <span><?php echo t('contact_support'); ?></span>
                    </a>
                    
                    <a href="#" class="btn btn-outline btn-lg" id="live-chat-btn">
                        <i class="fas fa-comments"></i>
                        <span><?php echo t('live_chat'); ?></span>
                    </a>
                </div>
                
                <div class="support-stats">
                    <div class="support-stat">
                        <div class="stat-number">< 5 <?php echo t('minutes'); ?></div>
                        <div class="stat-label"><?php echo t('average_response'); ?></div>
                    </div>
                    <div class="support-stat">
                        <div class="stat-number">24/7</div>
                        <div class="stat-label"><?php echo t('availability'); ?></div>
                    </div>
                    <div class="support-stat">
                        <div class="stat-number">98%</div>
                        <div class="stat-label"><?php echo t('satisfaction_rate'); ?></div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- FAQ JavaScript -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // FAQ Accordion
            const faqItems = document.querySelectorAll('.faq-item');
            faqItems.forEach(item => {
                const question = item.querySelector('.faq-question');
                question.addEventListener('click', function() {
                    const isActive = item.classList.contains('active');
                    
                    // Close all other items
                    faqItems.forEach(otherItem => {
                        otherItem.classList.remove('active');
                    });
                    
                    // Toggle current item
                    if (!isActive) {
                        item.classList.add('active');
                    }
                });
            });
            
            // Category filtering
            const categoryBtns = document.querySelectorAll('.category-btn');
            const faqCategories = document.querySelectorAll('.faq-category');
            
            categoryBtns.forEach(btn => {
                btn.addEventListener('click', function() {
                    const category = this.getAttribute('data-category');
                    
                    // Update active button
                    categoryBtns.forEach(otherBtn => {
                        otherBtn.classList.remove('active');
                    });
                    this.classList.add('active');
                    
                    // Show/hide categories
                    faqCategories.forEach(cat => {
                        if (category === 'all' || cat.getAttribute('data-category') === category) {
                            cat.style.display = 'block';
                        } else {
                            cat.style.display = 'none';
                        }
                    });
                });
            });
            
            // Search functionality
            const searchInput = document.getElementById('faq-search');
            searchInput.addEventListener('input', function() {
                const searchTerm = this.value.toLowerCase();
                
                faqItems.forEach(item => {
                    const question = item.querySelector('.faq-question h4').textContent.toLowerCase();
                    const answer = item.querySelector('.faq-answer p').textContent.toLowerCase();
                    
                    if (question.includes(searchTerm) || answer.includes(searchTerm)) {
                        item.style.display = 'block';
                        item.closest('.faq-category').style.display = 'block';
                    } else {
                        item.style.display = 'none';
                    }
                });
                
                // Hide empty categories
                if (searchTerm) {
                    faqCategories.forEach(cat => {
                        const visibleItems = cat.querySelectorAll('.faq-item[style*="block"]');
                        if (visibleItems.length === 0) {
                            cat.style.display = 'none';
                        }
                    });
                }
            });
        });
    </script>

<?php
// Подключение футера
include 'includes/footer.php';
?>
