/* Astragenix Admin Panel Styles */

/* Admin Page Specific */
.admin-page {
    background: var(--dark-bg);
}

.admin-page .sidebar-logo .logo-text {
    font-size: 1rem;
}

.admin-page .user-role {
    font-size: 0.75rem;
    color: var(--warning-color);
    font-weight: 500;
}

/* Admin Header */
.admin-actions {
    display: flex;
    gap: var(--spacing-md);
}

/* Alert Styles */
.alert {
    padding: var(--spacing-md);
    border-radius: var(--radius-md);
    margin-bottom: var(--spacing-lg);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    border: 1px solid;
}

.alert-warning {
    background: rgba(245, 158, 11, 0.1);
    border-color: rgba(245, 158, 11, 0.3);
    color: #fcd34d;
}

.alert-success {
    background: rgba(16, 185, 129, 0.1);
    border-color: rgba(16, 185, 129, 0.3);
    color: #6ee7b7;
}

.alert-error {
    background: rgba(239, 68, 68, 0.1);
    border-color: rgba(239, 68, 68, 0.3);
    color: #fca5a5;
}

.alert-info {
    background: rgba(99, 102, 241, 0.1);
    border-color: rgba(99, 102, 241, 0.3);
    color: #c7d2fe;
}

.alert i {
    font-size: 1.25rem;
}

.alert-link {
    color: inherit;
    text-decoration: underline;
    font-weight: 500;
    margin-left: var(--spacing-sm);
}

.alert-link:hover {
    opacity: 0.8;
}

/* Admin Tables */
.table-responsive {
    overflow-x: auto;
    border-radius: var(--radius-md);
    border: 1px solid var(--dark-border);
}

.admin-table {
    width: 100%;
    border-collapse: collapse;
    background: var(--dark-surface);
}

.admin-table th,
.admin-table td {
    padding: var(--spacing-md);
    text-align: left;
    border-bottom: 1px solid var(--dark-border);
}

.admin-table th {
    background: rgba(255, 255, 255, 0.02);
    font-weight: 600;
    color: var(--text-primary);
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.admin-table td {
    color: var(--text-secondary);
    font-size: 0.875rem;
}

.admin-table tr:hover {
    background: rgba(255, 255, 255, 0.02);
}

.admin-table tr:last-child td {
    border-bottom: none;
}

/* User Cell */
.user-cell {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.user-avatar-sm {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    overflow: hidden;
    border: 2px solid var(--dark-border);
}

.user-avatar-sm img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

/* Status Badges */
.status-badge {
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-sm);
    font-size: 0.75rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-badge.active {
    background: rgba(16, 185, 129, 0.2);
    color: var(--success-color);
}

.status-badge.suspended {
    background: rgba(245, 158, 11, 0.2);
    color: var(--warning-color);
}

.status-badge.banned {
    background: rgba(239, 68, 68, 0.2);
    color: var(--error-color);
}

.status-badge.pending {
    background: rgba(99, 102, 241, 0.2);
    color: var(--primary-color);
}

.status-badge.completed {
    background: rgba(16, 185, 129, 0.2);
    color: var(--success-color);
}

.status-badge.cancelled {
    background: rgba(100, 116, 139, 0.2);
    color: var(--text-muted);
}

.status-badge.rejected {
    background: rgba(239, 68, 68, 0.2);
    color: var(--error-color);
}

/* Transaction Actions */
.transaction-actions {
    display: flex;
    gap: var(--spacing-xs);
}

.btn-sm {
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: 0.75rem;
    min-width: auto;
}

.btn-success {
    background: var(--success-color);
    color: white;
    border: none;
}

.btn-success:hover {
    background: #059669;
    transform: translateY(-1px);
}

.btn-danger {
    background: var(--error-color);
    color: white;
    border: none;
}

.btn-danger:hover {
    background: #dc2626;
    transform: translateY(-1px);
}

/* Investment Amount Display */
.investment-amount {
    text-align: right;
}

.investment-amount .amount {
    display: block;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
}

.investment-amount .profit {
    font-size: 0.75rem;
    color: var(--success-color);
}

/* Quick Actions Grid */
.quick-actions-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-md);
}

.quick-action-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-lg);
    background: rgba(255, 255, 255, 0.02);
    border: 1px solid var(--dark-border);
    border-radius: var(--radius-md);
    color: var(--text-secondary);
    cursor: pointer;
    transition: var(--transition-fast);
    text-decoration: none;
}

.quick-action-btn:hover {
    background: rgba(99, 102, 241, 0.1);
    border-color: var(--primary-color);
    color: var(--text-primary);
    transform: translateY(-2px);
}

.quick-action-btn i {
    font-size: 1.5rem;
    color: var(--primary-color);
}

.quick-action-btn span {
    font-size: 0.875rem;
    font-weight: 500;
    text-align: center;
}

/* Admin Forms */
.admin-form {
    background: var(--dark-surface);
    border: 1px solid var(--dark-border);
    border-radius: var(--radius-lg);
    padding: var(--spacing-xl);
}

.admin-form .form-group {
    margin-bottom: var(--spacing-lg);
}

.admin-form label {
    display: block;
    margin-bottom: var(--spacing-sm);
    font-weight: 500;
    color: var(--text-primary);
}

.admin-form input,
.admin-form select,
.admin-form textarea {
    width: 100%;
    padding: var(--spacing-md);
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid var(--dark-border);
    border-radius: var(--radius-md);
    color: var(--text-primary);
    font-size: 0.875rem;
}

.admin-form input:focus,
.admin-form select:focus,
.admin-form textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

/* Admin Modals */
.admin-modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    opacity: 0;
    visibility: hidden;
    transition: var(--transition-normal);
}

.admin-modal.active {
    opacity: 1;
    visibility: visible;
}

.modal-content {
    background: var(--dark-surface);
    border: 1px solid var(--dark-border);
    border-radius: var(--radius-lg);
    padding: var(--spacing-xl);
    max-width: 500px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    transform: scale(0.9);
    transition: var(--transition-normal);
}

.admin-modal.active .modal-content {
    transform: scale(1);
}

.modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--spacing-lg);
    padding-bottom: var(--spacing-md);
    border-bottom: 1px solid var(--dark-border);
}

.modal-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
}

.modal-close {
    background: none;
    border: none;
    color: var(--text-muted);
    cursor: pointer;
    padding: var(--spacing-sm);
    border-radius: var(--radius-sm);
    transition: var(--transition-fast);
}

.modal-close:hover {
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-primary);
}

.modal-footer {
    display: flex;
    gap: var(--spacing-md);
    justify-content: flex-end;
    margin-top: var(--spacing-lg);
    padding-top: var(--spacing-md);
    border-top: 1px solid var(--dark-border);
}

/* Admin Statistics Cards */
.admin-stat-card {
    background: var(--dark-surface);
    border: 1px solid var(--dark-border);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    text-align: center;
    transition: var(--transition-fast);
}

.admin-stat-card:hover {
    border-color: var(--primary-color);
    transform: translateY(-2px);
}

.admin-stat-card .stat-icon {
    width: 60px;
    height: 60px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto var(--spacing-md);
    font-size: 1.5rem;
    color: white;
}

.admin-stat-card .stat-number {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
}

.admin-stat-card .stat-label {
    color: var(--text-secondary);
    font-size: 0.875rem;
}

/* Profit Distribution */
.profit-distribution-form {
    background: var(--dark-surface);
    border: 1px solid var(--dark-border);
    border-radius: var(--radius-lg);
    padding: var(--spacing-xl);
    margin-bottom: var(--spacing-xl);
}

.distribution-preview {
    background: rgba(16, 185, 129, 0.1);
    border: 1px solid rgba(16, 185, 129, 0.3);
    border-radius: var(--radius-md);
    padding: var(--spacing-lg);
    margin-top: var(--spacing-lg);
}

.distribution-summary {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
}

.summary-item {
    text-align: center;
}

.summary-item .summary-number {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--success-color);
    margin-bottom: var(--spacing-xs);
}

.summary-item .summary-label {
    color: var(--text-secondary);
    font-size: 0.875rem;
}

/* Responsive Admin Design */
@media (max-width: 768px) {
    .admin-actions {
        display: none;
    }
    
    .quick-actions-grid {
        grid-template-columns: 1fr;
    }
    
    .admin-table {
        font-size: 0.75rem;
    }
    
    .admin-table th,
    .admin-table td {
        padding: var(--spacing-sm);
    }
    
    .user-cell {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-xs);
    }
    
    .transaction-actions {
        flex-direction: column;
    }
    
    .modal-content {
        padding: var(--spacing-lg);
    }
    
    .modal-footer {
        flex-direction: column;
    }
}

@media (max-width: 480px) {
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .distribution-summary {
        grid-template-columns: 1fr;
    }
    
    .admin-form {
        padding: var(--spacing-lg);
    }
}

/* Additional Admin Dashboard Styles */

/* Admin Header for Dashboard */
.admin-header {
    background: var(--bg-card);
    border-bottom: 1px solid var(--border-color);
    padding: var(--spacing-md) 0;
    position: sticky;
    top: 0;
    z-index: 100;
    box-shadow: var(--shadow-sm);
}

.admin-header-content {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 var(--spacing-lg);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.admin-logo {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-weight: 700;
    font-size: var(--font-size-lg);
    color: var(--text-primary);
}

.admin-logo img {
    width: 32px;
    height: 32px;
}

.admin-nav {
    display: flex;
    gap: var(--spacing-md);
}

.admin-nav .nav-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-sm) var(--spacing-md);
    color: var(--text-secondary);
    text-decoration: none;
    border-radius: var(--radius-md);
    font-weight: 500;
    font-size: var(--font-size-sm);
    transition: var(--transition-fast);
}

.admin-nav .nav-item:hover,
.admin-nav .nav-item.active {
    background: var(--primary-color);
    color: var(--text-inverse);
}

.admin-user {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
}

/* Admin Main */
.admin-main {
    padding: var(--spacing-xl) 0;
}

.admin-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 var(--spacing-lg);
}

.admin-page-header {
    margin-bottom: var(--spacing-2xl);
}

.admin-page-header h1 {
    font-size: var(--font-size-3xl);
    font-weight: 800;
    margin-bottom: var(--spacing-sm);
    color: var(--text-primary);
}

.admin-page-header p {
    color: var(--text-secondary);
    font-size: var(--font-size-lg);
}

/* Admin Content Grid */
.admin-content-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-lg);
}

.admin-card {
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-xl);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
}

.admin-card.full-width {
    grid-column: 1 / -1;
}

.card-header {
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: var(--bg-tertiary);
}

.card-header h3 {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--text-primary);
}

.card-content {
    padding: var(--spacing-lg);
}

/* Transactions List */
.transactions-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.transaction-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-md);
    background: var(--bg-tertiary);
    border-radius: var(--radius-lg);
    border: 1px solid var(--border-color);
}

.transaction-info {
    flex: 1;
}

.transaction-user {
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
}

.transaction-details {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    margin-bottom: var(--spacing-xs);
}

.transaction-date {
    font-size: var(--font-size-xs);
    color: var(--text-muted);
}

.transaction-actions {
    display: flex;
    gap: var(--spacing-xs);
}

/* Users List */
.users-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.user-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
    background: var(--bg-tertiary);
    border-radius: var(--radius-lg);
    border: 1px solid var(--border-color);
}

.user-avatar {
    width: 40px;
    height: 40px;
    background: var(--primary-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-inverse);
    font-weight: 600;
    font-size: var(--font-size-sm);
}

.user-avatar.small {
    width: 32px;
    height: 32px;
    font-size: var(--font-size-xs);
}

.user-info {
    flex: 1;
}

.user-name {
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
}

.user-email {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    margin-bottom: var(--spacing-xs);
}

.user-date {
    font-size: var(--font-size-xs);
    color: var(--text-muted);
}

.user-status .status {
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-full);
    font-size: var(--font-size-xs);
    font-weight: 600;
    text-transform: uppercase;
}

.user-cell {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.transaction-type {
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-full);
    font-size: var(--font-size-xs);
    font-weight: 600;
    text-transform: uppercase;
}

.transaction-type.deposit {
    background: rgba(16, 185, 129, 0.1);
    color: var(--success-color);
}

.transaction-type.withdrawal {
    background: rgba(239, 68, 68, 0.1);
    color: var(--error-color);
}

.transaction-type.profit {
    background: rgba(99, 102, 241, 0.1);
    color: var(--primary-color);
}

.amount.positive {
    color: var(--success-color);
    font-weight: 600;
}

.amount.negative {
    color: var(--error-color);
    font-weight: 600;
}

.action-buttons {
    display: flex;
    gap: var(--spacing-xs);
}

/* Button Sizes */
.btn.btn-xs {
    padding: var(--spacing-xs);
    font-size: var(--font-size-xs);
    min-width: auto;
}

.btn.btn-sm {
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: var(--font-size-sm);
}

/* Empty State */
.empty-state {
    text-align: center;
    padding: var(--spacing-2xl);
    color: var(--text-muted);
}

.empty-state i {
    font-size: var(--font-size-3xl);
    margin-bottom: var(--spacing-md);
    color: var(--success-color);
}
