/* Referrals Page Styles */

.referrals-page {
    background: linear-gradient(135deg, var(--bg-primary), var(--bg-secondary));
    min-height: 100vh;
}

.referrals-section {
    padding: var(--spacing-2xl) 0;
}

.referrals-content {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-2xl);
}

.referral-link-section {
    margin-bottom: var(--spacing-xl);
}

.link-card,
.structure-card,
.list-card,
.commissions-card,
.how-card {
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-lg);
    overflow: hidden;
}

.card-header {
    padding: var(--spacing-xl);
    border-bottom: 1px solid var(--border-color);
    text-align: center;
}

.card-header h3 {
    font-size: var(--font-size-xl);
    font-weight: 700;
    margin-bottom: var(--spacing-sm);
    color: var(--text-primary);
}

.card-header p {
    color: var(--text-secondary);
    font-size: var(--font-size-base);
}

.link-container {
    padding: var(--spacing-xl);
}

.link-input-group {
    display: flex;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-lg);
}

.link-input {
    flex: 1;
    padding: var(--spacing-md);
    background: var(--bg-tertiary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    color: var(--text-primary);
    font-size: var(--font-size-sm);
    font-family: monospace;
}

.copy-btn {
    padding: var(--spacing-md) var(--spacing-lg);
    background: var(--primary-color);
    color: var(--text-inverse);
    border: none;
    border-radius: var(--radius-lg);
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition-fast);
    white-space: nowrap;
}

.copy-btn:hover {
    background: var(--primary-light);
}

.copy-btn.copied {
    background: var(--success-color);
}

.referral-code {
    text-align: center;
    padding: var(--spacing-lg);
    background: var(--bg-tertiary);
    border-radius: var(--radius-lg);
    margin-bottom: var(--spacing-lg);
}

.code-label {
    display: block;
    font-size: var(--font-size-sm);
    color: var(--text-muted);
    margin-bottom: var(--spacing-xs);
}

.code-value {
    font-size: var(--font-size-xl);
    font-weight: 700;
    color: var(--primary-color);
    font-family: monospace;
}

.share-buttons {
    text-align: center;
}

.share-buttons h4 {
    font-size: var(--font-size-base);
    font-weight: 600;
    margin-bottom: var(--spacing-md);
    color: var(--text-primary);
}

.social-buttons {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: var(--spacing-sm);
}

.social-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--radius-lg);
    text-decoration: none;
    font-size: var(--font-size-sm);
    font-weight: 600;
    transition: var(--transition-fast);
}

.social-btn.telegram {
    background: #0088cc;
    color: white;
}

.social-btn.whatsapp {
    background: #25d366;
    color: white;
}

.social-btn.twitter {
    background: #1da1f2;
    color: white;
}

.social-btn.facebook {
    background: #1877f2;
    color: white;
}

.social-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-lg);
}

.stat-card {
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    padding: var(--spacing-xl);
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
    transition: var(--transition-base);
}

.stat-card:hover {
    transform: translateY(-4px);
    border-color: var(--primary-color);
    box-shadow: var(--shadow-glow);
}

.stat-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-inverse);
    font-size: var(--font-size-xl);
}

.stat-info h3 {
    font-size: var(--font-size-2xl);
    font-weight: 700;
    margin-bottom: var(--spacing-xs);
    color: var(--text-primary);
}

.stat-info p {
    font-size: var(--font-size-sm);
    color: var(--text-muted);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.levels-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-lg);
    padding: var(--spacing-xl);
}

.level-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
    padding: var(--spacing-lg);
    background: var(--bg-tertiary);
    border-radius: var(--radius-lg);
    border: 1px solid var(--border-color);
    transition: var(--transition-base);
}

.level-item:hover {
    border-color: var(--primary-color);
    transform: translateY(-2px);
}

.level-number {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-inverse);
    font-size: var(--font-size-lg);
    font-weight: 700;
}

.level-info h4 {
    font-size: var(--font-size-base);
    font-weight: 600;
    margin-bottom: var(--spacing-xs);
    color: var(--text-primary);
}

.level-rate {
    font-size: var(--font-size-lg);
    font-weight: 700;
    color: var(--success-color);
    margin-bottom: var(--spacing-xs);
}

.level-description {
    font-size: var(--font-size-sm);
    color: var(--text-muted);
    line-height: 1.4;
}

.referrals-table {
    display: flex;
    flex-direction: column;
}

.table-header {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1fr;
    gap: var(--spacing-md);
    padding: var(--spacing-lg) var(--spacing-xl);
    background: var(--bg-tertiary);
    border-bottom: 1px solid var(--border-color);
}

.header-cell {
    font-size: var(--font-size-xs);
    font-weight: 600;
    color: var(--text-muted);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.table-body {
    display: flex;
    flex-direction: column;
}

.table-row {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1fr;
    gap: var(--spacing-md);
    padding: var(--spacing-lg) var(--spacing-xl);
    border-bottom: 1px solid var(--border-color);
    transition: var(--transition-fast);
}

.table-row:hover {
    background: var(--bg-tertiary);
}

.table-row:last-child {
    border-bottom: none;
}

.table-cell {
    display: flex;
    align-items: center;
}

.user-info {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.user-avatar {
    width: 40px;
    height: 40px;
    background: var(--primary-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-inverse);
    font-weight: 600;
    font-size: var(--font-size-sm);
}

.user-details {
    display: flex;
    flex-direction: column;
}

.user-name {
    font-size: var(--font-size-sm);
    font-weight: 600;
    color: var(--text-primary);
}

.user-email {
    font-size: var(--font-size-xs);
    color: var(--text-muted);
}

.investment-count,
.invested-amount {
    font-size: var(--font-size-sm);
    font-weight: 600;
    color: var(--text-primary);
}

.commissions-list {
    padding: var(--spacing-xl);
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.commission-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-md);
    background: var(--bg-tertiary);
    border-radius: var(--radius-lg);
    border: 1px solid var(--border-color);
    transition: var(--transition-fast);
}

.commission-item:hover {
    border-color: var(--primary-color);
}

.commission-user {
    font-size: var(--font-size-sm);
    font-weight: 600;
    color: var(--text-primary);
}

.commission-date {
    font-size: var(--font-size-xs);
    color: var(--text-muted);
}

.commission-amount {
    font-size: var(--font-size-base);
    font-weight: 700;
    color: var(--success-color);
}

.steps-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-lg);
    padding: var(--spacing-xl);
}

.step-item {
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-lg);
    padding: var(--spacing-lg);
    background: var(--bg-tertiary);
    border-radius: var(--radius-lg);
    border: 1px solid var(--border-color);
}

.step-number {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-inverse);
    font-size: var(--font-size-base);
    font-weight: 700;
    flex-shrink: 0;
}

.step-content h4 {
    font-size: var(--font-size-base);
    font-weight: 600;
    margin-bottom: var(--spacing-xs);
    color: var(--text-primary);
}

.step-content p {
    font-size: var(--font-size-sm);
    color: var(--text-muted);
    line-height: 1.4;
}

.header-action {
    color: var(--primary-color);
    text-decoration: none;
    font-size: var(--font-size-sm);
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    transition: var(--transition-fast);
}

.header-action:hover {
    color: var(--primary-light);
}

.header-info {
    font-size: var(--font-size-sm);
    color: var(--text-muted);
}

@media (max-width: 768px) {
    .link-input-group {
        flex-direction: column;
    }
    
    .social-buttons {
        grid-template-columns: 1fr 1fr;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .levels-grid {
        grid-template-columns: 1fr;
    }
    
    .table-header,
    .table-row {
        grid-template-columns: 1fr 1fr;
    }
    
    .date-cell,
    .investments-cell {
        display: none;
    }
    
    .steps-grid {
        grid-template-columns: 1fr;
    }
    
    .commission-item {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-sm);
    }
}
