<?php
session_start();
require_once 'config/database.php';
require_once 'includes/functions.php';
require_once 'includes/language.php';

// Определение языка
$lang = detectLanguage();
loadLanguage($lang);

// Проверка авторизации
if (!isset($_SESSION['user_id'])) {
    redirect('login.php');
}

$user = getUserById($_SESSION['user_id']);
if (!$user) {
    session_destroy();
    redirect('login.php');
}

$db = getDB();

// Получение реферального кода пользователя
$referral_code = $user['referral_code'] ?? generateReferralCode();
if (!$user['referral_code']) {
    // Обновляем реферальный код в базе данных
    $stmt = $db->prepare("UPDATE users SET referral_code = ? WHERE id = ?");
    $stmt->execute([$referral_code, $_SESSION['user_id']]);
}

// Получение статистики рефералов
$stmt = $db->prepare("
    SELECT 
        COUNT(*) as total_referrals,
        COUNT(CASE WHEN created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY) THEN 1 END) as monthly_referrals,
        COUNT(CASE WHEN created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY) THEN 1 END) as weekly_referrals
    FROM users 
    WHERE referred_by = ?
");
$stmt->execute([$_SESSION['user_id']]);
$referral_stats = $stmt->fetch();

// Получение комиссий
$stmt = $db->prepare("
    SELECT 
        COALESCE(SUM(commission_amount), 0) as total_earnings,
        COALESCE(SUM(CASE WHEN created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY) THEN commission_amount ELSE 0 END), 0) as monthly_earnings,
        COUNT(*) as total_commissions
    FROM referral_commissions 
    WHERE referrer_id = ?
");
$stmt->execute([$_SESSION['user_id']]);
$commission_stats = $stmt->fetch();

// Получение списка рефералов
$stmt = $db->prepare("
    SELECT 
        u.first_name, u.last_name, u.email, u.created_at,
        COALESCE(SUM(i.amount), 0) as total_invested,
        COUNT(i.id) as total_investments
    FROM users u
    LEFT JOIN investments i ON u.id = i.user_id
    WHERE u.referred_by = ?
    GROUP BY u.id
    ORDER BY u.created_at DESC
    LIMIT 20
");
$stmt->execute([$_SESSION['user_id']]);
$referrals = $stmt->fetchAll();

// Получение последних комиссий
$stmt = $db->prepare("
    SELECT rc.*, u.first_name, u.last_name 
    FROM referral_commissions rc
    JOIN users u ON rc.referred_user_id = u.id
    WHERE rc.referrer_id = ?
    ORDER BY rc.created_at DESC
    LIMIT 10
");
$stmt->execute([$_SESSION['user_id']]);
$recent_commissions = $stmt->fetchAll();

// Реферальная ссылка
$referral_link = "https://" . $_SERVER['HTTP_HOST'] . "/register.php?ref=" . $referral_code;

// Настройки реферальной программы
$referral_settings = [
    'commission_rate' => 10, // 10% комиссия
    'levels' => [
        1 => 10, // 10% с первого уровня
        2 => 5,  // 5% со второго уровня
        3 => 2   // 2% с третьего уровня
    ]
];

$pageTitle = t('referral_program') . ' - Astragenix';
$pageDescription = t('referrals_description');
$currentPage = 'referrals';
$bodyClass = 'referrals-page';
$additionalCSS = ['assets/css/referrals.css'];
include 'includes/header.php';
?>

<!-- Referrals Section -->
<section class="referrals-section">
    <div class="container">
        <!-- Page Header -->
        <div class="page-header">
            <div class="page-header-content">
                <h1 class="page-title"><?php echo t('referral_program'); ?></h1>
                <p class="page-subtitle"><?php echo t('referrals_description'); ?></p>
                <div class="breadcrumb">
                    <a href="dashboard.php"><?php echo t('dashboard'); ?></a>
                    <span class="breadcrumb-separator">/</span>
                    <span class="breadcrumb-current"><?php echo t('referrals'); ?></span>
                </div>
            </div>
        </div>

        <div class="referrals-content">
            <!-- Referral Link Section -->
            <div class="referral-link-section">
                <div class="link-card">
                    <div class="card-header">
                        <h3><?php echo t('your_referral_link'); ?></h3>
                        <p><?php echo t('share_link_earn_commission'); ?></p>
                    </div>
                    
                    <div class="link-container">
                        <div class="link-input-group">
                            <input type="text" 
                                   id="referral-link" 
                                   class="link-input" 
                                   value="<?php echo $referral_link; ?>" 
                                   readonly>
                            <button type="button" class="copy-btn" onclick="copyReferralLink()">
                                <i class="fas fa-copy"></i>
                                <?php echo t('copy'); ?>
                            </button>
                        </div>
                        
                        <div class="referral-code">
                            <span class="code-label"><?php echo t('your_referral_code'); ?>:</span>
                            <span class="code-value"><?php echo $referral_code; ?></span>
                        </div>
                    </div>
                    
                    <div class="share-buttons">
                        <h4><?php echo t('share_on_social_media'); ?></h4>
                        <div class="social-buttons">
                            <a href="https://t.me/share/url?url=<?php echo urlencode($referral_link); ?>&text=<?php echo urlencode(t('join_astragenix_message')); ?>" 
                               target="_blank" class="social-btn telegram">
                                <i class="fab fa-telegram"></i>
                                Telegram
                            </a>
                            <a href="https://wa.me/?text=<?php echo urlencode(t('join_astragenix_message') . ' ' . $referral_link); ?>" 
                               target="_blank" class="social-btn whatsapp">
                                <i class="fab fa-whatsapp"></i>
                                WhatsApp
                            </a>
                            <a href="https://twitter.com/intent/tweet?text=<?php echo urlencode(t('join_astragenix_message')); ?>&url=<?php echo urlencode($referral_link); ?>" 
                               target="_blank" class="social-btn twitter">
                                <i class="fab fa-twitter"></i>
                                Twitter
                            </a>
                            <a href="https://www.facebook.com/sharer/sharer.php?u=<?php echo urlencode($referral_link); ?>" 
                               target="_blank" class="social-btn facebook">
                                <i class="fab fa-facebook"></i>
                                Facebook
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Statistics -->
            <div class="stats-section">
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="stat-info">
                            <h3><?php echo $referral_stats['total_referrals']; ?></h3>
                            <p><?php echo t('total_referrals'); ?></p>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-dollar-sign"></i>
                        </div>
                        <div class="stat-info">
                            <h3><?php echo formatCurrency($commission_stats['total_earnings']); ?></h3>
                            <p><?php echo t('total_earnings'); ?></p>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-calendar-month"></i>
                        </div>
                        <div class="stat-info">
                            <h3><?php echo formatCurrency($commission_stats['monthly_earnings']); ?></h3>
                            <p><?php echo t('monthly_earnings'); ?></p>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-user-plus"></i>
                        </div>
                        <div class="stat-info">
                            <h3><?php echo $referral_stats['monthly_referrals']; ?></h3>
                            <p><?php echo t('monthly_referrals'); ?></p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Commission Structure -->
            <div class="commission-structure">
                <div class="structure-card">
                    <div class="card-header">
                        <h3><?php echo t('commission_structure'); ?></h3>
                        <p><?php echo t('earn_from_multiple_levels'); ?></p>
                    </div>
                    
                    <div class="levels-grid">
                        <?php foreach ($referral_settings['levels'] as $level => $rate): ?>
                            <div class="level-item">
                                <div class="level-number"><?php echo $level; ?></div>
                                <div class="level-info">
                                    <h4><?php echo t('level'); ?> <?php echo $level; ?></h4>
                                    <p class="level-rate"><?php echo $rate; ?>% <?php echo t('commission'); ?></p>
                                    <p class="level-description">
                                        <?php echo t('level_' . $level . '_description'); ?>
                                    </p>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>

            <!-- Referrals List -->
            <?php if (!empty($referrals)): ?>
                <div class="referrals-list-section">
                    <div class="list-card">
                        <div class="card-header">
                            <h3><?php echo t('your_referrals'); ?></h3>
                            <div class="header-info">
                                <?php echo count($referrals); ?> <?php echo t('active_referrals'); ?>
                            </div>
                        </div>
                        
                        <div class="referrals-table">
                            <div class="table-header">
                                <div class="header-cell"><?php echo t('user'); ?></div>
                                <div class="header-cell"><?php echo t('joined_date'); ?></div>
                                <div class="header-cell"><?php echo t('investments'); ?></div>
                                <div class="header-cell"><?php echo t('total_invested'); ?></div>
                            </div>
                            
                            <div class="table-body">
                                <?php foreach ($referrals as $referral): ?>
                                    <div class="table-row">
                                        <div class="table-cell user-cell">
                                            <div class="user-info">
                                                <div class="user-avatar">
                                                    <?php echo strtoupper(substr($referral['first_name'], 0, 1)); ?>
                                                </div>
                                                <div class="user-details">
                                                    <span class="user-name">
                                                        <?php echo h($referral['first_name'] . ' ' . $referral['last_name']); ?>
                                                    </span>
                                                    <span class="user-email"><?php echo h($referral['email']); ?></span>
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <div class="table-cell date-cell">
                                            <?php echo date('M j, Y', strtotime($referral['created_at'])); ?>
                                        </div>
                                        
                                        <div class="table-cell investments-cell">
                                            <span class="investment-count"><?php echo $referral['total_investments']; ?></span>
                                        </div>
                                        
                                        <div class="table-cell amount-cell">
                                            <span class="invested-amount">
                                                <?php echo formatCurrency($referral['total_invested']); ?>
                                            </span>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Recent Commissions -->
            <?php if (!empty($recent_commissions)): ?>
                <div class="commissions-section">
                    <div class="commissions-card">
                        <div class="card-header">
                            <h3><?php echo t('recent_commissions'); ?></h3>
                            <a href="history.php?type=referral" class="header-action">
                                <?php echo t('view_all'); ?>
                                <i class="fas fa-arrow-right"></i>
                            </a>
                        </div>
                        
                        <div class="commissions-list">
                            <?php foreach ($recent_commissions as $commission): ?>
                                <div class="commission-item">
                                    <div class="commission-info">
                                        <div class="commission-user">
                                            <?php echo h($commission['first_name'] . ' ' . $commission['last_name']); ?>
                                        </div>
                                        <div class="commission-date">
                                            <?php echo date('M j, Y H:i', strtotime($commission['created_at'])); ?>
                                        </div>
                                    </div>
                                    <div class="commission-amount">
                                        +<?php echo formatCurrency($commission['commission_amount']); ?>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
            <?php endif; ?>

            <!-- How It Works -->
            <div class="how-it-works">
                <div class="how-card">
                    <div class="card-header">
                        <h3><?php echo t('how_it_works'); ?></h3>
                        <p><?php echo t('simple_steps_to_earn'); ?></p>
                    </div>
                    
                    <div class="steps-grid">
                        <div class="step-item">
                            <div class="step-number">1</div>
                            <div class="step-content">
                                <h4><?php echo t('share_your_link'); ?></h4>
                                <p><?php echo t('share_link_description'); ?></p>
                            </div>
                        </div>
                        
                        <div class="step-item">
                            <div class="step-number">2</div>
                            <div class="step-content">
                                <h4><?php echo t('friend_registers'); ?></h4>
                                <p><?php echo t('friend_registers_description'); ?></p>
                            </div>
                        </div>
                        
                        <div class="step-item">
                            <div class="step-number">3</div>
                            <div class="step-content">
                                <h4><?php echo t('earn_commission'); ?></h4>
                                <p><?php echo t('earn_commission_description'); ?></p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<script>
function copyReferralLink() {
    const linkInput = document.getElementById('referral-link');
    linkInput.select();
    linkInput.setSelectionRange(0, 99999);
    
    try {
        document.execCommand('copy');
        
        const copyBtn = document.querySelector('.copy-btn');
        const originalText = copyBtn.innerHTML;
        copyBtn.innerHTML = '<i class="fas fa-check"></i> <?php echo t("copied"); ?>';
        copyBtn.classList.add('copied');
        
        setTimeout(() => {
            copyBtn.innerHTML = originalText;
            copyBtn.classList.remove('copied');
        }, 2000);
    } catch (err) {
        console.error('Failed to copy: ', err);
    }
}
</script>

<?php include 'includes/footer.php'; ?>
