<?php
session_start();
require_once 'config/database.php';
require_once 'includes/functions.php';
require_once 'includes/language.php';

// Определение языка
$lang = detectLanguage();
loadLanguage($lang);

// Проверка авторизации
$isLoggedIn = isset($_SESSION['user_id']);
$user = null;
if ($isLoggedIn) {
    $user = getUserById($_SESSION['user_id']);
}

// Настройки страницы
$pageTitle = 'Astragenix - ' . t('about_us');
$pageDescription = t('about_description_detailed');
$currentPage = 'about';
$bodyClass = 'about-page';

// Подключение заголовка
include 'includes/header.php';
?>

    <!-- Page Header -->
    <section class="page-header">
        <div class="container">
            <div class="page-header-content">
                <h1 class="page-title scroll-reveal"><?php echo t('about_astragenix'); ?></h1>
                <p class="page-subtitle scroll-reveal"><?php echo t('about_subtitle'); ?></p>
                <nav class="breadcrumb scroll-reveal">
                    <a href="index.php"><?php echo t('home'); ?></a>
                    <span class="breadcrumb-separator">/</span>
                    <span class="breadcrumb-current"><?php echo t('about'); ?></span>
                </nav>
            </div>
        </div>
    </section>

    <!-- Company Story Section -->
    <section class="story-section">
        <div class="container">
            <div class="story-content">
                <div class="story-text scroll-reveal-left">
                    <h2 class="section-title"><?php echo t('our_story'); ?></h2>
                    <p class="story-description"><?php echo t('company_story_detailed'); ?></p>
                    
                    <div class="story-timeline">
                        <div class="timeline-item">
                            <div class="timeline-year">2025</div>
                            <div class="timeline-content">
                                <h4><?php echo t('company_founded'); ?></h4>
                                <p><?php echo t('founding_story'); ?></p>
                            </div>
                        </div>
                        
                        <div class="timeline-item">
                            <div class="timeline-year">2025</div>
                            <div class="timeline-content">
                                <h4><?php echo t('platform_launch'); ?></h4>
                                <p><?php echo t('launch_story'); ?></p>
                            </div>
                        </div>
                        
                        <div class="timeline-item">
                            <div class="timeline-year">2025</div>
                            <div class="timeline-content">
                                <h4><?php echo t('global_expansion'); ?></h4>
                                <p><?php echo t('expansion_story'); ?></p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="story-visual scroll-reveal-right">
                    <div class="story-image">
                        <img src="assets/images/company-story.jpg" alt="<?php echo t('our_story'); ?>" class="img-responsive">
                    </div>
                    
                    <div class="story-stats">
                        <div class="stat-card">
                            <div class="stat-number" data-target="50000">0</div>
                            <div class="stat-label"><?php echo t('happy_investors'); ?></div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number" data-target="25">0</div>
                            <div class="stat-label"><?php echo t('million_invested'); ?></div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number" data-target="99.8">0</div>
                            <div class="stat-label"><?php echo t('uptime_percentage'); ?></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Mission & Vision Section -->
    <section class="mission-section">
        <div class="container">
            <div class="mission-grid">
                <div class="mission-card scroll-reveal stagger-item">
                    <div class="mission-icon">
                        <i class="fas fa-bullseye"></i>
                    </div>
                    <h3 class="mission-title"><?php echo t('our_mission'); ?></h3>
                    <p class="mission-description"><?php echo t('mission_description'); ?></p>
                </div>
                
                <div class="mission-card scroll-reveal stagger-item">
                    <div class="mission-icon">
                        <i class="fas fa-eye"></i>
                    </div>
                    <h3 class="mission-title"><?php echo t('our_vision'); ?></h3>
                    <p class="mission-description"><?php echo t('vision_description'); ?></p>
                </div>
                
                <div class="mission-card scroll-reveal stagger-item">
                    <div class="mission-icon">
                        <i class="fas fa-heart"></i>
                    </div>
                    <h3 class="mission-title"><?php echo t('our_values'); ?></h3>
                    <p class="mission-description"><?php echo t('values_description'); ?></p>
                </div>
            </div>
        </div>
    </section>

    <!-- Team Section -->
    <section class="team-section">
        <div class="container">
            <div class="section-header text-center">
                <h2 class="section-title scroll-reveal"><?php echo t('our_team'); ?></h2>
                <p class="section-subtitle scroll-reveal"><?php echo t('team_description'); ?></p>
            </div>
            
            <div class="team-grid">
                <div class="team-member scroll-reveal stagger-item">
                    <div class="member-photo">
                        <img src="assets/images/team/ceo.jpg" alt="CEO" class="img-responsive">
                        <div class="member-overlay">
                            <div class="member-social">
                                <a href="#" class="social-link"><i class="fab fa-linkedin"></i></a>
                                <a href="#" class="social-link"><i class="fab fa-twitter"></i></a>
                            </div>
                        </div>
                    </div>
                    <div class="member-info">
                        <h4 class="member-name">Alexander Chen</h4>
                        <p class="member-position"><?php echo t('ceo_founder'); ?></p>
                        <p class="member-bio"><?php echo t('ceo_bio'); ?></p>
                    </div>
                </div>
                
                <div class="team-member scroll-reveal stagger-item">
                    <div class="member-photo">
                        <img src="assets/images/team/cto.jpg" alt="CTO" class="img-responsive">
                        <div class="member-overlay">
                            <div class="member-social">
                                <a href="#" class="social-link"><i class="fab fa-linkedin"></i></a>
                                <a href="#" class="social-link"><i class="fab fa-github"></i></a>
                            </div>
                        </div>
                    </div>
                    <div class="member-info">
                        <h4 class="member-name">Sarah Johnson</h4>
                        <p class="member-position"><?php echo t('cto'); ?></p>
                        <p class="member-bio"><?php echo t('cto_bio'); ?></p>
                    </div>
                </div>
                
                <div class="team-member scroll-reveal stagger-item">
                    <div class="member-photo">
                        <img src="assets/images/team/cfo.jpg" alt="CFO" class="img-responsive">
                        <div class="member-overlay">
                            <div class="member-social">
                                <a href="#" class="social-link"><i class="fab fa-linkedin"></i></a>
                                <a href="#" class="social-link"><i class="fab fa-twitter"></i></a>
                            </div>
                        </div>
                    </div>
                    <div class="member-info">
                        <h4 class="member-name">Michael Rodriguez</h4>
                        <p class="member-position"><?php echo t('cfo'); ?></p>
                        <p class="member-bio"><?php echo t('cfo_bio'); ?></p>
                    </div>
                </div>
                
                <div class="team-member scroll-reveal stagger-item">
                    <div class="member-photo">
                        <img src="assets/images/team/head-trading.jpg" alt="Head of Trading" class="img-responsive">
                        <div class="member-overlay">
                            <div class="member-social">
                                <a href="#" class="social-link"><i class="fab fa-linkedin"></i></a>
                                <a href="#" class="social-link"><i class="fab fa-twitter"></i></a>
                            </div>
                        </div>
                    </div>
                    <div class="member-info">
                        <h4 class="member-name">Emma Thompson</h4>
                        <p class="member-position"><?php echo t('head_of_trading'); ?></p>
                        <p class="member-bio"><?php echo t('head_trading_bio'); ?></p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Technology Section -->
    <section class="technology-section">
        <div class="container">
            <div class="technology-content">
                <div class="technology-text scroll-reveal-left">
                    <h2 class="section-title"><?php echo t('our_technology'); ?></h2>
                    <p class="technology-description"><?php echo t('technology_description'); ?></p>
                    
                    <div class="technology-features">
                        <div class="tech-feature">
                            <div class="tech-icon">
                                <i class="fas fa-robot"></i>
                            </div>
                            <div class="tech-content">
                                <h4><?php echo t('ai_algorithms'); ?></h4>
                                <p><?php echo t('ai_description'); ?></p>
                            </div>
                        </div>
                        
                        <div class="tech-feature">
                            <div class="tech-icon">
                                <i class="fas fa-shield-alt"></i>
                            </div>
                            <div class="tech-content">
                                <h4><?php echo t('security_protocols'); ?></h4>
                                <p><?php echo t('security_tech_description'); ?></p>
                            </div>
                        </div>
                        
                        <div class="tech-feature">
                            <div class="tech-icon">
                                <i class="fas fa-chart-line"></i>
                            </div>
                            <div class="tech-content">
                                <h4><?php echo t('real_time_analytics'); ?></h4>
                                <p><?php echo t('analytics_description'); ?></p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="technology-visual scroll-reveal-right">
                    <div class="tech-dashboard">
                        <div class="dashboard-screen">
                            <div class="screen-header">
                                <div class="screen-title">AI Trading Engine</div>
                                <div class="screen-status active">Active</div>
                            </div>
                            <div class="screen-content">
                                <canvas id="techChart" width="300" height="150"></canvas>
                            </div>
                            <div class="screen-stats">
                                <div class="screen-stat">
                                    <span class="stat-label">Accuracy</span>
                                    <span class="stat-value">98.7%</span>
                                </div>
                                <div class="screen-stat">
                                    <span class="stat-label">Trades/Day</span>
                                    <span class="stat-value">1,247</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Awards & Recognition Section -->
    <section class="awards-section">
        <div class="container">
            <div class="section-header text-center">
                <h2 class="section-title scroll-reveal"><?php echo t('awards_recognition'); ?></h2>
                <p class="section-subtitle scroll-reveal"><?php echo t('awards_description'); ?></p>
            </div>
            
            <div class="awards-grid">
                <div class="award-item scroll-reveal stagger-item">
                    <div class="award-icon">
                        <i class="fas fa-trophy"></i>
                    </div>
                    <h4 class="award-title"><?php echo t('best_crypto_platform'); ?></h4>
                    <p class="award-year">2025</p>
                </div>
                
                <div class="award-item scroll-reveal stagger-item">
                    <div class="award-icon">
                        <i class="fas fa-medal"></i>
                    </div>
                    <h4 class="award-title"><?php echo t('innovation_award'); ?></h4>
                    <p class="award-year">2025</p>
                </div>
                
                <div class="award-item scroll-reveal stagger-item">
                    <div class="award-icon">
                        <i class="fas fa-star"></i>
                    </div>
                    <h4 class="award-title"><?php echo t('customer_choice'); ?></h4>
                    <p class="award-year">2025</p>
                </div>
                
                <div class="award-item scroll-reveal stagger-item">
                    <div class="award-icon">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <h4 class="award-title"><?php echo t('security_excellence'); ?></h4>
                    <p class="award-year">2025</p>
                </div>
            </div>
        </div>
    </section>

    <!-- CTA Section -->
    <section class="cta-section">
        <div class="container">
            <div class="cta-content text-center">
                <h2 class="cta-title scroll-reveal"><?php echo t('join_our_journey'); ?></h2>
                <p class="cta-subtitle scroll-reveal"><?php echo t('journey_description'); ?></p>
                
                <div class="cta-buttons scroll-reveal">
                    <?php if (!$isLoggedIn): ?>
                        <a href="register.php" class="btn btn-primary btn-xl">
                            <i class="fas fa-rocket"></i>
                            <span><?php echo t('get_started_now'); ?></span>
                        </a>
                        <a href="contact.php" class="btn btn-outline btn-xl">
                            <i class="fas fa-envelope"></i>
                            <span><?php echo t('contact_us'); ?></span>
                        </a>
                    <?php else: ?>
                        <a href="dashboard.php" class="btn btn-primary btn-xl">
                            <i class="fas fa-tachometer-alt"></i>
                            <span><?php echo t('go_to_dashboard'); ?></span>
                        </a>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </section>

<?php
// Подключение футера
include 'includes/footer.php';
?>
