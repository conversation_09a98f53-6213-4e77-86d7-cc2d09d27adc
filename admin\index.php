<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';
require_once '../includes/language.php';

// Определение языка
$lang = detectLanguage();
loadLanguage($lang);

// Проверка авторизации и прав администратора
if (!isset($_SESSION['user_id'])) {
    redirect('../login.php');
}

$user = getUserById($_SESSION['user_id']);
if (!$user || $user['role'] !== 'admin') {
    redirect('../dashboard.php');
}

$db = getDB();

// Получение статистики
$stats = [];

// Общая статистика пользователей
$stmt = $db->query("
    SELECT 
        COUNT(*) as total_users,
        COUNT(CASE WHEN created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY) THEN 1 END) as new_users_month,
        COUNT(CASE WHEN last_activity >= DATE_SUB(NOW(), INTERVAL 24 HOUR) THEN 1 END) as active_users_today
    FROM users
");
$stats['users'] = $stmt->fetch();

// Статистика инвестиций
$stmt = $db->query("
    SELECT 
        COUNT(*) as total_investments,
        COUNT(CASE WHEN status = 'active' THEN 1 END) as active_investments,
        COALESCE(SUM(amount), 0) as total_invested,
        COALESCE(SUM(CASE WHEN created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY) THEN amount ELSE 0 END), 0) as invested_this_month
    FROM investments
");
$stats['investments'] = $stmt->fetch();

// Статистика транзакций
$stmt = $db->query("
    SELECT 
        COUNT(*) as total_transactions,
        COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_transactions,
        COALESCE(SUM(CASE WHEN type = 'deposit' AND status = 'completed' THEN amount ELSE 0 END), 0) as total_deposits,
        COALESCE(SUM(CASE WHEN type = 'withdrawal' AND status = 'completed' THEN amount ELSE 0 END), 0) as total_withdrawals
    FROM transactions
");
$stats['transactions'] = $stmt->fetch();

// Статистика рефералов
$stmt = $db->query("
    SELECT 
        COUNT(*) as total_commissions,
        COALESCE(SUM(commission_amount), 0) as total_commission_paid
    FROM referral_commissions
");
$stats['referrals'] = $stmt->fetch();

// Последние пользователи
$stmt = $db->query("
    SELECT id, first_name, last_name, email, created_at, status
    FROM users 
    ORDER BY created_at DESC 
    LIMIT 10
");
$recent_users = $stmt->fetchAll();

// Последние транзакции
$stmt = $db->query("
    SELECT t.*, u.first_name, u.last_name, u.email
    FROM transactions t
    JOIN users u ON t.user_id = u.id
    ORDER BY t.created_at DESC 
    LIMIT 10
");
$recent_transactions = $stmt->fetchAll();

// Ожидающие транзакции
$stmt = $db->query("
    SELECT t.*, u.first_name, u.last_name, u.email
    FROM transactions t
    JOIN users u ON t.user_id = u.id
    WHERE t.status = 'pending'
    ORDER BY t.created_at ASC
    LIMIT 20
");
$pending_transactions = $stmt->fetchAll();

$pageTitle = 'Admin Dashboard - Astragenix';
$currentPage = 'admin';
$bodyClass = 'admin-page';
$additionalCSS = ['../assets/css/admin.css'];
?>

<!DOCTYPE html>
<html lang="<?php echo $lang; ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?></title>
    <link rel="stylesheet" href="../assets/css/modern.css">
    <?php if (isset($additionalCSS)): ?>
        <?php foreach ($additionalCSS as $css): ?>
            <link rel="stylesheet" href="<?php echo $css; ?>">
        <?php endforeach; ?>
    <?php endif; ?>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body class="<?php echo $bodyClass; ?>">
    <!-- Admin Header -->
    <header class="admin-header">
        <div class="admin-header-content">
            <div class="admin-logo">
                <img src="../assets/images/logo.png" alt="Astragenix">
                <span>Admin Panel</span>
            </div>
            
            <div class="admin-nav">
                <a href="index.php" class="nav-item active">
                    <i class="fas fa-tachometer-alt"></i>
                    Dashboard
                </a>
                <a href="users.php" class="nav-item">
                    <i class="fas fa-users"></i>
                    Users
                </a>
                <a href="investments.php" class="nav-item">
                    <i class="fas fa-chart-line"></i>
                    Investments
                </a>
                <a href="transactions.php" class="nav-item">
                    <i class="fas fa-exchange-alt"></i>
                    Transactions
                </a>
                <a href="settings.php" class="nav-item">
                    <i class="fas fa-cog"></i>
                    Settings
                </a>
            </div>
            
            <div class="admin-user">
                <span>Welcome, <?php echo h($user['first_name']); ?></span>
                <a href="../dashboard.php" class="btn btn-outline btn-sm">
                    <i class="fas fa-external-link-alt"></i>
                    View Site
                </a>
                <a href="../logout.php" class="btn btn-danger btn-sm">
                    <i class="fas fa-sign-out-alt"></i>
                    Logout
                </a>
            </div>
        </div>
    </header>

    <!-- Admin Content -->
    <main class="admin-main">
        <div class="admin-container">
            <!-- Page Header -->
            <div class="admin-page-header">
                <h1>Dashboard Overview</h1>
                <p>Monitor your platform's performance and manage operations</p>
            </div>

            <!-- Stats Grid -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-icon users">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="stat-content">
                        <h3><?php echo number_format($stats['users']['total_users']); ?></h3>
                        <p>Total Users</p>
                        <span class="stat-change positive">
                            +<?php echo $stats['users']['new_users_month']; ?> this month
                        </span>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon investments">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <div class="stat-content">
                        <h3><?php echo formatCurrency($stats['investments']['total_invested']); ?></h3>
                        <p>Total Invested</p>
                        <span class="stat-change positive">
                            <?php echo $stats['investments']['active_investments']; ?> active
                        </span>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon transactions">
                        <i class="fas fa-exchange-alt"></i>
                    </div>
                    <div class="stat-content">
                        <h3><?php echo number_format($stats['transactions']['total_transactions']); ?></h3>
                        <p>Total Transactions</p>
                        <span class="stat-change warning">
                            <?php echo $stats['transactions']['pending_transactions']; ?> pending
                        </span>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon referrals">
                        <i class="fas fa-handshake"></i>
                    </div>
                    <div class="stat-content">
                        <h3><?php echo formatCurrency($stats['referrals']['total_commission_paid']); ?></h3>
                        <p>Referral Commissions</p>
                        <span class="stat-change positive">
                            <?php echo $stats['referrals']['total_commissions']; ?> payments
                        </span>
                    </div>
                </div>
            </div>

            <!-- Content Grid -->
            <div class="admin-content-grid">
                <!-- Pending Transactions -->
                <div class="admin-card">
                    <div class="card-header">
                        <h3>Pending Transactions</h3>
                        <a href="transactions.php?status=pending" class="btn btn-primary btn-sm">
                            View All
                        </a>
                    </div>
                    <div class="card-content">
                        <?php if (!empty($pending_transactions)): ?>
                            <div class="transactions-list">
                                <?php foreach ($pending_transactions as $transaction): ?>
                                    <div class="transaction-item">
                                        <div class="transaction-info">
                                            <div class="transaction-user">
                                                <?php echo h($transaction['first_name'] . ' ' . $transaction['last_name']); ?>
                                            </div>
                                            <div class="transaction-details">
                                                <?php echo ucfirst($transaction['type']); ?> - 
                                                <?php echo formatCurrency($transaction['amount']); ?>
                                            </div>
                                            <div class="transaction-date">
                                                <?php echo date('M j, Y H:i', strtotime($transaction['created_at'])); ?>
                                            </div>
                                        </div>
                                        <div class="transaction-actions">
                                            <button class="btn btn-success btn-xs" onclick="approveTransaction(<?php echo $transaction['id']; ?>)">
                                                <i class="fas fa-check"></i>
                                            </button>
                                            <button class="btn btn-danger btn-xs" onclick="rejectTransaction(<?php echo $transaction['id']; ?>)">
                                                <i class="fas fa-times"></i>
                                            </button>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        <?php else: ?>
                            <div class="empty-state">
                                <i class="fas fa-check-circle"></i>
                                <p>No pending transactions</p>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Recent Users -->
                <div class="admin-card">
                    <div class="card-header">
                        <h3>Recent Users</h3>
                        <a href="users.php" class="btn btn-primary btn-sm">
                            View All
                        </a>
                    </div>
                    <div class="card-content">
                        <div class="users-list">
                            <?php foreach ($recent_users as $recent_user): ?>
                                <div class="user-item">
                                    <div class="user-avatar">
                                        <?php echo strtoupper(substr($recent_user['first_name'], 0, 1)); ?>
                                    </div>
                                    <div class="user-info">
                                        <div class="user-name">
                                            <?php echo h($recent_user['first_name'] . ' ' . $recent_user['last_name']); ?>
                                        </div>
                                        <div class="user-email">
                                            <?php echo h($recent_user['email']); ?>
                                        </div>
                                        <div class="user-date">
                                            <?php echo date('M j, Y', strtotime($recent_user['created_at'])); ?>
                                        </div>
                                    </div>
                                    <div class="user-status">
                                        <span class="status <?php echo $recent_user['status']; ?>">
                                            <?php echo ucfirst($recent_user['status']); ?>
                                        </span>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>

                <!-- Recent Transactions -->
                <div class="admin-card full-width">
                    <div class="card-header">
                        <h3>Recent Transactions</h3>
                        <a href="transactions.php" class="btn btn-primary btn-sm">
                            View All
                        </a>
                    </div>
                    <div class="card-content">
                        <div class="table-responsive">
                            <table class="admin-table">
                                <thead>
                                    <tr>
                                        <th>User</th>
                                        <th>Type</th>
                                        <th>Amount</th>
                                        <th>Status</th>
                                        <th>Date</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($recent_transactions as $transaction): ?>
                                        <tr>
                                            <td>
                                                <div class="user-cell">
                                                    <div class="user-avatar small">
                                                        <?php echo strtoupper(substr($transaction['first_name'], 0, 1)); ?>
                                                    </div>
                                                    <div>
                                                        <div class="user-name">
                                                            <?php echo h($transaction['first_name'] . ' ' . $transaction['last_name']); ?>
                                                        </div>
                                                        <div class="user-email">
                                                            <?php echo h($transaction['email']); ?>
                                                        </div>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                <span class="transaction-type <?php echo $transaction['type']; ?>">
                                                    <?php echo ucfirst($transaction['type']); ?>
                                                </span>
                                            </td>
                                            <td>
                                                <span class="amount <?php echo $transaction['amount'] >= 0 ? 'positive' : 'negative'; ?>">
                                                    <?php echo formatCurrency($transaction['amount']); ?>
                                                </span>
                                            </td>
                                            <td>
                                                <span class="status <?php echo $transaction['status']; ?>">
                                                    <?php echo ucfirst($transaction['status']); ?>
                                                </span>
                                            </td>
                                            <td>
                                                <?php echo date('M j, Y H:i', strtotime($transaction['created_at'])); ?>
                                            </td>
                                            <td>
                                                <div class="action-buttons">
                                                    <button class="btn btn-outline btn-xs" onclick="viewTransaction(<?php echo $transaction['id']; ?>)">
                                                        <i class="fas fa-eye"></i>
                                                    </button>
                                                    <?php if ($transaction['status'] === 'pending'): ?>
                                                        <button class="btn btn-success btn-xs" onclick="approveTransaction(<?php echo $transaction['id']; ?>)">
                                                            <i class="fas fa-check"></i>
                                                        </button>
                                                        <button class="btn btn-danger btn-xs" onclick="rejectTransaction(<?php echo $transaction['id']; ?>)">
                                                            <i class="fas fa-times"></i>
                                                        </button>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Scripts -->
    <script src="../assets/js/modern.js"></script>
    <script src="../assets/js/admin.js"></script>
</body>
</html>
