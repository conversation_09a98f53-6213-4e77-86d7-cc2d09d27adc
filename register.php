<?php
session_start();
require_once 'config/database.php';
require_once 'includes/functions.php';
require_once 'includes/language.php';

// Определение языка
$lang = detectLanguage();
loadLanguage($lang);

// Проверка, если пользователь уже авторизован
if (isset($_SESSION['user_id'])) {
    redirect('dashboard.php');
}

$errors = [];
$success = '';

// Обработка формы регистрации
if ($_POST) {
    $email = trim($_POST['email'] ?? '');
    $password = $_POST['password'] ?? '';
    $confirmPassword = $_POST['confirm_password'] ?? '';
    $firstName = trim($_POST['first_name'] ?? '');
    $lastName = trim($_POST['last_name'] ?? '');
    $phone = trim($_POST['phone'] ?? '');
    $referralCode = trim($_POST['referral_code'] ?? '');
    $agreeTerms = isset($_POST['agree_terms']);
    
    // Валидация
    if (empty($email)) {
        $errors[] = t('email') . ' ' . t('required');
    } elseif (!isValidEmail($email)) {
        $errors[] = t('invalid_email');
    } elseif (getUserByEmail($email)) {
        $errors[] = t('email_already_exists');
    }
    
    if (empty($password)) {
        $errors[] = t('password') . ' ' . t('required');
    } elseif (!isStrongPassword($password)) {
        $errors[] = t('password_requirements');
    }
    
    if ($password !== $confirmPassword) {
        $errors[] = t('passwords_not_match');
    }
    
    if (empty($firstName)) {
        $errors[] = t('first_name') . ' ' . t('required');
    }
    
    if (empty($lastName)) {
        $errors[] = t('last_name') . ' ' . t('required');
    }
    
    if (!$agreeTerms) {
        $errors[] = t('must_agree_terms');
    }
    
    // Проверка реферального кода
    $referrerId = null;
    if (!empty($referralCode)) {
        $db = getDB();
        $stmt = $db->prepare("SELECT id FROM users WHERE referral_code = ?");
        $stmt->execute([$referralCode]);
        $referrer = $stmt->fetch();
        
        if (!$referrer) {
            $errors[] = t('invalid_referral_code');
        } else {
            $referrerId = $referrer['id'];
        }
    }
    
    // Если нет ошибок, создаем пользователя
    if (empty($errors)) {
        $userData = [
            'email' => $email,
            'password' => $password,
            'first_name' => $firstName,
            'last_name' => $lastName,
            'phone' => $phone,
            'referred_by' => $referrerId
        ];
        
        if (createUser($userData)) {
            // Получаем созданного пользователя
            $user = getUserByEmail($email);
            
            // Добавляем стартовый бонус
            addTransaction($user['id'], 'bonus', STARTER_BONUS, t('welcome_bonus'), 'completed');
            
            // Создаем реферальные связи
            if ($referrerId) {
                createReferralRelations($user['id'], $referrerId);
            }
            
            // Логируем регистрацию
            logAction($user['id'], 'user_registered', 'New user registration');
            
            // Авторизуем пользователя
            $_SESSION['user_id'] = $user['id'];
            $_SESSION['user_email'] = $user['email'];
            
            setFlashMessage('success', t('registration_successful'));
            redirect('dashboard.php');
        } else {
            $errors[] = t('registration_failed');
        }
    }
}

// Функция для создания реферальных связей
function createReferralRelations($userId, $referrerId) {
    $db = getDB();
    
    // Создаем прямую реферальную связь
    $stmt = $db->prepare("
        INSERT INTO referrals (referrer_id, referred_id, level, commission_rate) 
        VALUES (?, ?, 1, ?)
    ");
    $stmt->execute([$referrerId, $userId, REFERRAL_COMMISSION[0]]);
    
    // Создаем многоуровневые реферальные связи
    $currentReferrer = $referrerId;
    for ($level = 2; $level <= REFERRAL_LEVELS; $level++) {
        $stmt = $db->prepare("SELECT referred_by FROM users WHERE id = ?");
        $stmt->execute([$currentReferrer]);
        $nextReferrer = $stmt->fetchColumn();
        
        if ($nextReferrer) {
            $stmt = $db->prepare("
                INSERT INTO referrals (referrer_id, referred_id, level, commission_rate) 
                VALUES (?, ?, ?, ?)
            ");
            $stmt->execute([$nextReferrer, $userId, $level, REFERRAL_COMMISSION[$level - 1]]);
            $currentReferrer = $nextReferrer;
        } else {
            break;
        }
    }
}
?>
<?php
$pageTitle = t('register') . ' - Astragenix';
$pageDescription = t('register_description');
$currentPage = 'register';
$bodyClass = 'auth-page';
$additionalCSS = ['assets/css/auth.css'];
include 'includes/header.php';
?>
    <!-- Auth Section -->
    <section class="auth-section">
        <div class="container">
            <div class="auth-container">
                <div class="auth-background">
                    <div class="stars"></div>
                    <div class="floating-elements">
                        <div class="floating-element"></div>
                        <div class="floating-element"></div>
                        <div class="floating-element"></div>
                    </div>
                </div>

                <div class="auth-content">
                    <div class="auth-card animate-scale-in">
                        <div class="auth-header">
                            <div class="auth-logo">
                                <img src="assets/images/logo.png" alt="Astragenix" class="logo-img">
                                <span class="logo-text">Astragenix</span>
                            </div>
                            <h1 class="auth-title"><?php echo t('create_account'); ?></h1>
                            <p class="auth-subtitle"><?php echo t('join_thousands_investors'); ?></p>
                        </div>
                
                <?php if (!empty($errors)): ?>
                    <div class="alert alert-error animate-shake">
                        <i class="fas fa-exclamation-triangle"></i>
                        <ul>
                            <?php foreach ($errors as $error): ?>
                                <li><?php echo h($error); ?></li>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                <?php endif; ?>
                
                <form method="POST" class="auth-form" data-validate>
                    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="first_name"><?php echo t('first_name'); ?></label>
                            <input type="text" id="first_name" name="first_name" 
                                   value="<?php echo h($_POST['first_name'] ?? ''); ?>" 
                                   required>
                            <i class="fas fa-user form-icon"></i>
                        </div>
                        
                        <div class="form-group">
                            <label for="last_name"><?php echo t('last_name'); ?></label>
                            <input type="text" id="last_name" name="last_name" 
                                   value="<?php echo h($_POST['last_name'] ?? ''); ?>" 
                                   required>
                            <i class="fas fa-user form-icon"></i>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="email"><?php echo t('email'); ?></label>
                        <input type="email" id="email" name="email" 
                               value="<?php echo h($_POST['email'] ?? ''); ?>" 
                               required>
                        <i class="fas fa-envelope form-icon"></i>
                    </div>
                    
                    <div class="form-group">
                        <label for="phone"><?php echo t('phone'); ?></label>
                        <input type="tel" id="phone" name="phone" 
                               value="<?php echo h($_POST['phone'] ?? ''); ?>">
                        <i class="fas fa-phone form-icon"></i>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="password"><?php echo t('password'); ?></label>
                            <input type="password" id="password" name="password" required>
                            <i class="fas fa-lock form-icon"></i>
                            <button type="button" class="password-toggle" onclick="togglePassword('password')">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                        
                        <div class="form-group">
                            <label for="confirm_password"><?php echo t('confirm_password'); ?></label>
                            <input type="password" id="confirm_password" name="confirm_password" required>
                            <i class="fas fa-lock form-icon"></i>
                            <button type="button" class="password-toggle" onclick="togglePassword('confirm_password')">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="referral_code"><?php echo t('referral_code'); ?></label>
                        <input type="text" id="referral_code" name="referral_code" 
                               value="<?php echo h($_POST['referral_code'] ?? $_GET['ref'] ?? ''); ?>" 
                               placeholder="<?php echo t('optional'); ?>">
                        <i class="fas fa-users form-icon"></i>
                    </div>
                    
                    <div class="form-group checkbox-group">
                        <label class="checkbox-label">
                            <input type="checkbox" name="agree_terms" required>
                            <span class="checkmark"></span>
                            <?php echo t('agree_terms'); ?>
                        </label>
                    </div>
                    
                    <button type="submit" class="btn btn-primary btn-large btn-full animate-pulse">
                        <i class="fas fa-rocket"></i>
                        <?php echo t('create_account'); ?>
                    </button>
                </form>
                
                <div class="auth-footer">
                    <p><?php echo t('already_have_account'); ?> 
                       <a href="login.php" class="auth-link"><?php echo t('sign_in'); ?></a>
                    </p>
                </div>
                
                <div class="bonus-info">
                    <div class="bonus-card">
                        <i class="fas fa-gift"></i>
                        <div class="bonus-text">
                            <h3><?php echo t('welcome_bonus'); ?></h3>
                            <p><?php echo formatCurrency(STARTER_BONUS); ?> <?php echo t('free_on_registration'); ?></p>
                        </div>
                    </div>
                </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    
<?php include 'includes/footer.php'; ?>
