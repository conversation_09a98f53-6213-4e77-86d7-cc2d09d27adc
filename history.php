<?php
session_start();
require_once 'config/database.php';
require_once 'includes/functions.php';
require_once 'includes/language.php';

// Определение языка
$lang = detectLanguage();
loadLanguage($lang);

// Проверка авторизации
if (!isset($_SESSION['user_id'])) {
    redirect('login.php');
}

$user = getUserById($_SESSION['user_id']);
if (!$user) {
    session_destroy();
    redirect('login.php');
}

// Параметры фильтрации
$filter_type = $_GET['type'] ?? 'all';
$filter_status = $_GET['status'] ?? 'all';
$page = max(1, intval($_GET['page'] ?? 1));
$per_page = 20;
$offset = ($page - 1) * $per_page;

$db = getDB();

// Построение WHERE условий
$where_conditions = ['user_id = ?'];
$params = [$_SESSION['user_id']];

if ($filter_type !== 'all') {
    $where_conditions[] = 'type = ?';
    $params[] = $filter_type;
}

if ($filter_status !== 'all') {
    $where_conditions[] = 'status = ?';
    $params[] = $filter_status;
}

$where_clause = implode(' AND ', $where_conditions);

// Получение общего количества записей
$count_stmt = $db->prepare("SELECT COUNT(*) FROM transactions WHERE {$where_clause}");
$count_stmt->execute($params);
$total_records = $count_stmt->fetchColumn();
$total_pages = ceil($total_records / $per_page);

// Получение транзакций
$stmt = $db->prepare("
    SELECT * FROM transactions 
    WHERE {$where_clause}
    ORDER BY created_at DESC 
    LIMIT ? OFFSET ?
");
$params[] = $per_page;
$params[] = $offset;
$stmt->execute($params);
$transactions = $stmt->fetchAll();

// Получение инвестиций
$investments_stmt = $db->prepare("
    SELECT * FROM investments 
    WHERE user_id = ? 
    ORDER BY created_at DESC 
    LIMIT 10
");
$investments_stmt->execute([$_SESSION['user_id']]);
$investments = $investments_stmt->fetchAll();

// Функция для получения иконки транзакции
function getTransactionIcon($type) {
    $icons = [
        'deposit' => 'arrow-down',
        'withdrawal' => 'arrow-up',
        'profit' => 'chart-line',
        'bonus' => 'gift',
        'referral' => 'users',
        'investment' => 'chart-line'
    ];
    return $icons[$type] ?? 'exchange-alt';
}

// Функция для получения цвета транзакции
function getTransactionColor($type, $amount) {
    if ($amount > 0) {
        return 'success';
    } elseif ($amount < 0) {
        return 'danger';
    }
    return 'primary';
}

$pageTitle = t('transaction_history') . ' - Astragenix';
$pageDescription = t('history_description');
$currentPage = 'history';
$bodyClass = 'history-page';
$additionalCSS = ['assets/css/history.css'];
include 'includes/header.php';
?>

<!-- History Section -->
<section class="history-section">
    <div class="container">
        <!-- Page Header -->
        <div class="page-header">
            <div class="page-header-content">
                <h1 class="page-title"><?php echo t('transaction_history'); ?></h1>
                <p class="page-subtitle"><?php echo t('history_description'); ?></p>
                <div class="breadcrumb">
                    <a href="dashboard.php"><?php echo t('dashboard'); ?></a>
                    <span class="breadcrumb-separator">/</span>
                    <span class="breadcrumb-current"><?php echo t('history'); ?></span>
                </div>
            </div>
        </div>

        <div class="history-content">
            <!-- Filters -->
            <div class="filters-section">
                <div class="filters-card">
                    <h3><?php echo t('filter_transactions'); ?></h3>
                    <form method="GET" class="filters-form">
                        <div class="filter-group">
                            <label for="type"><?php echo t('transaction_type'); ?></label>
                            <select id="type" name="type" class="filter-select">
                                <option value="all" <?php echo $filter_type === 'all' ? 'selected' : ''; ?>>
                                    <?php echo t('all_types'); ?>
                                </option>
                                <option value="deposit" <?php echo $filter_type === 'deposit' ? 'selected' : ''; ?>>
                                    <?php echo t('deposit'); ?>
                                </option>
                                <option value="withdrawal" <?php echo $filter_type === 'withdrawal' ? 'selected' : ''; ?>>
                                    <?php echo t('withdrawal'); ?>
                                </option>
                                <option value="profit" <?php echo $filter_type === 'profit' ? 'selected' : ''; ?>>
                                    <?php echo t('profit'); ?>
                                </option>
                                <option value="bonus" <?php echo $filter_type === 'bonus' ? 'selected' : ''; ?>>
                                    <?php echo t('bonus'); ?>
                                </option>
                                <option value="referral" <?php echo $filter_type === 'referral' ? 'selected' : ''; ?>>
                                    <?php echo t('referral'); ?>
                                </option>
                                <option value="investment" <?php echo $filter_type === 'investment' ? 'selected' : ''; ?>>
                                    <?php echo t('investment'); ?>
                                </option>
                            </select>
                        </div>
                        
                        <div class="filter-group">
                            <label for="status"><?php echo t('status'); ?></label>
                            <select id="status" name="status" class="filter-select">
                                <option value="all" <?php echo $filter_status === 'all' ? 'selected' : ''; ?>>
                                    <?php echo t('all_statuses'); ?>
                                </option>
                                <option value="pending" <?php echo $filter_status === 'pending' ? 'selected' : ''; ?>>
                                    <?php echo t('pending'); ?>
                                </option>
                                <option value="completed" <?php echo $filter_status === 'completed' ? 'selected' : ''; ?>>
                                    <?php echo t('completed'); ?>
                                </option>
                                <option value="failed" <?php echo $filter_status === 'failed' ? 'selected' : ''; ?>>
                                    <?php echo t('failed'); ?>
                                </option>
                                <option value="processing" <?php echo $filter_status === 'processing' ? 'selected' : ''; ?>>
                                    <?php echo t('processing'); ?>
                                </option>
                            </select>
                        </div>
                        
                        <div class="filter-actions">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-filter"></i>
                                <?php echo t('apply_filters'); ?>
                            </button>
                            <a href="history.php" class="btn btn-secondary">
                                <i class="fas fa-times"></i>
                                <?php echo t('clear_filters'); ?>
                            </a>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Transactions Table -->
            <div class="transactions-section">
                <div class="transactions-card">
                    <div class="card-header">
                        <h3><?php echo t('transactions'); ?></h3>
                        <div class="header-info">
                            <?php echo t('showing'); ?> <?php echo $offset + 1; ?>-<?php echo min($offset + $per_page, $total_records); ?> 
                            <?php echo t('of'); ?> <?php echo $total_records; ?> <?php echo t('results'); ?>
                        </div>
                    </div>
                    
                    <?php if (!empty($transactions)): ?>
                        <div class="transactions-table">
                            <div class="table-header">
                                <div class="header-cell"><?php echo t('type'); ?></div>
                                <div class="header-cell"><?php echo t('amount'); ?></div>
                                <div class="header-cell"><?php echo t('status'); ?></div>
                                <div class="header-cell"><?php echo t('date'); ?></div>
                                <div class="header-cell"><?php echo t('description'); ?></div>
                            </div>
                            
                            <div class="table-body">
                                <?php foreach ($transactions as $transaction): ?>
                                    <div class="table-row">
                                        <div class="table-cell type-cell">
                                            <div class="transaction-type">
                                                <div class="type-icon <?php echo getTransactionColor($transaction['type'], $transaction['amount']); ?>">
                                                    <i class="fas fa-<?php echo getTransactionIcon($transaction['type']); ?>"></i>
                                                </div>
                                                <span class="type-name"><?php echo t($transaction['type']); ?></span>
                                            </div>
                                        </div>
                                        
                                        <div class="table-cell amount-cell">
                                            <span class="amount <?php echo getTransactionColor($transaction['type'], $transaction['amount']); ?>">
                                                <?php echo $transaction['amount'] >= 0 ? '+' : ''; ?>
                                                <?php echo formatCurrency($transaction['amount']); ?>
                                            </span>
                                        </div>
                                        
                                        <div class="table-cell status-cell">
                                            <span class="status <?php echo $transaction['status']; ?>">
                                                <?php echo t($transaction['status']); ?>
                                            </span>
                                        </div>
                                        
                                        <div class="table-cell date-cell">
                                            <div class="transaction-date">
                                                <span class="date"><?php echo date('M j, Y', strtotime($transaction['created_at'])); ?></span>
                                                <span class="time"><?php echo date('H:i', strtotime($transaction['created_at'])); ?></span>
                                            </div>
                                        </div>
                                        
                                        <div class="table-cell description-cell">
                                            <span class="description"><?php echo h($transaction['description']); ?></span>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                        
                        <!-- Pagination -->
                        <?php if ($total_pages > 1): ?>
                            <div class="pagination">
                                <?php if ($page > 1): ?>
                                    <a href="?page=<?php echo $page - 1; ?>&type=<?php echo $filter_type; ?>&status=<?php echo $filter_status; ?>" 
                                       class="pagination-btn">
                                        <i class="fas fa-chevron-left"></i>
                                        <?php echo t('previous'); ?>
                                    </a>
                                <?php endif; ?>
                                
                                <div class="pagination-info">
                                    <?php echo t('page'); ?> <?php echo $page; ?> <?php echo t('of'); ?> <?php echo $total_pages; ?>
                                </div>
                                
                                <?php if ($page < $total_pages): ?>
                                    <a href="?page=<?php echo $page + 1; ?>&type=<?php echo $filter_type; ?>&status=<?php echo $filter_status; ?>" 
                                       class="pagination-btn">
                                        <?php echo t('next'); ?>
                                        <i class="fas fa-chevron-right"></i>
                                    </a>
                                <?php endif; ?>
                            </div>
                        <?php endif; ?>
                    <?php else: ?>
                        <div class="empty-state">
                            <div class="empty-icon">
                                <i class="fas fa-receipt"></i>
                            </div>
                            <h3><?php echo t('no_transactions_found'); ?></h3>
                            <p><?php echo t('no_transactions_message'); ?></p>
                            <a href="invest.php" class="btn btn-primary">
                                <i class="fas fa-plus"></i>
                                <?php echo t('make_investment'); ?>
                            </a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Recent Investments -->
            <?php if (!empty($investments)): ?>
                <div class="investments-section">
                    <div class="investments-card">
                        <div class="card-header">
                            <h3><?php echo t('recent_investments'); ?></h3>
                            <a href="invest.php" class="header-action">
                                <?php echo t('new_investment'); ?>
                                <i class="fas fa-arrow-right"></i>
                            </a>
                        </div>
                        
                        <div class="investments-grid">
                            <?php foreach (array_slice($investments, 0, 6) as $investment): ?>
                                <div class="investment-item">
                                    <div class="investment-header">
                                        <h4><?php echo ucfirst($investment['plan_id']); ?> <?php echo t('plan'); ?></h4>
                                        <span class="investment-status <?php echo $investment['status']; ?>">
                                            <?php echo t($investment['status']); ?>
                                        </span>
                                    </div>
                                    
                                    <div class="investment-details">
                                        <div class="detail-item">
                                            <span class="detail-label"><?php echo t('amount'); ?>:</span>
                                            <span class="detail-value"><?php echo formatCurrency($investment['amount']); ?></span>
                                        </div>
                                        
                                        <div class="detail-item">
                                            <span class="detail-label"><?php echo t('daily_rate'); ?>:</span>
                                            <span class="detail-value"><?php echo $investment['daily_rate']; ?>%</span>
                                        </div>
                                        
                                        <div class="detail-item">
                                            <span class="detail-label"><?php echo t('duration'); ?>:</span>
                                            <span class="detail-value"><?php echo $investment['duration_days']; ?> <?php echo t('days'); ?></span>
                                        </div>
                                        
                                        <div class="detail-item">
                                            <span class="detail-label"><?php echo t('start_date'); ?>:</span>
                                            <span class="detail-value"><?php echo date('M j, Y', strtotime($investment['start_date'])); ?></span>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>
</section>

<?php include 'includes/footer.php'; ?>
