<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Astragenix - Created Images</title>
    <style>
        body { font-family: Arial, sans-serif; background: #0f172a; color: white; padding: 20px; }
        .image-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin: 20px 0; }
        .image-item { background: #1e293b; padding: 20px; border-radius: 10px; text-align: center; border: 1px solid #334155; }
        .image-item img { max-width: 100%; height: auto; border-radius: 5px; }
        .image-item h3 { margin: 10px 0 5px; color: #6366f1; }
        .image-item p { margin: 5px 0; color: #cbd5e1; font-size: 14px; }
    </style>
</head>
<body>
    <h1>Astragenix - Created Images</h1>
    <p>Below are the images that were automatically created for your project:</p>
    
    <div class="image-grid">
        <div class="image-item">
            <h3>Logo</h3>
            <img src="assets/images/logo.png" alt="Astragenix Logo">
            <p>64x64 PNG - Main logo for the platform</p>
        </div>
        
        <div class="image-item">
            <h3>Default Avatar</h3>
            <img src="assets/images/default-avatar.png" alt="Default Avatar">
            <p>100x100 PNG - Default user avatar</p>
        </div>
        
        <div class="image-item">
            <h3>Default News</h3>
            <img src="assets/images/default-news.jpg" alt="Default News">
            <p>300x200 JPG - Default news article image</p>
        </div>
        
        <div class="image-item">
            <h3>Favicon</h3>
            <img src="assets/images/favicon.png" alt="Favicon" style="width: 32px; height: 32px;">
            <p>32x32 PNG - Browser favicon</p>
        </div>
    </div>
    
    <h2>Usage Instructions:</h2>
    <ul>
        <li>Copy the logo.png to use as the main site logo</li>
        <li>Use default-avatar.png for users without profile pictures</li>
        <li>Use default-news.jpg for news articles without images</li>
        <li>Add favicon.png to your site root or reference it in HTML head</li>
    </ul>
    
    <p><strong>Note:</strong> These are basic generated images. For production use, consider creating professional graphics or hiring a designer.</p>
</body>
</html>