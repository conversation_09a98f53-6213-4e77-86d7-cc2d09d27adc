/* Astragenix Forms Styles */

/* Auth Page Layout */
.auth-page {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-lg);
}

.auth-container {
    position: relative;
    width: 100%;
    max-width: 500px;
    margin: 0 auto;
}

.auth-background {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(ellipse at center, rgba(99, 102, 241, 0.1) 0%, transparent 70%);
    z-index: -1;
}

.auth-content {
    position: relative;
    z-index: 2;
}

.auth-card {
    background: rgba(30, 41, 59, 0.95);
    backdrop-filter: blur(20px);
    border: 1px solid var(--dark-border);
    border-radius: var(--radius-xl);
    padding: var(--spacing-2xl);
    box-shadow: var(--shadow-xl);
}

.auth-header {
    text-align: center;
    margin-bottom: var(--spacing-2xl);
}

.auth-logo {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-lg);
}

.auth-logo .logo-img {
    width: 40px;
    height: 40px;
}

.auth-logo .logo-text {
    font-size: 1.75rem;
    font-weight: 700;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.auth-title {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: var(--spacing-sm);
    color: var(--text-primary);
}

.auth-subtitle {
    color: var(--text-secondary);
    font-size: 1rem;
}

/* Form Styles */
.auth-form {
    margin-bottom: var(--spacing-lg);
}

.form-group {
    position: relative;
    margin-bottom: var(--spacing-lg);
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-md);
}

.form-group label {
    display: block;
    margin-bottom: var(--spacing-sm);
    font-weight: 500;
    color: var(--text-primary);
    font-size: 0.875rem;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: var(--spacing-md) var(--spacing-lg);
    padding-left: 3rem;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid var(--dark-border);
    border-radius: var(--radius-md);
    color: var(--text-primary);
    font-size: 1rem;
    transition: var(--transition-fast);
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    background: rgba(255, 255, 255, 0.08);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.form-group input.error,
.form-group select.error,
.form-group textarea.error {
    border-color: var(--error-color);
    background: rgba(239, 68, 68, 0.1);
}

.form-icon {
    position: absolute;
    left: var(--spacing-md);
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-muted);
    font-size: 1rem;
    pointer-events: none;
    margin-top: 12px; /* Adjust for label */
}

.password-toggle {
    position: absolute;
    right: var(--spacing-md);
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: var(--text-muted);
    cursor: pointer;
    padding: var(--spacing-sm);
    margin-top: 12px; /* Adjust for label */
    transition: var(--transition-fast);
}

.password-toggle:hover {
    color: var(--text-primary);
}

/* Checkbox Styles */
.checkbox-group {
    margin-bottom: var(--spacing-xl);
}

.checkbox-label {
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-sm);
    cursor: pointer;
    font-size: 0.875rem;
    line-height: 1.5;
    color: var(--text-secondary);
}

.checkbox-label input[type="checkbox"] {
    display: none;
}

.checkmark {
    width: 20px;
    height: 20px;
    background: rgba(255, 255, 255, 0.05);
    border: 2px solid var(--dark-border);
    border-radius: var(--radius-sm);
    position: relative;
    flex-shrink: 0;
    transition: var(--transition-fast);
}

.checkbox-label input[type="checkbox"]:checked + .checkmark {
    background: var(--gradient-primary);
    border-color: var(--primary-color);
}

.checkbox-label input[type="checkbox"]:checked + .checkmark::after {
    content: '';
    position: absolute;
    left: 6px;
    top: 2px;
    width: 6px;
    height: 10px;
    border: solid white;
    border-width: 0 2px 2px 0;
    transform: rotate(45deg);
}

/* Button Styles */
.btn-full {
    width: 100%;
    justify-content: center;
}

.btn-large {
    padding: var(--spacing-md) var(--spacing-xl);
    font-size: 1rem;
    font-weight: 600;
}

/* Auth Footer */
.auth-footer {
    text-align: center;
    padding-top: var(--spacing-lg);
    border-top: 1px solid var(--dark-border);
    color: var(--text-secondary);
}

.auth-link {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 500;
    transition: var(--transition-fast);
}

.auth-link:hover {
    color: var(--primary-dark);
    text-decoration: underline;
}

/* Bonus Info */
.bonus-info {
    margin-top: var(--spacing-lg);
}

.bonus-card {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-lg);
    background: rgba(16, 185, 129, 0.1);
    border: 1px solid rgba(16, 185, 129, 0.3);
    border-radius: var(--radius-lg);
    animation: glow 2s ease-in-out infinite alternate;
}

.bonus-card i {
    font-size: 2rem;
    color: var(--success-color);
}

.bonus-text h3 {
    font-size: 1rem;
    font-weight: 600;
    color: var(--success-color);
    margin-bottom: var(--spacing-xs);
}

.bonus-text p {
    font-size: 0.875rem;
    color: var(--text-secondary);
    margin: 0;
}

/* Alert Styles */
.alert {
    padding: var(--spacing-md);
    border-radius: var(--radius-md);
    margin-bottom: var(--spacing-lg);
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-sm);
}

.alert-error {
    background: rgba(239, 68, 68, 0.1);
    border: 1px solid rgba(239, 68, 68, 0.3);
    color: #fca5a5;
}

.alert-success {
    background: rgba(16, 185, 129, 0.1);
    border: 1px solid rgba(16, 185, 129, 0.3);
    color: #6ee7b7;
}

.alert-warning {
    background: rgba(245, 158, 11, 0.1);
    border: 1px solid rgba(245, 158, 11, 0.3);
    color: #fcd34d;
}

.alert-info {
    background: rgba(99, 102, 241, 0.1);
    border: 1px solid rgba(99, 102, 241, 0.3);
    color: #c7d2fe;
}

.alert i {
    font-size: 1.25rem;
    margin-top: 2px;
}

.alert ul {
    list-style: none;
    margin: 0;
    padding: 0;
}

.alert li {
    margin-bottom: var(--spacing-xs);
}

.alert li:last-child {
    margin-bottom: 0;
}

/* Error Messages */
.error-message {
    color: var(--error-color);
    font-size: 0.875rem;
    margin-top: var(--spacing-xs);
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.error-message::before {
    content: '\f071';
    font-family: 'Font Awesome 6 Free';
    font-weight: 900;
    font-size: 0.75rem;
}

/* File Upload */
.file-upload {
    position: relative;
    display: inline-block;
    width: 100%;
}

.file-upload input[type="file"] {
    position: absolute;
    opacity: 0;
    width: 100%;
    height: 100%;
    cursor: pointer;
}

.file-upload-label {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-lg);
    border: 2px dashed var(--dark-border);
    border-radius: var(--radius-md);
    background: rgba(255, 255, 255, 0.02);
    color: var(--text-secondary);
    cursor: pointer;
    transition: var(--transition-fast);
}

.file-upload-label:hover {
    border-color: var(--primary-color);
    background: rgba(99, 102, 241, 0.05);
    color: var(--text-primary);
}

.file-upload-label i {
    font-size: 1.5rem;
}

/* Select Dropdown */
.form-group select {
    appearance: none;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 0.75rem center;
    background-repeat: no-repeat;
    background-size: 1.5em 1.5em;
    padding-right: 2.5rem;
}

/* Textarea */
.form-group textarea {
    min-height: 120px;
    resize: vertical;
    padding-left: var(--spacing-lg);
}

/* Form Validation States */
.form-group.valid input,
.form-group.valid select,
.form-group.valid textarea {
    border-color: var(--success-color);
    background: rgba(16, 185, 129, 0.05);
}

.form-group.valid .form-icon {
    color: var(--success-color);
}

/* Loading State */
.btn.loading {
    position: relative;
    color: transparent;
    pointer-events: none;
}

.btn.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 20px;
    height: 20px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-top: 2px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* Responsive Design */
@media (max-width: 768px) {
    .auth-page {
        padding: var(--spacing-md);
    }
    
    .auth-card {
        padding: var(--spacing-lg);
    }
    
    .form-row {
        grid-template-columns: 1fr;
    }
    
    .auth-title {
        font-size: 1.5rem;
    }
    
    .bonus-card {
        flex-direction: column;
        text-align: center;
    }
}

@media (max-width: 480px) {
    .auth-card {
        padding: var(--spacing-md);
    }
    
    .form-group input,
    .form-group select,
    .form-group textarea {
        padding: var(--spacing-sm) var(--spacing-md);
        padding-left: 2.5rem;
        font-size: 0.875rem;
    }
    
    .form-icon {
        left: var(--spacing-sm);
        font-size: 0.875rem;
    }
    
    .password-toggle {
        right: var(--spacing-sm);
    }
}
