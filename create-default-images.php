<?php
// Скрипт для создания базовых изображений для проекта Astragenix

// Проверяем, поддерживает ли сервер GD
if (!extension_loaded('gd')) {
    die('GD extension is not loaded. Please install php-gd extension.');
}

// Создаем папку для изображений, если её нет
$imagesDir = 'assets/images/';
if (!is_dir($imagesDir)) {
    mkdir($imagesDir, 0755, true);
}

// Функция для создания логотипа
function createLogo($size = 64) {
    $image = imagecreatetruecolor($size, $size);
    
    // Включаем альфа-канал
    imagealphablending($image, false);
    imagesavealpha($image, true);
    
    // Прозрачный фон
    $transparent = imagecolorallocatealpha($image, 0, 0, 0, 127);
    imagefill($image, 0, 0, $transparent);
    
    // Цвета градиента
    $color1 = imagecolorallocate($image, 99, 102, 241);   // #6366f1
    $color2 = imagecolorallocate($image, 139, 92, 246);   // #8b5cf6
    $white = imagecolorallocate($image, 255, 255, 255);
    
    // Рисуем круг с градиентом (упрощенная версия)
    $center = $size / 2;
    $radius = $size / 2 - 2;
    
    // Создаем градиентный эффект
    for ($i = 0; $i < $radius; $i++) {
        $ratio = $i / $radius;
        $r = 99 + ($ratio * (139 - 99));
        $g = 102 + ($ratio * (92 - 102));
        $b = 241 + ($ratio * (246 - 241));
        
        $color = imagecolorallocate($image, $r, $g, $b);
        imagefilledellipse($image, $center, $center, ($radius - $i) * 2, ($radius - $i) * 2, $color);
    }
    
    // Рисуем звезду в центре
    $starSize = $size * 0.4;
    $starPoints = [
        $center, $center - $starSize/2,  // верх
        $center + $starSize/4, $center - $starSize/6,  // правый верх
        $center + $starSize/2, $center - $starSize/6,  // правый
        $center + $starSize/6, $center + $starSize/6,  // правый низ
        $center + $starSize/3, $center + $starSize/2,  // низ правый
        $center, $center + $starSize/4,  // низ
        $center - $starSize/3, $center + $starSize/2,  // низ левый
        $center - $starSize/6, $center + $starSize/6,  // левый низ
        $center - $starSize/2, $center - $starSize/6,  // левый
        $center - $starSize/4, $center - $starSize/6   // левый верх
    ];
    
    imagefilledpolygon($image, $starPoints, 10, $white);
    
    return $image;
}

// Функция для создания аватара по умолчанию
function createDefaultAvatar($size = 100) {
    $image = imagecreatetruecolor($size, $size);
    
    // Цвета
    $bg = imagecolorallocate($image, 30, 41, 59);      // #1e293b
    $border = imagecolorallocate($image, 99, 102, 241); // #6366f1
    $icon = imagecolorallocate($image, 203, 213, 225);  // #cbd5e1
    
    // Фон
    imagefill($image, 0, 0, $bg);
    
    // Рамка
    $center = $size / 2;
    imageellipse($image, $center, $center, $size - 4, $size - 4, $border);
    
    // Иконка пользователя (упрощенная)
    $headRadius = $size * 0.15;
    $bodyWidth = $size * 0.4;
    $bodyHeight = $size * 0.3;
    
    // Голова
    imagefilledellipse($image, $center, $center - $size * 0.1, $headRadius * 2, $headRadius * 2, $icon);
    
    // Тело
    imagefilledrectangle(
        $image,
        $center - $bodyWidth/2,
        $center + $size * 0.1,
        $center + $bodyWidth/2,
        $center + $size * 0.1 + $bodyHeight,
        $icon
    );
    
    return $image;
}

// Функция для создания изображения новости по умолчанию
function createDefaultNews($width = 300, $height = 200) {
    $image = imagecreatetruecolor($width, $height);
    
    // Цвета
    $bg = imagecolorallocate($image, 30, 41, 59);      // #1e293b
    $accent = imagecolorallocate($image, 99, 102, 241); // #6366f1
    $text = imagecolorallocate($image, 203, 213, 225);  // #cbd5e1
    
    // Фон
    imagefill($image, 0, 0, $bg);
    
    // Декоративные элементы
    for ($i = 0; $i < 20; $i++) {
        $x = rand(0, $width);
        $y = rand(0, $height);
        $size = rand(2, 8);
        $alpha = rand(20, 60);
        $color = imagecolorallocatealpha($image, 99, 102, 241, $alpha);
        imagefilledellipse($image, $x, $y, $size, $size, $color);
    }
    
    // Центральная иконка
    $centerX = $width / 2;
    $centerY = $height / 2;
    $iconSize = min($width, $height) * 0.3;
    
    imagefilledrectangle(
        $image,
        $centerX - $iconSize/2,
        $centerY - $iconSize/2,
        $centerX + $iconSize/2,
        $centerY + $iconSize/2,
        $accent
    );
    
    return $image;
}

try {
    // Создаем логотип
    echo "Creating logo...\n";
    $logo = createLogo(64);
    imagepng($logo, $imagesDir . 'logo.png');
    imagedestroy($logo);
    
    // Создаем аватар по умолчанию
    echo "Creating default avatar...\n";
    $avatar = createDefaultAvatar(100);
    imagepng($avatar, $imagesDir . 'default-avatar.png');
    imagedestroy($avatar);
    
    // Создаем изображение новости по умолчанию
    echo "Creating default news image...\n";
    $news = createDefaultNews(300, 200);
    imagepng($news, $imagesDir . 'default-news.jpg');
    imagedestroy($news);
    
    // Создаем favicon
    echo "Creating favicon...\n";
    $favicon = createLogo(32);
    imagepng($favicon, $imagesDir . 'favicon.png');
    imagedestroy($favicon);
    
    echo "All images created successfully!\n";
    echo "Files created:\n";
    echo "- assets/images/logo.png (64x64)\n";
    echo "- assets/images/default-avatar.png (100x100)\n";
    echo "- assets/images/default-news.jpg (300x200)\n";
    echo "- assets/images/favicon.png (32x32)\n";
    
} catch (Exception $e) {
    echo "Error creating images: " . $e->getMessage() . "\n";
}

// Создаем простой HTML файл для просмотра созданных изображений
$htmlContent = '<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Astragenix - Created Images</title>
    <style>
        body { font-family: Arial, sans-serif; background: #0f172a; color: white; padding: 20px; }
        .image-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin: 20px 0; }
        .image-item { background: #1e293b; padding: 20px; border-radius: 10px; text-align: center; border: 1px solid #334155; }
        .image-item img { max-width: 100%; height: auto; border-radius: 5px; }
        .image-item h3 { margin: 10px 0 5px; color: #6366f1; }
        .image-item p { margin: 5px 0; color: #cbd5e1; font-size: 14px; }
    </style>
</head>
<body>
    <h1>Astragenix - Created Images</h1>
    <p>Below are the images that were automatically created for your project:</p>
    
    <div class="image-grid">
        <div class="image-item">
            <h3>Logo</h3>
            <img src="assets/images/logo.png" alt="Astragenix Logo">
            <p>64x64 PNG - Main logo for the platform</p>
        </div>
        
        <div class="image-item">
            <h3>Default Avatar</h3>
            <img src="assets/images/default-avatar.png" alt="Default Avatar">
            <p>100x100 PNG - Default user avatar</p>
        </div>
        
        <div class="image-item">
            <h3>Default News</h3>
            <img src="assets/images/default-news.jpg" alt="Default News">
            <p>300x200 JPG - Default news article image</p>
        </div>
        
        <div class="image-item">
            <h3>Favicon</h3>
            <img src="assets/images/favicon.png" alt="Favicon" style="width: 32px; height: 32px;">
            <p>32x32 PNG - Browser favicon</p>
        </div>
    </div>
    
    <h2>Usage Instructions:</h2>
    <ul>
        <li>Copy the logo.png to use as the main site logo</li>
        <li>Use default-avatar.png for users without profile pictures</li>
        <li>Use default-news.jpg for news articles without images</li>
        <li>Add favicon.png to your site root or reference it in HTML head</li>
    </ul>
    
    <p><strong>Note:</strong> These are basic generated images. For production use, consider creating professional graphics or hiring a designer.</p>
</body>
</html>';

file_put_contents('created-images.html', $htmlContent);
echo "\nView created images at: created-images.html\n";
?>
