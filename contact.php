<?php
session_start();
require_once 'config/database.php';
require_once 'includes/functions.php';
require_once 'includes/language.php';

// Определение языка
$lang = detectLanguage();
loadLanguage($lang);

// Проверка авторизации
$isLoggedIn = isset($_SESSION['user_id']);
$user = null;
if ($isLoggedIn) {
    $user = getUserById($_SESSION['user_id']);
}

// Настройки страницы
$pageTitle = 'Astragenix - ' . t('contact_us');
$pageDescription = t('contact_description');
$currentPage = 'contact';
$bodyClass = 'contact-page';

// Обработка формы
$message = '';
$messageType = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $name = trim($_POST['name'] ?? '');
    $email = trim($_POST['email'] ?? '');
    $subject = trim($_POST['subject'] ?? '');
    $messageText = trim($_POST['message'] ?? '');
    
    if (empty($name) || empty($email) || empty($subject) || empty($messageText)) {
        $message = t('all_fields_required');
        $messageType = 'error';
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $message = t('invalid_email');
        $messageType = 'error';
    } else {
        // Здесь можно добавить отправку email или сохранение в базу данных
        $message = t('message_sent_successfully');
        $messageType = 'success';
    }
}

// Подключение заголовка
include 'includes/header.php';
?>

    <!-- Page Header -->
    <section class="page-header">
        <div class="container">
            <div class="page-header-content">
                <h1 class="page-title scroll-reveal"><?php echo t('contact_us'); ?></h1>
                <p class="page-subtitle scroll-reveal"><?php echo t('contact_subtitle'); ?></p>
                <nav class="breadcrumb scroll-reveal">
                    <a href="index.php"><?php echo t('home'); ?></a>
                    <span class="breadcrumb-separator">/</span>
                    <span class="breadcrumb-current"><?php echo t('contact'); ?></span>
                </nav>
            </div>
        </div>
    </section>

    <!-- Contact Section -->
    <section class="contact-section">
        <div class="container">
            <div class="contact-content">
                <!-- Contact Information -->
                <div class="contact-info scroll-reveal-left">
                    <h2 class="section-title"><?php echo t('get_in_touch'); ?></h2>
                    <p class="contact-description"><?php echo t('contact_description_detailed'); ?></p>
                    
                    <div class="contact-methods">
                        <div class="contact-method">
                            <div class="method-icon">
                                <i class="fas fa-envelope"></i>
                            </div>
                            <div class="method-content">
                                <h4><?php echo t('email_us'); ?></h4>
                                <p><EMAIL></p>
                                <p><EMAIL></p>
                            </div>
                        </div>
                        
                        <div class="contact-method">
                            <div class="method-icon">
                                <i class="fas fa-phone"></i>
                            </div>
                            <div class="method-content">
                                <h4><?php echo t('call_us'); ?></h4>
                                <p>+****************</p>
                                <p><?php echo t('24_7_support'); ?></p>
                            </div>
                        </div>
                        
                        <div class="contact-method">
                            <div class="method-icon">
                                <i class="fab fa-telegram"></i>
                            </div>
                            <div class="method-content">
                                <h4><?php echo t('telegram'); ?></h4>
                                <p>@AstragenixSupport</p>
                                <p><?php echo t('instant_support'); ?></p>
                            </div>
                        </div>
                        
                        <div class="contact-method">
                            <div class="method-icon">
                                <i class="fas fa-clock"></i>
                            </div>
                            <div class="method-content">
                                <h4><?php echo t('business_hours'); ?></h4>
                                <p><?php echo t('24_7_available'); ?></p>
                                <p><?php echo t('all_time_zones'); ?></p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="social-contact">
                        <h4><?php echo t('follow_us'); ?></h4>
                        <div class="social-links">
                            <a href="#" class="social-link">
                                <i class="fab fa-telegram"></i>
                                <span>Telegram</span>
                            </a>
                            <a href="#" class="social-link">
                                <i class="fab fa-twitter"></i>
                                <span>Twitter</span>
                            </a>
                            <a href="#" class="social-link">
                                <i class="fab fa-discord"></i>
                                <span>Discord</span>
                            </a>
                            <a href="#" class="social-link">
                                <i class="fab fa-youtube"></i>
                                <span>YouTube</span>
                            </a>
                        </div>
                    </div>
                </div>
                
                <!-- Contact Form -->
                <div class="contact-form-container scroll-reveal-right">
                    <div class="contact-form-card">
                        <h3 class="form-title"><?php echo t('send_message'); ?></h3>
                        
                        <?php if ($message): ?>
                            <div class="alert alert-<?php echo $messageType; ?>">
                                <?php echo h($message); ?>
                            </div>
                        <?php endif; ?>
                        
                        <form class="contact-form" method="POST" action="">
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="name" class="form-label"><?php echo t('full_name'); ?></label>
                                    <input type="text" id="name" name="name" class="form-input" 
                                           placeholder="<?php echo t('enter_name'); ?>" 
                                           value="<?php echo h($_POST['name'] ?? ''); ?>" required>
                                </div>
                                
                                <div class="form-group">
                                    <label for="email" class="form-label"><?php echo t('email_address'); ?></label>
                                    <input type="email" id="email" name="email" class="form-input" 
                                           placeholder="<?php echo t('enter_email'); ?>" 
                                           value="<?php echo h($_POST['email'] ?? ''); ?>" required>
                                </div>
                            </div>
                            
                            <div class="form-group">
                                <label for="subject" class="form-label"><?php echo t('subject'); ?></label>
                                <select id="subject" name="subject" class="form-input form-select" required>
                                    <option value=""><?php echo t('select_subject'); ?></option>
                                    <option value="general"><?php echo t('general_inquiry'); ?></option>
                                    <option value="support"><?php echo t('technical_support'); ?></option>
                                    <option value="investment"><?php echo t('investment_question'); ?></option>
                                    <option value="partnership"><?php echo t('partnership'); ?></option>
                                    <option value="complaint"><?php echo t('complaint'); ?></option>
                                </select>
                            </div>
                            
                            <div class="form-group">
                                <label for="message" class="form-label"><?php echo t('message'); ?></label>
                                <textarea id="message" name="message" class="form-input form-textarea" 
                                          placeholder="<?php echo t('enter_message'); ?>" 
                                          rows="6" required><?php echo h($_POST['message'] ?? ''); ?></textarea>
                            </div>
                            
                            <div class="form-group">
                                <label class="checkbox-label">
                                    <input type="checkbox" class="form-checkbox" required>
                                    <span class="checkbox-text">
                                        <?php echo t('agree_to'); ?> 
                                        <a href="privacy.php"><?php echo t('privacy_policy'); ?></a>
                                    </span>
                                </label>
                            </div>
                            
                            <button type="submit" class="btn btn-primary btn-full">
                                <i class="fas fa-paper-plane"></i>
                                <span><?php echo t('send_message'); ?></span>
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- FAQ Section -->
    <section class="faq-preview-section">
        <div class="container">
            <div class="section-header text-center">
                <h2 class="section-title scroll-reveal"><?php echo t('frequently_asked'); ?></h2>
                <p class="section-subtitle scroll-reveal"><?php echo t('faq_subtitle'); ?></p>
            </div>
            
            <div class="faq-grid">
                <div class="faq-item scroll-reveal stagger-item">
                    <h4 class="faq-question"><?php echo t('how_to_start_investing'); ?></h4>
                    <p class="faq-answer"><?php echo t('start_investing_answer'); ?></p>
                </div>
                
                <div class="faq-item scroll-reveal stagger-item">
                    <h4 class="faq-question"><?php echo t('minimum_investment'); ?></h4>
                    <p class="faq-answer"><?php echo t('minimum_investment_answer'); ?></p>
                </div>
                
                <div class="faq-item scroll-reveal stagger-item">
                    <h4 class="faq-question"><?php echo t('withdrawal_time'); ?></h4>
                    <p class="faq-answer"><?php echo t('withdrawal_time_answer'); ?></p>
                </div>
                
                <div class="faq-item scroll-reveal stagger-item">
                    <h4 class="faq-question"><?php echo t('platform_security'); ?></h4>
                    <p class="faq-answer"><?php echo t('platform_security_answer'); ?></p>
                </div>
            </div>
            
            <div class="faq-cta text-center scroll-reveal">
                <a href="faq.php" class="btn btn-outline">
                    <i class="fas fa-question-circle"></i>
                    <span><?php echo t('view_all_faq'); ?></span>
                </a>
            </div>
        </div>
    </section>

    <!-- Support Features -->
    <section class="support-features-section">
        <div class="container">
            <div class="features-grid">
                <div class="support-feature scroll-reveal stagger-item">
                    <div class="feature-icon">
                        <i class="fas fa-headset"></i>
                    </div>
                    <h4 class="feature-title"><?php echo t('24_7_support'); ?></h4>
                    <p class="feature-description"><?php echo t('round_clock_support'); ?></p>
                </div>
                
                <div class="support-feature scroll-reveal stagger-item">
                    <div class="feature-icon">
                        <i class="fas fa-language"></i>
                    </div>
                    <h4 class="feature-title"><?php echo t('multilingual'); ?></h4>
                    <p class="feature-description"><?php echo t('multiple_languages'); ?></p>
                </div>
                
                <div class="support-feature scroll-reveal stagger-item">
                    <div class="feature-icon">
                        <i class="fas fa-bolt"></i>
                    </div>
                    <h4 class="feature-title"><?php echo t('fast_response'); ?></h4>
                    <p class="feature-description"><?php echo t('quick_response_time'); ?></p>
                </div>
                
                <div class="support-feature scroll-reveal stagger-item">
                    <div class="feature-icon">
                        <i class="fas fa-user-tie"></i>
                    </div>
                    <h4 class="feature-title"><?php echo t('expert_team'); ?></h4>
                    <p class="feature-description"><?php echo t('professional_support'); ?></p>
                </div>
            </div>
        </div>
    </section>

<?php
// Подключение футера
include 'includes/footer.php';
?>
